# KAUST B2B Marketplace - Technical Architecture Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Technology Stack](#technology-stack)
4. [Core Features](#core-features)
5. [B2B Workflow Architecture](#b2b-workflow-architecture)
6. [Order States Workflow](#order-states-workflow)
7. [Component Architecture](#component-architecture)
8. [State Management](#state-management)
9. [API Integration](#api-integration)
10. [Authentication Architecture](#authentication-architecture)
11. [Internationalization](#internationalization)
12. [UI/UX Design System](#uiux-design-system)
13. [Home Page Layout](#home-page-layout)
14. [Data Flow](#data-flow)
15. [Security Considerations](#security-considerations)
16. [Performance Optimizations](#performance-optimizations)
17. [Deployment Architecture](#deployment-architecture)
18. [Development Workflow](#development-workflow)
19. [Future Enhancements](#future-enhancements)

---

## Project Overview

The KAUST B2B Marketplace is a modern shop, enterprise-grade customer shop portal built for King Abdullah University of Science and Technology (KAUST). It serves as a comprehensive e-commerce platform designed specifically for B2B transactions, featuring multi-order management, advanced product catalog, and seamless integration capabilities with ERP systems like Odoo without payment process .

for branding ux color use : https://www.kaust.edu.sa/en/

### Key Objectives

- **Streamlined B2B Operations**: Simplify procurement processes for institutional buyers
- **Multi-Order Management**: Enable users to manage multiple orders simultaneously
- **Scalable Architecture**: Built to handle enterprise-level traffic and data
- **ERP Integration Ready**: Designed for seamless integration with Odoo 18 and other ERP systems
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Internationalization**: Full support for English and Arabic languages with RTL layout

### Core B2B Concept

The KAUST B2B Marketplace implements a unified concept where **Order = Quotation = Cart**. This approach aligns with B2B procurement workflows where:

- **Cart**: User's working collection of products (draft state)
- **Quotation**: Formal request for pricing and terms (submitted state)
- **Order**: Confirmed purchase after approval (final ordered state)

This unified model enables seamless transitions through the B2B procurement lifecycle while maintaining familiar e-commerce terminology for users.

---

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js 15 Frontend]
        B[React Components]
        C[Redux Store]
        D[Context Providers]
    end
  
    subgraph "API Layer"
        E[Mock API Layer]
        F[Odoo Integration]
        G[HTTP Controllers]
    end
  
    subgraph "Data Layer"
        H[Mock Data Store]
        I[Local Storage]
        J[Session Storage]
    end
  
    subgraph "External Systems"
        K[Odoo ERP]
        L[Payment Gateway]
        M[Email Service]
    end
  
    A --> E
    E --> F
    F --> K
    C --> I
    C --> J
    B --> C
    D --> B
  
    style A fill:#e1f5fe
    style K fill:#fff3e0
    style C fill:#f3e5f5
```

### Application Architecture Layers

1. **Presentation Layer**: Next.js 15 with React 19 components
2. **State Management Layer**: Redux Toolkit with persistent storage
3. **Business Logic Layer**: Custom hooks and utility functions
4. **API Abstraction Layer**: Mock API with Odoo integration points
5. **Data Persistence Layer**: Browser storage with future database integration

---

## Technology Stack

### Frontend Technologies

- **Framework**: Next.js 15.2.4 (App Router)
- **UI Library**: React 19.0.0
- **Language**: TypeScript 5.x
- **Styling**: Tailwind CSS 3.4.0 + ShadCN UI components
- **State Management**: Redux Toolkit 2.6.1 + React Redux 9.2.0
- **Internationalization**: Custom i18n implementation with React Context
- **Icons**: Lucide React 0.363.0
- **HTTP Client**: Axios 1.6.7

### Development Tools

- **Package Manager**: Bun (preferred) / npm / yarn
- **Linting**: ESLint 9 with Next.js config
- **Build Tool**: Next.js with Turbopack
- **Type Checking**: TypeScript with strict mode
- **CSS Processing**: PostCSS 8.5.3 + Autoprefixer

### External Integrations

- **ERP System**: Odoo 18 (via HTTP controllers)
- **Image Hosting**: Unsplash, Placehold.co
- **User Avatars**: RandomUser.me API

---

## Core Features

### 1. Multi-Order Management System

```mermaid
graph LR
    A[User] --> B[Create Order]
    B --> C[Add Products]
    C --> D[Manage Quantities]
    D --> E[Submit for Approval]
    E --> F[Track Status]
    F --> G[Order Confirmation]
  
    B --> H[Switch Between Orders]
    H --> C
  
    style B fill:#e8f5e8
    style E fill:#fff3e0
    style G fill:#e1f5fe
```

**Key Capabilities:**

- Create and manage multiple orders simultaneously
- Switch between draft, submitted, and confirmed orders
- Real-time order status tracking
- Order item management with quantity updates
- Target price setting for negotiation

### 2. Advanced Product Catalog

- **Product Categories**: Laptops, Phones, Electronics with subcategories
- **Product Variants**: Color, storage, specifications
- **Advanced Filtering**: Category, brand, price range, specifications
- **Search Functionality**: Real-time search with autocomplete
- **Product Details**: Comprehensive product information with image gallery

### 3. Wishlist Management

- Add/remove products from wishlist
- Wishlist notifications and counters
- Quick add to order from wishlist
- Persistent wishlist across sessions

### 4. Interactive Image Previewer

- Thumbnail navigation (vertical/horizontal)
- Zoom functionality with magnifying glass
- Touch support for mobile devices
- Keyboard accessibility (ESC to close)
- Loading states and error handling

---

## B2B Workflow Architecture

### Core B2B Principles

The KAUST B2B Marketplace is built on enterprise procurement principles where traditional e-commerce "cart" functionality is enhanced with B2B-specific workflows:

#### 1. Multi-Quotation Management

Unlike traditional e-commerce single cart systems, B2B users need to manage multiple procurement requests simultaneously:

- **Project-Based Quotations**: Separate quotations for different projects or departments
- **Budget Cycle Management**: Different quotations for different budget periods
- **Approval Workflows**: Draft quotations vs. submitted quotations
- **Negotiation Tracking**: Original quotation vs. revised versions

#### 2. Procurement Lifecycle Integration

The system integrates with enterprise procurement workflows:

```mermaid
graph LR
    A[User Need] --> B[Product Research]
    B --> C[Create Quotation]
    C --> D[Add Products]
    D --> E[Submit for Approval]
    E --> F[Vendor Review]
    F --> G[Negotiation]
    G --> H[Final Approval]
    H --> I[Order Conversion]
    I --> J[Fulfillment]

    style A fill:#f9f9f9
    style C fill:#e8f5e8
    style E fill:#fff3e0
    style H fill:#e1f5fe
    style I fill:#f3e5f5
```

#### 3. B2B User Roles and Permissions

The system supports multiple user roles within customer organizations:

- **Purchasing Representative**: Create and manage quotations
- **Department Manager**: Approve quotations within budget limits
- **Finance Approver**: Approve high-value quotations
- **End User/Requester**: Submit purchase requests
- **Company Administrator**: Manage users and company settings

#### 4. Contract-Based Pricing and Access

B2B operations are governed by frame contracts that define:

- **Product Access**: Which products are available to specific customers
- **Pricing Tiers**: Contract-specific pricing and volume discounts
- **SLA Commitments**: Delivery times and service level agreements
- **Terms and Conditions**: Payment terms and contract compliance

### B2B vs E-commerce Differences

| Aspect                     | Traditional E-commerce | B2B Marketplace                   |
| -------------------------- | ---------------------- | --------------------------------- |
| **Cart Concept**     | Single shopping cart   | Multiple named quotations         |
| **Pricing**          | Fixed catalog prices   | Contract-based dynamic pricing    |
| **User Model**       | Individual consumers   | Organization with multiple users  |
| **Purchase Process** | Immediate checkout     | Quotation → Approval → Order    |
| **Product Access**   | Public catalog         | Contract-based product visibility |
| **Payment**          | Credit card/PayPal     | Purchase orders and invoicing     |
| **Relationships**    | Transactional          | Long-term contract relationships  |

---

## Order States Workflow

### B2B Quotation State Machine

The KAUST B2B Marketplace implements a comprehensive state machine for managing the quotation-to-order lifecycle. This workflow is specifically designed for B2B procurement processes and aligns with enterprise purchasing requirements.

#### Core Workflow States

```mermaid
stateDiagram-v2
    [*] --> draft
    draft --> submitted : Customer submits quotation
    submitted --> under_review : Auto-transition to vendor review
    under_review --> revised : Vendor requests changes
    revised --> under_review : Customer resubmits after revision
    under_review --> approved : Vendor accepts terms
    under_review --> rejected : Vendor declines quotation
    approved --> converted_to_order : Customer confirms order
    approved --> expired : Validity period expires
    rejected --> [*]
    expired --> [*]
    converted_to_order --> [*]

    note right of draft : User building quotation
    note right of submitted : Sent to vendor for review
    note right of under_review : Vendor evaluating terms
    note right of revised : Updated due to negotiation
    note right of approved : Agreement reached
    note right of converted_to_order : Final order created
```

#### State Definitions and Business Logic

| State                  | Internal ID      | Description                 | User Actions                         | System Actions                 |
| ---------------------- | ---------------- | --------------------------- | ------------------------------------ | ------------------------------ |
| **Draft**        | `draft`        | User is building quotation  | Add/remove items, edit quantities    | Auto-save, validation          |
| **Submitted**    | `submitted`    | Sent to vendor for review   | View status, cancel if needed        | Notify vendor, lock editing    |
| **Under Review** | `under_review` | Vendor evaluating quotation | Monitor progress, respond to queries | Track SLA, send updates        |
| **Revised**      | `revised`      | Updated due to negotiation  | Review changes, accept/modify        | Version tracking, audit trail  |
| **Approved**     | `approved`     | Vendor accepted terms       | Convert to order, negotiate further  | Set expiry date, prepare order |
| **Rejected**     | `rejected`     | Vendor declined quotation   | View reason, create new quotation    | Archive, send notification     |
| **Expired**      | `expired`      | Validity period lapsed      | Request extension, create new        | Auto-expire, cleanup           |
| **Converted**    | `order`        | Final order created         | Track order, manage delivery         | Order processing, fulfillment  |

#### Workflow Scenarios

##### Scenario 1: Successful Quotation Flow

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant Vendor
    participant System

    Customer->>Portal: Create Draft Quotation
    Portal->>System: Save quotation (draft)
    Customer->>Portal: Submit Quotation
    Portal->>System: Change state to submitted
    System->>Vendor: Notify for review
    Vendor->>System: Accept quotation
    System->>Portal: State → approved
    Customer->>Portal: Convert to order
    Portal->>System: State → order
    System->>Customer: Order confirmation
```

##### Scenario 2: Negotiation Flow

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant Vendor
    participant System

    Customer->>Portal: Submit quotation
    Portal->>System: State → submitted
    System->>Vendor: Review request
    Vendor->>System: Request revisions
    System->>Portal: State → revised
    Portal->>Customer: Revision notification
    Customer->>Portal: Update quotation
    Portal->>System: Resubmit (under_review)
    Vendor->>System: Approve revised terms
    System->>Portal: State → approved
```

#### State Transition Rules

##### Allowed Transitions

- `draft` → `submitted`: Customer submits complete quotation
- `submitted` → `under_review`: Automatic transition for vendor review
- `under_review` → `approved`: Vendor accepts all terms
- `under_review` → `revised`: Vendor requests modifications
- `under_review` → `rejected`: Vendor declines quotation
- `revised` → `under_review`: Customer resubmits after changes
- `approved` → `order`: Customer confirms final order
- `approved` → `expired`: System auto-expires after validity period

##### Business Rules

1. **Draft State**: Unlimited editing, auto-save every 30 seconds
2. **Submitted State**: No editing allowed, 24-hour cancellation window
3. **Under Review**: SLA tracking, vendor response time monitoring
4. **Revised State**: Version control, change tracking, approval required
5. **Approved State**: 30-day validity period, conversion deadline
6. **Expired State**: Archive after 90 days, reactivation possible
7. **Converted State**: Immutable, order tracking begins

#### Multi-Quotation Management

##### Quotation Context Switching

Users can maintain multiple active quotations simultaneously:

- **Active Quotation**: Currently selected quotation for product additions
- **Quotation List**: All user quotations with status indicators
- **Quick Switch**: Dropdown selector in header for rapid context switching
- **Quotation Naming**: User-defined names for organization (e.g., "Q1 2024 Laptops", "Emergency IT Equipment")

##### Quotation Organization

- **Project-Based**: Group quotations by project or department
- **Status Filtering**: Filter by draft, submitted, approved, etc.
- **Date Ranges**: Filter by creation date, submission date, or validity
- **Search**: Full-text search across quotation names and contents

---

## Component Architecture

### Component Hierarchy

```mermaid
graph TD
    A[App Layout] --> B[Portal Layout]
    B --> C[Header]
    B --> D[Main Content]
    B --> E[Footer]
  
    C --> F[Search Autocomplete]
    C --> G[Order Selector]
    C --> H[Theme Toggle]
    C --> I[Language Toggle]
    C --> J[Wishlist Notification]
  
    D --> K[Product Pages]
    D --> L[Order Pages]
    D --> M[Account Pages]
  
    K --> N[Product Card]
    K --> O[Product Detail]
    K --> P[Filters Sidebar]
    K --> Q[Image Previewer]
  
    L --> R[Order Detail]
    L --> S[Order Chatter]
  
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

### Key Components

#### 1. Layout Components

- **`PortalLayoutContent`**: Main layout wrapper with header, navigation, and footer
- **`Header`**: Fixed header with search, navigation, and user controls
- **`Footer`**: Simple footer with copyright and links

#### 2. Product Components

- **`ProductCard`**: Grid item displaying product information
- **`ProductDetail`**: Detailed product view with variants and actions
- **`ImagePreviewer`**: Interactive image gallery with zoom functionality
- **`FiltersSidebar`**: Advanced filtering interface

#### 3. Order Management Components

- **`OrderSelector`**: Dropdown for switching between orders
- **`OrderDetail`**: Comprehensive order management interface
- **`OrderChatter`**: Communication system for order discussions

#### 4. Utility Components

- **`SearchAutocomplete`**: Real-time search with suggestions
- **`ThemeToggle`**: Light/dark mode switcher
- **`LanguageToggle`**: English/Arabic language switcher
- **`Alert`**: Custom alert system for notifications

---

## State Management

### Redux Store Structure

```mermaid
graph TB
    A[Redux Store] --> B[Products Slice]
    A --> C[Orders Slice]
    A --> E[Wishlist Slice]
  
    B --> F[Products Array]
    B --> G[Filtered Products]
    B --> H[Filter State]
    B --> I[Loading State]
  
    C --> J[Orders Array]
    C --> K[Active Order ID]
  
  
  
    E --> N[Wishlist Items]
  
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
 
    style E fill:#fce4ec
```

### State Slices

#### 1. Products Slice (`src/lib/redux/slices/products.ts`)

```typescript
interface ProductsState {
  products: Product[];
  filteredProducts: Product[];
  filters: FilterState;
  loading: boolean;
  error: string | null;
}
```

**Actions:**

- `getProducts`: Async thunk for fetching products
- `setSearchQuery`: Update search filter
- `setCategoryFilters`: Update category filters
- `setBrandFilters`: Update brand filters
- `setPriceRange`: Update price range filter
- `clearFilters`: Reset all filters

#### 2. Orders Slice (`src/lib/redux/slices/orders.ts`)

```typescript
interface OrdersState {
  orders: Order[];
  activeOrderId: string | null;
}
```

**Actions:**

- `createOrder`: Create new order
- `setActiveOrder`: Switch active order
- `addItemToOrder`: Add product to order
- `updateItemQuantity`: Update item quantity
- `removeItemFromOrder`: Remove item from order
- `submitOrder`: Submit order for approval
- `confirmOrder`: Confirm submitted order

#### 3. Wishlist Slice (`src/lib/redux/slices/wishlist.ts`)

```typescript
interface WishlistState {
  items: Product[];
}
```

**Actions:**

- `addToWishlist`: Add product to wishlist
- `removeFromWishlist`: Remove product from wishlist
- `clearWishlist`: Clear all wishlist items

### Context Providers

#### 1. Theme Context (`src/contexts/ThemeContext.tsx`)

- Manages light/dark theme state
- Persists theme preference in localStorage
- Applies theme classes to document

#### 2. Language Context (`src/contexts/LanguageContext.tsx`)

- Manages English/Arabic language state
- Provides translation function
- Handles RTL/LTR text direction
- Persists language preference

---

## API Integration

### Mock API Layer

The application uses a mock API layer that simulates real backend interactions while providing a foundation for future ERP integration.

#### API Structure (`src/lib/api/odoo.ts`)

```typescript
// Core API Functions
export const fetchProducts = async (): Promise<Product[]>
export const fetchProductById = async (productId: number): Promise<Product | null>
export const searchProducts = async (query: string): Promise<Product[]>
export const confirmBasket = async (basket: Basket): Promise<OdooResponse>
export const getOrderHistory = async (): Promise<OdooResponse>
```

#### Mock Data Generation (`src/lib/api/mockData.ts`)

The mock data system generates realistic product data for:

- **Laptops**: Business, Gaming, Ultrabook, Convertible, Workstation
- **Phones**: Smartphone, Feature Phone, Rugged Phone, Foldable
- **Electronics**: Various categories with specifications

### Odoo Integration Points

The application is designed for seamless integration with Odoo 18:

```python
# Example Odoo HTTP Controller
from odoo import http
from odoo.http import request
import json

class MarketplaceController(http.Controller):
  
    @http.route('/api/products', type='json', auth='user', methods=['POST'])
    def get_products(self, **kwargs):
        products = request.env['product.template'].search([])
        return {
            'success': True,
            'data': products.read(['name', 'list_price', 'description'])
        }
  
    @http.route('/api/orders/create', type='json', auth='user', methods=['POST'])
    def create_order(self, **kwargs):
        order_data = kwargs.get('order_data')
        order = request.env['sale.order'].create(order_data)
        return {
            'success': True,
            'data': {'order_id': order.id}
        }
```

---

## Authentication Architecture

### Two-Layer Authentication System

The KAUST B2B Marketplace implements a sophisticated two-layer authentication system designed for enterprise security requirements and Saudi Arabia's digital identity standards.

#### Layer 1: Nafath Identity Verification

**Nafath Integration** provides government-backed identity verification:

```mermaid
sequenceDiagram
    participant User
    participant Portal
    participant Nafath
    participant NafathAPI
    participant Government

    User->>Portal: Access B2B Portal
    Portal->>Nafath: Redirect to Nafath Login
    Nafath->>User: Request National ID
    User->>Nafath: Enter National ID + Biometrics
    Nafath->>Government: Verify Identity
    Government->>Nafath: Identity Confirmed
    Nafath->>Portal: Return Verified Identity Token
    Portal->>User: Proceed to Portal Authentication
```

**Nafath Features:**

- **National ID Verification**: Government database validation
- **Multi-Factor Security**: SMS OTP + biometric verification
- **Session Management**: Secure session tokens with expiration
- **Audit Trail**: Complete authentication logging

#### Layer 2: B2B Portal Authentication

**Portal Authentication** links verified identity to B2B customer context:

```mermaid
sequenceDiagram
    participant User
    participant Portal
    participant OdooAPI
    participant CustomerDB
    participant SessionStore

    User->>Portal: Nafath-verified identity
    Portal->>OdooAPI: Validate user in B2B system
    OdooAPI->>CustomerDB: Check customer association
    CustomerDB->>OdooAPI: Return customer context
    OdooAPI->>SessionStore: Create B2B session
    SessionStore->>Portal: Return session token
    Portal->>User: Grant B2B portal access
```

**Portal Features:**

- **Customer Context**: Link to B2B customer organization
- **Role-Based Access**: Department and permission-based access
- **Multi-User Support**: Multiple users per customer organization
- **Session Persistence**: Secure session management across devices
- **API Token Management**: JWT tokens for API authentication

### User Management Architecture

#### Customer Portal User Management System

The KAUST B2B Marketplace implements a comprehensive user management system that handles user profiles, approval workflows, and integration with Odoo backend administration.

##### User Profile Management

**Core User Information Structure:**

```mermaid
graph TB
    subgraph "User Profile Data"
        A[National ID - Primary Key]
        B[Personal Information]
        C[Contact Information]
        D[Organization Context]
        E[Role & Permissions]
    end

    subgraph "Personal Information"
        F[Full Name Arabic/English]
        G[Date of Birth]
        H[Gender]
        I[Nationality]
    end

    subgraph "Contact Information"
        J[Email Address]
        K[Mobile Phone]
        L[Office Phone]
        M[Department Address]
    end

    subgraph "Organization Context"
        N[Customer Organization]
        O[Department]
        P[Job Title]
        Q[Manager/Supervisor]
        R[Budget Authority]
    end

    A --> F
    B --> F
    B --> G
    B --> H
    B --> I
    C --> J
    C --> K
    C --> L
    C --> M
    D --> N
    D --> O
    D --> P
    D --> Q
    D --> R

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

**User Data Model:**

```typescript
interface B2BUser {
  // Primary Identity
  nationalId: string;           // Saudi National ID (Primary Key)
  nafathVerified: boolean;      // Government verification status

  // Personal Information
  fullNameArabic: string;       // Full name in Arabic
  fullNameEnglish: string;      // Full name in English
  dateOfBirth: Date;           // Date of birth
  gender: 'male' | 'female';   // Gender
  nationality: string;         // Nationality

  // Contact Information
  email: string;               // Primary email address
  mobilePhone: string;         // Mobile phone number
  officePhone?: string;        // Office phone number
  departmentAddress?: string;   // Department physical address

  // Organization Context
  customerId: string;          // B2B customer organization ID
  department: string;          // Department/division
  jobTitle: string;           // Job title/position
  managerId?: string;         // Direct manager/supervisor
  budgetAuthority: number;    // Maximum spending authority

  // System Information
  roles: UserRole[];          // Assigned roles and permissions
  status: UserStatus;         // Account status
  createdAt: Date;           // Account creation date
  lastLoginAt?: Date;        // Last login timestamp
  approvedBy?: string;       // Admin who approved account
  approvedAt?: Date;         // Approval timestamp
}
```

#### User Approval Workflow with Odoo Backend

**Multi-Stage Approval Process:**

```mermaid
sequenceDiagram
    participant User as New User
    participant Portal as Customer Portal
    participant Admin as Company Admin
    participant OdooAPI as Odoo Backend
    participant Vendor as Vendor Admin

    User->>Portal: Request Account Access
    Portal->>Portal: Nafath ID Verification
    Portal->>OdooAPI: Create Pending User Record
    OdooAPI->>Admin: Notify Company Admin

    Admin->>Portal: Review User Request
    Admin->>Portal: Approve/Reject User
    Portal->>OdooAPI: Update User Status

    alt User Approved
        OdooAPI->>Portal: Activate User Account
        Portal->>User: Send Welcome Email
        OdooAPI->>Vendor: Notify New User Added
    else User Rejected
        OdooAPI->>Portal: Mark as Rejected
        Portal->>User: Send Rejection Notice
    end
```

**Approval Workflow States:**

| State                        | Description                     | Actions Available               | Next States                   |
| ---------------------------- | ------------------------------- | ------------------------------- | ----------------------------- |
| **Pending**            | User submitted request          | Admin review, provide documents | Approved, Rejected, More Info |
| **More Info Required** | Additional documentation needed | User upload documents           | Pending, Rejected             |
| **Approved**           | Admin approved access           | User can access portal          | Active, Suspended             |
| **Rejected**           | Admin rejected request          | User can appeal or reapply      | Pending (reapplication)       |
| **Active**             | Full portal access              | Normal operations               | Suspended, Deactivated        |
| **Suspended**          | Temporary access restriction    | Admin can reactivate            | Active, Deactivated           |
| **Deactivated**        | Account permanently disabled    | Admin archive                   | -                             |

#### B2B User Roles and Permissions

```mermaid
graph TB
    subgraph "KAUST Organization"
        A[Company Administrator]
        B[Department Manager]
        C[Purchasing Representative]
        D[Finance Approver]
        E[End User/Requester]
    end

    subgraph "Permissions Matrix"
        F[Create Users]
        G[Approve Quotations]
        H[Create Quotations]
        I[Submit Orders]
        J[View Analytics]
        K[Manage Contracts]
    end

    A --> F
    A --> J
    A --> K
    B --> G
    B --> I
    B --> J
    C --> H
    C --> I
    D --> G
    E --> H

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

#### User Lifecycle Management

**User Onboarding Process:**

1. **Invitation**: Company admin invites user with role assignment
2. **Nafath Verification**: User completes government ID verification
3. **Portal Registration**: User creates portal account linked to customer
4. **Role Assignment**: Admin assigns specific roles and permissions
5. **Training**: Guided tour and feature introduction
6. **Activation**: Full access granted with monitoring period

**Permission Management:**

- **Resource-Level**: Control access to quotations, orders, analytics
- **Department-Based**: Restrict access to specific departments
- **Budget Limits**: Spending limits based on user role
- **Approval Workflows**: Automatic escalation for high-value quotations
- **Time-Based**: Temporary permissions for specific periods

### Security Features

#### Multi-Factor Authentication

- **Nafath Verification**: Government-grade verification verification
- **SMS Verification**: Secondary SMS-based verification
- **Device Registration**: Trusted device management
- **Session Monitoring**: Real-time session activity tracking
- **Anomaly Detection**: Unusual access pattern detection

#### Session Security

- **JWT Tokens**: Stateless authentication for API calls
- **Session Rotation**: Regular token refresh and rotation
- **Device Binding**: Sessions tied to specific devices
- **Concurrent Session Limits**: Maximum active sessions per user
- **Automatic Logout**: Idle session timeout and cleanup

#### Data Protection

- **Encryption at Rest**: All sensitive data encrypted in storage
- **Encryption in Transit**: TLS 1.3 for all communications
- **API Security**: Rate limiting and request validation
- **Audit Logging**: Complete user activity tracking
- **Privacy Controls**: GDPR-compliant data handling

---

## Notification and Messaging System

### Real-Time Communication Architecture

The KAUST B2B Marketplace implements a comprehensive notification and messaging system to facilitate communication between customers, vendors, and system administrators throughout the procurement lifecycle.

#### Notification Types and Channels

```mermaid
graph TB
    subgraph "Notification Sources"
        A[Quotation Updates]
        B[Order Status Changes]
        C[User Management]
        D[System Alerts]
        E[Contract Events]
    end

    subgraph "Notification Channels"
        F[In-App Notifications]
        G[Email Notifications]
        H[SMS Alerts]
        I[Push Notifications]
        J[Dashboard Alerts]
    end

    subgraph "User Preferences"
        K[Notification Settings]
        L[Channel Preferences]
        M[Frequency Controls]
        N[Priority Filters]
    end

    A --> F
    A --> G
    B --> F
    B --> G
    B --> H
    C --> G
    C --> H
    D --> F
    D --> J
    E --> F
    E --> G

    F --> K
    G --> L
    H --> M
    I --> N

    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style K fill:#fff3e0
```

#### In-App Notification System

**Notification Center Design:**

```mermaid
sequenceDiagram
    participant User
    participant Portal
    participant NotificationAPI
    participant OdooBackend
    participant EmailService

    OdooBackend->>NotificationAPI: Quotation Status Changed
    NotificationAPI->>Portal: Real-time Update
    Portal->>User: Show Notification Badge

    User->>Portal: Click Notification Icon
    Portal->>NotificationAPI: Get User Notifications
    NotificationAPI->>Portal: Return Notification List
    Portal->>User: Display Notification Center

    User->>Portal: Click Notification Item
    Portal->>User: Navigate to Related Content
    Portal->>NotificationAPI: Mark as Read
```

**Notification Categories:**

| Category                     | Priority | Channels           | Retention |
| ---------------------------- | -------- | ------------------ | --------- |
| **Quotation Updates**  | High     | In-App, Email      | 30 days   |
| **Order Status**       | High     | In-App, Email, SMS | 60 days   |
| **User Approval**      | Critical | Email, SMS         | 90 days   |
| **System Maintenance** | Medium   | In-App, Email      | 7 days    |
| **Contract Expiry**    | Critical | All Channels       | 180 days  |
| **Payment Reminders**  | High     | Email, SMS         | 30 days   |

#### Messaging System Architecture

**Order Communication (Chatter System):**

```mermaid
graph LR
    subgraph "Customer Side"
        A[Customer User]
        B[Order Detail Page]
        C[Message Thread]
    end

    subgraph "Vendor Side"
        D[Vendor User]
        E[Odoo Backend]
        F[Order Management]
    end

    subgraph "Communication Channel"
        G[Message API]
        H[Real-time Updates]
        I[Email Notifications]
    end

    A --> B
    B --> C
    C --> G
    G --> H
    G --> I
    H --> F
    F --> E
    E --> D

    style A fill:#e1f5fe
    style D fill:#e8f5e8
    style G fill:#fff3e0
```

**Message Types and Features:**

1. **Order Discussion Messages**

   - Text messages with rich formatting
   - File attachments (documents, images)
   - @mentions for specific users
   - Message threading and replies
   - Read receipts and status indicators
2. **System Generated Messages**

   - Automatic status updates
   - Delivery confirmations
   - Payment notifications
   - SLA breach alerts
3. **Vendor Communications**

   - Price negotiations
   - Product clarifications
   - Delivery updates
   - Technical support

#### Notification Preferences Management

**User Notification Settings:**

```typescript
interface NotificationPreferences {
  userId: string;

  // Channel Preferences
  channels: {
    inApp: boolean;
    email: boolean;
    sms: boolean;
    push: boolean;
  };

  // Category Settings
  categories: {
    quotationUpdates: NotificationSetting;
    orderStatus: NotificationSetting;
    userManagement: NotificationSetting;
    systemAlerts: NotificationSetting;
    contractEvents: NotificationSetting;
  };

  // Frequency Controls
  frequency: {
    immediate: boolean;
    daily: boolean;
    weekly: boolean;
  };

  // Quiet Hours
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
}

interface NotificationSetting {
  enabled: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  channels: string[];
}
```

#### Real-Time Messaging Implementation

**WebSocket Integration:**

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant WebSocket
    participant MessageAPI
    participant OdooBackend

    Customer->>Portal: Login to Portal
    Portal->>WebSocket: Establish Connection
    WebSocket->>MessageAPI: Subscribe to User Channel

    OdooBackend->>MessageAPI: New Message Event
    MessageAPI->>WebSocket: Broadcast to User
    WebSocket->>Portal: Real-time Update
    Portal->>Customer: Show New Message

    Customer->>Portal: Send Reply
    Portal->>MessageAPI: Post Message
    MessageAPI->>OdooBackend: Store Message
    MessageAPI->>WebSocket: Broadcast Update
```

**Message Threading System:**

- **Parent-Child Relationships**: Messages can be replies to other messages
- **Thread Visualization**: Indented display for conversation flow
- **Context Preservation**: Maintain conversation context across sessions
- **Search and Filter**: Find messages by content, date, or participant

#### Integration with Odoo Backend

**Odoo Chatter Integration:**

```python
# Odoo Backend Integration Example
class B2BQuotation(models.Model):
    _name = 'b2b.quotation'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    def _message_post_after_hook(self, message, msg_vals):
        """Send real-time notification to customer portal"""
        super()._message_post_after_hook(message, msg_vals)

        # Send to customer portal via API
        self._notify_customer_portal(message)

    def _notify_customer_portal(self, message):
        """Send notification to customer portal"""
        notification_data = {
            'type': 'quotation_message',
            'quotation_id': self.id,
            'message': message.body,
            'author': message.author_id.name,
            'timestamp': message.date
        }

        # Send via WebSocket or API call
        self.env['b2b.notification'].send_to_portal(
            self.customer_id.id,
            notification_data
        )
```

#### Mobile and Email Notifications

**Email Template System:**

- **Branded Templates**: Customer-specific email branding
- **Multi-Language**: Arabic and English email templates
- **Rich Content**: HTML emails with embedded images and links
- **Tracking**: Email open and click tracking
- **Unsubscribe Management**: Granular unsubscribe options

**SMS Integration:**

- **Critical Alerts Only**: High-priority notifications via SMS
- **Saudi Telecom Integration**: Local SMS gateway integration
- **Delivery Confirmation**: SMS delivery status tracking
- **Opt-out Management**: SMS preference management

**Push Notifications:**

- **Browser Push**: Web push notifications for active users
- **Mobile App**: Future mobile app push notifications
- **Personalization**: User-specific notification content
- **Action Buttons**: Quick actions from notifications

---

## Internationalization

### Implementation Strategy

The application implements a custom internationalization system supporting English and Arabic languages with full RTL support.

#### Language Context Architecture

```mermaid
graph LR
    A[Language Context] --> B[Translation Function]
    A --> C[Direction Handler]
    A --> D[Locale Storage]
  
    B --> E[English Translations]
    B --> F[Arabic Translations]
  
    C --> G[LTR Layout]
    C --> H[RTL Layout]
  
    D --> I[localStorage]
    D --> J[Browser Detection]
  
    style A fill:#e1f5fe
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#f3e5f5
    style H fill:#fce4ec
```

#### Translation Structure

```typescript
const translations: Record<Language, Record<string, string>> = {
  en: {
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.orders': 'Orders',
    'products.title': 'Products',
    'products.search': 'Search products...',
    // ... more translations
  },
  ar: {
    'nav.home': 'الرئيسية',
    'nav.products': 'المنتجات',
    'nav.orders': 'الطلبات',
    'products.title': 'المنتجات',
    'products.search': 'البحث عن منتجات...',
    // ... more translations
  }
};
```

#### RTL Support Features

1. **CSS Direction Handling**: Automatic `dir="rtl"` attribute
2. **Layout Mirroring**: Flexbox and grid adjustments
3. **Text Alignment**: Right-to-left text flow
4. **Icon Mirroring**: Directional icons flip appropriately
5. **Spacing Adjustments**: Margin and padding logical properties

---

## UI/UX Design System

### Design Principles

1. **Consistency**: Unified component library with ShadCN UI
2. **Accessibility**: WCAG 2.1 AA compliance
3. **Responsiveness**: Mobile-first design approach
4. **Performance**: Optimized loading and interactions
5. **Internationalization**: Seamless language switching

### Customer-Specific Theme System

The KAUST B2B Marketplace implements a dynamic theming system that allows each customer organization to have their own branded experience while maintaining consistent functionality.

#### Multi-Tenant Theme Architecture

```mermaid
graph TB
    subgraph "Theme Configuration System"
        A[Customer Theme Config]
        B[Base Theme System]
        C[Dynamic CSS Generation]
        D[Runtime Theme Loading]
    end

    subgraph "KAUST Theme Example"
        E[KAUST Brand Colors]
        F[University Typography]
        G[Logo Integration]
        H[Custom Styling]
    end

    subgraph "Other Customer Themes"
        I[Customer B Theme]
        J[Customer C Theme]
        K[Default Theme]
    end

    A --> B
    B --> C
    C --> D

    A --> E
    E --> F
    F --> G
    G --> H

    A --> I
    A --> J
    A --> K

    style A fill:#e1f5fe
    style E fill:#e8f5e8
    style I fill:#fff3e0
```

#### Customer Theme Configuration

**JSON-Based Theme Configuration:**

```typescript
interface CustomerTheme {
  customerId: string;
  themeName: string;

  // Brand Identity
  branding: {
    logoUrl: string;
    faviconUrl: string;
    companyName: string;
    tagline?: string;
  };

  // Color Palette
  colors: {
    light: ColorScheme;
    dark: ColorScheme;
  };

  // Typography
  typography: {
    primaryFont: string;
    secondaryFont: string;
    arabicFont: string;
    fontSizes: FontScale;
  };

  // Layout Customization
  layout: {
    headerStyle: 'default' | 'minimal' | 'extended';
    sidebarPosition: 'left' | 'right';
    borderRadius: string;
    spacing: SpacingScale;
  };

  // Custom CSS
  customCSS?: string;

  // Feature Toggles
  features: {
    darkMode: boolean;
    rtlSupport: boolean;
    customWelcomeMessage: string;
  };
}

interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  muted: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}
```

#### KAUST-Specific Theme Configuration

**KAUST Brand Implementation:**

```json
{
  "customerId": "kaust",
  "themeName": "KAUST University Theme",
  "branding": {
    "logoUrl": "/themes/kaust/logo.svg",
    "faviconUrl": "/themes/kaust/favicon.ico",
    "companyName": "King Abdullah University of Science and Technology",
    "tagline": "Advancing Science and Technology"
  },
  "colors": {
    "light": {
      "primary": "#0066CC",
      "secondary": "#004499",
      "accent": "#FFB800",
      "background": "#FFFFFF",
      "foreground": "#1A1A1A",
      "muted": "#F5F5F5",
      "border": "#E5E5E5",
      "success": "#10B981",
      "warning": "#F59E0B",
      "error": "#EF4444"
    },
    "dark": {
      "primary": "#3B82F6",
      "secondary": "#1E40AF",
      "accent": "#FCD34D",
      "background": "#0F172A",
      "foreground": "#F8FAFC",
      "muted": "#1E293B",
      "border": "#334155",
      "success": "#059669",
      "warning": "#D97706",
      "error": "#DC2626"
    }
  },
  "typography": {
    "primaryFont": "Inter, sans-serif",
    "secondaryFont": "Roboto Mono, monospace",
    "arabicFont": "Noto Sans Arabic, sans-serif",
    "fontSizes": {
      "xs": "0.75rem",
      "sm": "0.875rem",
      "base": "1rem",
      "lg": "1.125rem",
      "xl": "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem"
    }
  },
  "layout": {
    "headerStyle": "extended",
    "sidebarPosition": "left",
    "borderRadius": "0.5rem",
    "spacing": {
      "xs": "0.25rem",
      "sm": "0.5rem",
      "md": "1rem",
      "lg": "1.5rem",
      "xl": "3rem"
    }
  },
  "features": {
    "darkMode": true,
    "rtlSupport": true,
    "customWelcomeMessage": "Welcome to KAUST B2B Marketplace"
  }
}
```

#### Dynamic Theme Loading System

**Runtime Theme Application:**

```mermaid
sequenceDiagram
    participant User
    participant Portal
    participant ThemeAPI
    participant ThemeCache
    participant CDN

    User->>Portal: Access Customer Portal
    Portal->>ThemeAPI: Get Customer Theme
    ThemeAPI->>ThemeCache: Check Cache

    alt Theme Cached
        ThemeCache->>Portal: Return Cached Theme
    else Theme Not Cached
        ThemeAPI->>CDN: Load Theme Config
        CDN->>ThemeAPI: Return Theme Data
        ThemeAPI->>ThemeCache: Cache Theme
        ThemeCache->>Portal: Return Theme
    end

    Portal->>Portal: Apply Dynamic CSS
    Portal->>User: Render Branded Interface
```

#### Theme Implementation Strategy

**CSS Custom Properties Approach:**

```css
/* Base Theme Variables */
:root {
  /* Colors - Dynamically set per customer */
  --color-primary: var(--customer-primary, #2563eb);
  --color-secondary: var(--customer-secondary, #64748b);
  --color-accent: var(--customer-accent, #f1f5f9);
  --color-background: var(--customer-background, #ffffff);
  --color-foreground: var(--customer-foreground, #171717);

  /* Typography - Customer specific fonts */
  --font-primary: var(--customer-font-primary, 'Inter', sans-serif);
  --font-secondary: var(--customer-font-secondary, 'Roboto Mono', monospace);
  --font-arabic: var(--customer-font-arabic, 'Noto Sans Arabic', sans-serif);

  /* Layout - Customer preferences */
  --border-radius: var(--customer-border-radius, 0.5rem);
  --spacing-unit: var(--customer-spacing, 1rem);
}

/* Customer-specific overrides injected at runtime */
.theme-kaust {
  --customer-primary: #0066CC;
  --customer-secondary: #004499;
  --customer-accent: #FFB800;
  --customer-font-primary: 'Inter', sans-serif;
}

.theme-customer-b {
  --customer-primary: #8B5CF6;
  --customer-secondary: #7C3AED;
  --customer-accent: #A78BFA;
  --customer-font-primary: 'Poppins', sans-serif;
}
```

#### Deployment Configuration

**Hard-coded Theme Configs per Customer:**

```typescript
// themes/index.ts - Deployed with application
export const customerThemes: Record<string, CustomerTheme> = {
  'kaust': {
    // KAUST theme configuration
    customerId: 'kaust',
    themeName: 'KAUST University Theme',
    // ... full configuration
  },
  'customer-b': {
    // Another customer theme
    customerId: 'customer-b',
    themeName: 'Customer B Corporate Theme',
    // ... full configuration
  },
  'default': {
    // Default fallback theme
    customerId: 'default',
    themeName: 'Default B2B Theme',
    // ... default configuration
  }
};

// Theme loading utility
export function getCustomerTheme(customerId: string): CustomerTheme {
  return customerThemes[customerId] || customerThemes['default'];
}
```

#### Advanced Theme Features

**Logo and Asset Management:**

- **Dynamic Logo Loading**: Customer logos loaded from CDN or local assets
- **Favicon Customization**: Customer-specific favicons and app icons
- **Background Patterns**: Optional background patterns or textures
- **Custom Icons**: Customer-specific icon sets or modifications

**Typography Customization:**

- **Web Font Loading**: Dynamic loading of customer-specific fonts
- **Font Fallbacks**: Graceful fallbacks for custom fonts
- **Arabic Font Support**: Specialized Arabic typography for RTL customers
- **Font Size Scaling**: Customer-specific font size preferences

**Layout Adaptations:**

- **Header Variations**: Different header styles per customer preference
- **Navigation Customization**: Custom navigation layouts and positioning
- **Component Spacing**: Adjustable spacing and padding throughout the interface
- **Border Radius**: Consistent border radius customization

#### Theme Performance Optimization

**Loading Strategy:**

- **Critical CSS**: Inline critical theme styles for fast rendering
- **Progressive Enhancement**: Load additional theme assets progressively
- **Font Display**: Optimize font loading with font-display: swap
- **Asset Preloading**: Preload customer-specific assets

**Caching Strategy:**

- **Theme Versioning**: Version-based cache invalidation
- **CDN Distribution**: Global distribution of theme assets
- **Browser Caching**: Long-term caching of static theme resources
- **Service Worker**: Offline theme asset caching

### Component Design Patterns

#### 1. Card Components

- Consistent shadow and border radius
- Hover states with smooth transitions
- Loading states with skeleton animations

#### 2. Form Components

- Unified input styling
- Error state handling
- Accessibility labels and descriptions

#### 3. Navigation Components

- Active state indicators
- Smooth transitions
- Mobile-responsive collapsing

---

## Home Page Layout

### KAUST B2B Marketplace Home Page Design

The home page serves as the central hub for B2B procurement activities, designed specifically for institutional buyers and enterprise workflows.

#### Main Layout Structure

```mermaid
graph TB
    subgraph "Header Section"
        A[KAUST Logo & Branding]
        B[Search Bar with Autocomplete]
        C[Active Quotation Selector]
        D[User Menu & Notifications]
        E[Language Toggle EN/AR]
        F[Theme Toggle Light/Dark]
    end

    subgraph "Navigation Bar"
        G[Home]
        H[Products]
        I[Orders]
        J[Account]
        K[Wishlist]
    end

    subgraph "Hero Section"
        L[Welcome Message]
        M[Quick Actions Panel]
        N[Active Quotations Summary]
    end

    subgraph "Main Content Area"
        O[Dashboard Widgets]
        P[Recent Orders]
        Q[Featured Products]
        R[Contract Information]
    end

    subgraph "Footer Section"
        S[Company Information]
        T[Support Links]
        U[Legal & Compliance]
    end

    A --> G
    B --> H
    C --> I
    D --> J
    E --> K
    L --> O
    M --> P
    N --> Q
    O --> R

    style A fill:#e1f5fe
    style L fill:#e8f5e8
    style O fill:#fff3e0
    style S fill:#f3e5f5
```

#### Header Component Layout

**Fixed Header Design** (Always visible during scroll):

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [KAUST Logo] [Search: "Search products..."] [Cart: My Order] [🔔] [👤] [🌙] [🇸🇦] │
└─────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────┐
│     [Home]     [Products]     [Orders]     [Account]     [Wishlist]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**Header Elements:**

- **KAUST Branding**: University logo and marketplace title
- **Global Search**: Intelligent product search with autocomplete
- **Active Quotation**: Current working quotation with item count
- **Notifications**: Order updates, quotation status changes
- **User Menu**: Profile, settings, logout
- **Theme Toggle**: Light/dark mode switcher
- **Language Toggle**: English/Arabic with flag icons

#### Hero Section Content

**Welcome Panel** (Personalized for logged-in users):

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        Welcome back, Dr. Ahmed Al-Rashid                    │
│                     KAUST - Computer Science Department                      │
│                                                                             │
│  [📋 Create New Quotation]  [📊 View Analytics]  [📋 Recent Orders]        │
│                                                                             │
│  Active Quotations: 3 Draft, 2 Under Review, 1 Approved                   │
└─────────────────────────────────────────────────────────────────────────────┘
```

**Quick Actions Panel:**

- **Create New Quotation**: Start new procurement request
- **View Analytics**: Department spending and order analytics
- **Recent Orders**: Quick access to order history
- **Contract Status**: Active contracts and SLA compliance

#### Main Dashboard Widgets

**Four-Column Responsive Layout:**

```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│   Active    │   Recent    │  Featured   │  Contract   │
│ Quotations  │   Orders    │  Products   │ Information │
│             │             │             │             │
│ • Q1 2024   │ • Order     │ • Laptops   │ • Frame     │
│   Laptops   │   #12345    │   Dell XPS  │   Contract  │
│   (Draft)   │   Delivered │   Special   │   Active    │
│             │             │   Pricing   │             │
│ • Emergency │ • Order     │             │ • SLA       │
│   IT Equip  │   #12344    │ • Phones    │   99.2%     │
│   (Review)  │   Shipped   │   iPhone 15 │   Uptime    │
│             │             │   Pro       │             │
│ [View All]  │ [View All]  │ [View All]  │ [View All]  │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

#### Responsive Design Breakpoints

**Desktop (1200px+):**

- Four-column dashboard layout
- Full header with all elements visible
- Expanded product cards with detailed information

**Tablet (768px - 1199px):**

- Two-column dashboard layout
- Collapsible navigation menu
- Condensed header with dropdown menus

**Mobile (< 768px):**

- Single-column stacked layout
- Hamburger menu navigation
- Simplified header with essential elements only

#### Content Sections

##### 1. Active Quotations Widget

```typescript
interface QuotationWidget {
  title: "Active Quotations"
  quotations: [
    {
      name: "Q1 2024 Laptops"
      status: "draft"
      itemCount: 15
      totalValue: "SAR 45,000"
      lastModified: "2 hours ago"
    },
    {
      name: "Emergency IT Equipment"
      status: "under_review"
      itemCount: 8
      totalValue: "SAR 12,500"
      lastModified: "1 day ago"
    }
  ]
  actions: ["Create New", "View All"]
}
```

##### 2. Recent Orders Widget

```typescript
interface OrderWidget {
  title: "Recent Orders"
  orders: [
    {
      orderNumber: "#ORD-12345"
      status: "delivered"
      deliveryDate: "2024-01-15"
      totalValue: "SAR 25,000"
      trackingNumber: "TRK-789456"
    },
    {
      orderNumber: "#ORD-12344"
      status: "shipped"
      estimatedDelivery: "2024-01-20"
      totalValue: "SAR 18,750"
      trackingNumber: "TRK-789455"
    }
  ]
  actions: ["Track Order", "Reorder", "View All"]
}
```

##### 3. Featured Products Widget

```typescript
interface ProductWidget {
  title: "Featured Products"
  products: [
    {
      name: "Dell XPS 13 Plus"
      category: "Laptops"
      contractPrice: "SAR 4,200"
      availability: "In Stock"
      specialOffer: "10% Volume Discount"
    },
    {
      name: "iPhone 15 Pro"
      category: "Mobile Devices"
      contractPrice: "SAR 3,800"
      availability: "Pre-Order"
      specialOffer: "Educational Pricing"
    }
  ]
  actions: ["Add to Quotation", "View Details", "View All"]
}
```

##### 4. Contract Information Widget

```typescript
interface ContractWidget {
  title: "Contract Information"
  contracts: [
    {
      contractName: "KAUST Frame Contract 2024"
      status: "active"
      validUntil: "2024-12-31"
      utilizationRate: "67%"
      slaCompliance: "99.2%"
    }
  ]
  metrics: {
    totalSpending: "SAR 450,000"
    remainingBudget: "SAR 150,000"
    averageDeliveryTime: "3.2 days"
  }
  actions: ["View Contract", "Download Terms", "Contact Manager"]
}
```

#### Accessibility Features

**WCAG 2.1 AA Compliance:**

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Color Contrast**: High contrast ratios for all text
- **Focus Indicators**: Clear focus states for interactive elements
- **Alternative Text**: Descriptive alt text for all images

**RTL Support for Arabic:**

- **Layout Mirroring**: Automatic layout direction changes
- **Text Alignment**: Proper text alignment for Arabic content
- **Icon Orientation**: Directional icons flip appropriately
- **Navigation Flow**: Intuitive navigation in RTL mode

#### Performance Optimization

**Loading Strategy:**

- **Critical CSS**: Inline critical styles for above-the-fold content
- **Lazy Loading**: Progressive loading of dashboard widgets
- **Image Optimization**: WebP format with fallbacks
- **Code Splitting**: Component-based code splitting

**Caching Strategy:**

- **Static Assets**: Aggressive caching of images and styles
- **API Responses**: Smart caching of dashboard data
- **User Preferences**: Local storage for theme and language
- **Session Data**: Secure session storage for user context

---

## Data Flow

### Application Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Component
    participant R as Redux Store
    participant A as API Layer
    participant M as Mock Data

    U->>C: User Action
    C->>R: Dispatch Action
    R->>A: API Call
    A->>M: Fetch Data
    M-->>A: Return Data
    A-->>R: Update State
    R-->>C: State Change
    C-->>U: UI Update
```

### Product Catalog Flow

1. **Initial Load**: Fetch all products from mock API
2. **Filter Application**: Apply filters in Redux store
3. **Search Query**: Real-time filtering based on search input
4. **Product Selection**: Navigate to product detail page
5. **Add to Order**: Update order state in Redux

### Order Management Flow

1. **Order Creation**: Create new order in Redux store
2. **Product Addition**: Add products with variants to order
3. **Quantity Management**: Update item quantities
4. **Order Submission**: Submit order for approval
5. **Status Tracking**: Monitor order status changes

---

## Security Considerations

### Frontend Security Measures

1. **Input Validation**: Client-side validation for all user inputs
2. **XSS Prevention**: Proper sanitization of user-generated content
3. **CSRF Protection**: Token-based protection for state changes
4. **Secure Storage**: Sensitive data encryption in localStorage
5. **Content Security Policy**: Strict CSP headers for production

### API Security (Future Implementation)

1. **Authentication**: JWT-based authentication with Odoo
2. **Authorization**: Role-based access control
3. **Rate Limiting**: API request throttling
4. **Data Encryption**: HTTPS for all communications
5. **Input Sanitization**: Server-side validation and sanitization

### Data Privacy

1. **GDPR Compliance**: User data handling according to GDPR
2. **Data Minimization**: Collect only necessary user data
3. **Consent Management**: Clear consent mechanisms
4. **Data Retention**: Defined data retention policies

---

## Performance Optimizations

### Frontend Optimizations

#### 1. Code Splitting

```typescript
// Dynamic imports for route-based code splitting
const ProductDetail = dynamic(() => import('./ProductDetail'), {
  loading: () => <ProductDetailSkeleton />
});
```

#### 2. Image Optimization

- Next.js Image component with automatic optimization
- WebP format support with fallbacks
- Lazy loading for below-the-fold images
- Responsive image sizing

#### 3. State Management Optimization

- Memoized selectors to prevent unnecessary re-renders
- Normalized state structure for efficient updates
- Debounced search inputs to reduce API calls

#### 4. Bundle Optimization

- Tree shaking for unused code elimination
- Dynamic imports for large dependencies
- Webpack bundle analysis and optimization

### Performance Metrics

| Metric                   | Target  | Current |
| ------------------------ | ------- | ------- |
| First Contentful Paint   | < 1.5s  | ~1.2s   |
| Largest Contentful Paint | < 2.5s  | ~2.1s   |
| Cumulative Layout Shift  | < 0.1   | ~0.05   |
| First Input Delay        | < 100ms | ~50ms   |

---

## Deployment Architecture

### Production Deployment

```mermaid
graph TB
    subgraph "CDN Layer"
        A[CloudFlare/Docker CloudFront]
    end

    subgraph "Application Layer"
        B[Next.js Application]
        C[Static Assets]
        D[API Routes]
    end

    subgraph "Integration Layer"
        E[Odoo ERP System]
        F[Payment Gateway 'Optional']
        G[Email Service]
    end

    subgraph "Monitoring"
        H[Application Monitoring]
        I[Error Tracking]
        J[Performance Monitoring]
    end

    A --> B
    B --> E
    B --> F
    B --> G
    B --> H
    B --> I
    B --> J

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style E fill:#fff3e0
```

### Environment Configuration

#### Development

- Local development server with hot reloading
- Mock API responses for rapid development
- Development-specific debugging tools

#### Staging

- Production-like environment for testing
- Integration with staging Odoo instance
- Performance testing and optimization

#### Production

- Optimized build with static generation
- CDN distribution for global performance
- Production Odoo integration
- Monitoring and alerting systems

---

### Development Standards

#### 1. Code Quality

- TypeScript strict mode enabled
- ESLint with Next.js configuration
- Prettier for code formatting
- Husky for pre-commit hooks

#### 2. Component Development

- Functional components with hooks
- Props interface definitions
- JSDoc comments for complex components
- Storybook for component documentation

#### 3. Testing Strategy

- Unit tests for utility functions
- Component testing with React Testing Library
- Integration tests for Redux slices
- E2E tests for critical user flows

#### 4. Documentation

- README files for each major feature
- API documentation for integration points
- Component documentation with examples
- Architecture decision records (ADRs)

---

## Future Enhancements

### Phase 2 Features

#### 1. Advanced Analytics

- User behavior tracking
- Product performance analytics
- Order conversion metrics
- Custom dashboard creation

#### 2. Enhanced Integration

- Real-time Odoo synchronization
- Payment gateway integration
- Inventory management system
- Shipping and logistics integration

#### 3. Mobile Application

- React Native mobile app
- Offline capability
- Push notifications
- Mobile-specific features

#### 4. AI/ML Features

- Product recommendation engine
- Intelligent search with NLP
- Automated pricing optimization
- Demand forecasting

### Technical Improvements

#### 1. Performance Enhancements

- Server-side rendering optimization
- Advanced caching strategies
- Database query optimization
- CDN optimization

#### 2. Security Enhancements

- Advanced authentication methods
- Audit logging system
- Security monitoring
- Compliance automation

#### 3. Scalability Improvements

- Microservices architecture
- Container orchestration
- Auto-scaling capabilities
- Load balancing optimization

---

## Project Structure Deep Dive

### Directory Structure

```
lp_commerce/
├── public/                          # Static assets
│   ├── flags/                       # Country flags for language selector
│   └── images/                      # Product images and icons
├── src/
│   ├── app/                         # Next.js 15 App Router
│   │   ├── (portal)/               # Portal route group
│   │   │   ├── layout.tsx          # Portal layout with providers
│   │   │   ├── page.tsx            # Home page
│   │   │   ├── products/           # Product pages
│   │   │   │   ├── page.tsx        # Product listing
│   │   │   │   └── [id]/           # Dynamic product detail
│   │   │   ├── orders/             # Order management
│   │   │   │   ├── page.tsx        # Orders listing
│   │   │   │   └── [id]/           # Order detail pages
│   │   │   ├── account/            # User account pages
│   │   │   └── wishlist/           # Wishlist management
│   │   ├── globals.css             # Global styles and CSS variables
│   │   └── layout.tsx              # Root layout
│   ├── components/                  # Reusable UI components
│   │   ├── Alert.tsx               # Custom alert system
│   │   ├── FiltersSidebar.tsx      # Product filtering interface
│   │   ├── ImagePreviewer.tsx      # Interactive image gallery
│   │   ├── OrderChatter.tsx        # Order communication system
│   │   ├── ProductCard.tsx         # Product grid item
│   │   ├── ProductDetail.tsx       # Detailed product view
│   │   ├── SearchAutocomplete.tsx  # Search with suggestions
│   │   └── ui/                     # ShadCN UI components
│   ├── contexts/                    # React Context providers
│   │   ├── LanguageContext.tsx     # Internationalization
│   │   └── ThemeContext.tsx        # Theme management
│   ├── lib/                        # Utility libraries
│   │   ├── api/                    # API integration layer
│   │   │   ├── mockData.ts         # Mock product data
│   │   │   └── odoo.ts             # Odoo API functions
│   │   ├── redux/                  # State management
│   │   │   ├── slices/             # Redux slices
│   │   │   │   ├── orders.ts       # Order management
│   │   │   │   ├── products.ts     # Product catalog
│   │   │   │   └── wishlist.ts     # Wishlist functionality
│   │   │   └── store.ts            # Redux store configuration
│   │   └── utils/                  # Utility functions
│   ├── types/                      # TypeScript type definitions
│   │   └── index.ts                # Core type definitions
│   └── data/                       # Static data files
│       └── userData.json           # User profile data
├── package.json                    # Dependencies and scripts
├── next.config.js                  # Next.js configuration
├── tailwind.config.js              # Tailwind CSS configuration
├── tsconfig.json                   # TypeScript configuration
└── README.md                       # Project documentation
```

### Key Configuration Files

#### Next.js Configuration (`next.config.js`)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
        pathname: '/**',
      },
    ],
  },
};
```

#### TypeScript Configuration

- Strict mode enabled for type safety
- Path mapping for clean imports
- JSX preserve for Next.js optimization
- Module resolution for Node.js compatibility

---

## Conclusion

The KAUST B2B Marketplace represents a modern, scalable, and user-friendly solution for institutional procurement needs. Built with cutting-edge technologies and following best practices, it provides a solid foundation for current requirements while being architected for future growth and enhancement.

The modular architecture, comprehensive state management, and robust integration capabilities make it an ideal platform for enterprise-level B2B operations. The focus on internationalization, accessibility, and performance ensures a superior user experience across diverse user bases and devices.

### Key Achievements

1. **Modern Tech Stack**: Leveraging Next.js 15, React 19, and TypeScript for robust development
2. **Scalable Architecture**: Component-based design with clear separation of concerns
3. **Enterprise Ready**: Built for B2B operations with multi-order management
4. **International Support**: Full English/Arabic localization with RTL support
5. **Performance Optimized**: Fast loading times and smooth user interactions
6. **Integration Ready**: Designed for seamless ERP integration

### Next Steps

1. **Portal Development**: Deploy to staging and production environments
2. **Odoo Integration**: Connect with real Odoo ERP system
3. **Production Deployment**: Deploy to staging and production environments
4. **User Testing**: Conduct comprehensive user acceptance testing
5. **Performance Monitoring**: Implement monitoring and analytics
6. **Feature Enhancement**: Add advanced features based on user feedback

---

*This document is maintained by the development team and updated with each major release. For technical questions or clarifications, please refer to the development team or create an issue in the project repository.*

**Document Version**: 1.0
**Last Updated**: June 2025
**Maintained By**: Laplace Development Team

<style>#mermaid-1751372976301{font-family:sans-serif;font-size:16px;fill:#333;}#mermaid-1751372976301 .error-icon{fill:#552222;}#mermaid-1751372976301 .error-text{fill:#552222;stroke:#552222;}#mermaid-1751372976301 .edge-thickness-normal{stroke-width:2px;}#mermaid-1751372976301 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1751372976301 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1751372976301 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1751372976301 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1751372976301 .marker{fill:#333333;}#mermaid-1751372976301 .marker.cross{stroke:#333333;}#mermaid-1751372976301 svg{font-family:sans-serif;font-size:16px;}#mermaid-1751372976301 .label{font-family:sans-serif;color:#333;}#mermaid-1751372976301 .label text{fill:#333;}#mermaid-1751372976301 .node rect,#mermaid-1751372976301 .node circle,#mermaid-1751372976301 .node ellipse,#mermaid-1751372976301 .node polygon,#mermaid-1751372976301 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-1751372976301 .node .label{text-align:center;}#mermaid-1751372976301 .node.clickable{cursor:pointer;}#mermaid-1751372976301 .arrowheadPath{fill:#333333;}#mermaid-1751372976301 .edgePath .path{stroke:#333333;stroke-width:1.5px;}#mermaid-1751372976301 .flowchart-link{stroke:#333333;fill:none;}#mermaid-1751372976301 .edgeLabel{background-color:#e8e8e8;text-align:center;}#mermaid-1751372976301 .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#mermaid-1751372976301 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-1751372976301 .cluster text{fill:#333;}#mermaid-1751372976301 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:sans-serif;font-size:12px;background:hsl(80,100%,96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-1751372976301:root{--mermaid-font-family:sans-serif;}#mermaid-1751372976301:root{--mermaid-alt-font-family:sans-serif;}#mermaid-1751372976301 flowchart{fill:apa;}</style>
