# Task 06: Product Detail Pages with Image Gallery

## Objective
Implement comprehensive product detail pages with interactive image galleries, specifications, variants, and B2B-specific features like bulk ordering and quotation requests.

## Technical Requirements

### Product Detail Page Structure

#### Page Layout Architecture
```
ProductDetailPage
├── Breadcrumb
├── ProductGallery
│   ├── MainImage
│   ├── ThumbnailNavigation
│   ├── ZoomModal
│   └── ImagePreloader
├── ProductInfo
│   ├── ProductHeader
│   ├── PriceDisplay
│   ├── VariantSelector
│   ├── QuantitySelector
│   ├── ActionButtons
│   └── DeliveryInfo
├── ProductTabs
│   ├── DescriptionTab
│   ├── SpecificationsTab
│   ├── ReviewsTab
│   └── DocumentsTab
└── RelatedProducts
```

### Product Gallery Component

#### Gallery Interface
```typescript
interface ProductGalleryProps {
  images: ProductImage[];
  productName: string;
  onImageChange?: (index: number) => void;
  enableZoom?: boolean;
  enableFullscreen?: boolean;
  className?: string;
}

interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
  thumbnailUrl?: string;
  zoomUrl?: string;
}

interface GalleryState {
  currentIndex: number;
  isZoomed: boolean;
  isFullscreen: boolean;
  isLoading: boolean;
  touchStart: { x: number; y: number } | null;
}
```

#### Gallery Layout Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [Thumb1] │                                                                  │
│ [Thumb2] │                    Main Product Image                            │
│ [Thumb3] │                                                                  │
│ [Thumb4] │                                                                  │
│ [Thumb5] │                                                                  │
│          │ [🔍] [⛶] [❮] [❯]                                                │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### Gallery Features
- **Thumbnail Navigation**: Vertical or horizontal thumbnail strip
- **Image Zoom**: Magnifying glass on hover, click to zoom
- **Fullscreen Mode**: Modal overlay with navigation
- **Touch Support**: Swipe gestures for mobile
- **Keyboard Navigation**: Arrow keys, escape
- **Lazy Loading**: Progressive image loading
- **Preloading**: Next/previous image preloading

### Image Zoom Component

#### Zoom Interface
```typescript
interface ImageZoomProps {
  src: string;
  alt: string;
  zoomSrc?: string;
  zoomLevel?: number;
  enablePan?: boolean;
  className?: string;
}

interface ZoomState {
  isZoomed: boolean;
  zoomLevel: number;
  panPosition: { x: number; y: number };
  mousePosition: { x: number; y: number };
}
```

#### Zoom Implementation Features
- **Hover Zoom**: Magnifying glass effect on hover
- **Click Zoom**: Click to toggle zoom mode
- **Pan Support**: Drag to pan when zoomed
- **Zoom Controls**: Zoom in/out buttons
- **Touch Zoom**: Pinch to zoom on mobile
- **Reset Position**: Double-click to reset

### Product Information Section

#### Product Info Interface
```typescript
interface ProductInfoProps {
  product: Product;
  selectedVariant?: ProductVariant;
  onVariantChange?: (variant: ProductVariant) => void;
  onAddToOrder?: (product: Product, quantity: number) => void;
  onAddToWishlist?: (product: Product) => void;
  onRequestQuote?: (product: Product, quantity: number) => void;
}
```

#### Product Header Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ MacBook Pro 16-inch with M3 Pro chip                                       │
│ Apple • SKU: MBP16-M3-512                                                  │
│ ⭐⭐⭐⭐⭐ 4.8 (124 reviews) • In Stock (15 units)                          │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### Price Display Component
```typescript
interface PriceDisplayProps {
  price: number;
  currency: string;
  originalPrice?: number;
  discount?: number;
  priceType: 'fixed' | 'quote' | 'negotiable';
  minimumQuantity?: number;
  bulkPricing?: BulkPricing[];
}

interface BulkPricing {
  minQuantity: number;
  maxQuantity?: number;
  price: number;
  discount: number;
}
```

#### Price Display Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ SAR 12,999.00                                                               │
│ Was: SAR 14,999.00 (Save 13%)                                              │
│                                                                             │
│ Bulk Pricing:                                                               │
│ • 5-9 units: SAR 12,499.00 each (4% off)                                   │
│ • 10+ units: SAR 11,999.00 each (8% off)                                   │
│                                                                             │
│ Minimum order: 1 unit                                                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Variant Selector Component

#### Variant Interface
```typescript
interface VariantSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant;
  onVariantChange: (variant: ProductVariant) => void;
  variantAttributes: VariantAttribute[];
}

interface VariantAttribute {
  name: string;
  type: 'color' | 'size' | 'storage' | 'memory' | 'text';
  options: VariantOption[];
  required: boolean;
}

interface VariantOption {
  value: string;
  label: string;
  colorCode?: string;
  imageUrl?: string;
  available: boolean;
  priceModifier?: number;
}
```

#### Variant Selector Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Configuration:                                                              │
│                                                                             │
│ Storage: ● 512GB SSD   ○ 1TB SSD (+SAR 1,000)   ○ 2TB SSD (+SAR 3,000)    │
│                                                                             │
│ Memory:  ● 16GB        ○ 32GB (+SAR 2,000)       ○ 64GB (+SAR 6,000)       │
│                                                                             │
│ Color:   ● Space Gray  ○ Silver                                             │
│          [■]           [□]                                                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Quantity and Action Controls

#### Quantity Selector Interface
```typescript
interface QuantitySelectorProps {
  value: number;
  min?: number;
  max?: number;
  step?: number;
  onChange: (quantity: number) => void;
  disabled?: boolean;
  showBulkOptions?: boolean;
}
```

#### Action Buttons Interface
```typescript
interface ProductActionsProps {
  product: Product;
  quantity: number;
  selectedVariant?: ProductVariant;
  onAddToOrder: () => void;
  onAddToWishlist: () => void;
  onRequestQuote: () => void;
  onCompare?: () => void;
  loading?: boolean;
}
```

#### Actions Section Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Quantity: [−] [5] [+]    Bulk: [10] [25] [50] [100]                        │
│                                                                             │
│ [Add to Order]  [♡ Add to Wishlist]  [📋 Request Quote]  [⚖ Compare]      │
│                                                                             │
│ 🚚 Delivery: 3-5 business days                                             │
│ 📞 Need help? Contact our B2B team                                         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Product Tabs Component

#### Tabs Interface
```typescript
interface ProductTabsProps {
  product: Product;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

interface ProductTab {
  id: string;
  label: string;
  content: React.ReactNode;
  badge?: number;
}
```

#### Description Tab Content
```typescript
interface DescriptionTabProps {
  description: string;
  features: string[];
  highlights: string[];
  specifications: ProductSpecification[];
}
```

#### Specifications Tab Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ General                                                                     │
│ ├─ Brand: Apple                                                             │
│ ├─ Model: MacBook Pro 16"                                                   │
│ ├─ SKU: MBP16-M3-512                                                        │
│ └─ Warranty: 1 Year Limited                                                 │
│                                                                             │
│ Performance                                                                 │
│ ├─ Processor: Apple M3 Pro chip                                             │
│ ├─ Memory: 16GB Unified Memory                                              │
│ ├─ Storage: 512GB SSD                                                       │
│ └─ Graphics: Integrated GPU                                                 │
│                                                                             │
│ Display                                                                     │
│ ├─ Size: 16.2-inch                                                          │
│ ├─ Resolution: 3456 x 2234                                                  │
│ ├─ Technology: Liquid Retina XDR                                            │
│ └─ Brightness: 1000 nits                                                    │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Related Products Section

#### Related Products Interface
```typescript
interface RelatedProductsProps {
  productId: string;
  relatedProducts: Product[];
  type: 'similar' | 'accessories' | 'frequently_bought' | 'alternatives';
  title?: string;
  maxItems?: number;
}
```

#### Related Products Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Frequently Bought Together                                                  │
│                                                                             │
│ [Product 1] + [Product 2] + [Product 3] = Total: SAR 15,999                │
│                                                                             │
│ [Add All to Order]                                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ Similar Products                                                            │
│                                                                             │
│ [Product Card] [Product Card] [Product Card] [Product Card]                 │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Mobile Optimization

#### Mobile Product Detail Layout
```typescript
interface MobileProductDetailProps {
  product: Product;
  isMobile: boolean;
}

// Mobile-specific features:
// - Swipeable image gallery
// - Sticky add to cart button
// - Collapsible sections
// - Touch-friendly controls
// - Bottom sheet for variants
```

### Accessibility Features

#### Accessibility Requirements
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Keyboard Navigation**: Tab order and keyboard shortcuts
- **Focus Management**: Visible focus indicators
- **Image Alt Text**: Descriptive alt text for all images
- **Color Contrast**: WCAG AA compliance
- **Zoom Accessibility**: Screen reader compatible zoom

### Performance Optimization

#### Image Optimization
```typescript
interface ImageOptimization {
  lazyLoading: boolean;
  progressiveLoading: boolean;
  webpSupport: boolean;
  responsiveImages: boolean;
  preloadCritical: boolean;
}

// Optimization features:
// - Next.js Image component
// - WebP format with fallbacks
// - Responsive image sizes
// - Progressive JPEG loading
// - Critical image preloading
```

## Mock Data Requirements

### Mock Product Detail Data
```typescript
const mockProductDetail: Product = {
  id: 'mbp-16-m3-512',
  sku: 'MBP16-M3-512',
  name: 'MacBook Pro 16-inch with M3 Pro chip',
  description: 'The most powerful MacBook Pro ever...',
  category: { id: 'laptops', name: 'Laptops', slug: 'laptops', level: 1 },
  brand: 'Apple',
  price: 12999,
  currency: 'SAR',
  availability: 'in_stock',
  stockQuantity: 15,
  minimumOrderQuantity: 1,
  images: [
    {
      id: 'img-1',
      url: '/images/products/mbp-16-1.jpg',
      alt: 'MacBook Pro 16" front view',
      isPrimary: true,
      order: 1,
      thumbnailUrl: '/images/products/mbp-16-1-thumb.jpg',
      zoomUrl: '/images/products/mbp-16-1-zoom.jpg'
    },
    // ... more images
  ],
  specifications: [
    { name: 'Processor', value: 'Apple M3 Pro chip', category: 'Performance' },
    { name: 'Memory', value: '16GB', unit: 'GB', category: 'Performance' },
    // ... more specifications
  ],
  variants: [
    {
      id: 'mbp-16-m3-512-sg',
      name: 'Space Gray, 512GB',
      sku: 'MBP16-M3-512-SG',
      price: 12999,
      attributes: { color: 'Space Gray', storage: '512GB' },
      availability: 'in_stock'
    },
    // ... more variants
  ],
  // ... other product properties
};
```

### Mock Related Products
```typescript
const mockRelatedProducts = {
  similar: [], // Similar laptops
  accessories: [], // Laptop accessories
  frequently_bought: [], // Frequently bought together
  alternatives: [] // Alternative products
};
```

## Acceptance Criteria

1. ✅ Product detail page with complete information
2. ✅ Interactive image gallery with zoom functionality
3. ✅ Thumbnail navigation with keyboard support
4. ✅ Variant selector with all attribute types
5. ✅ Quantity selector with bulk options
6. ✅ Add to order, wishlist, and quote request
7. ✅ Tabbed content with specifications
8. ✅ Related products recommendations
9. ✅ Mobile-responsive design
10. ✅ Accessibility compliance
11. ✅ Performance optimization
12. ✅ Loading states and error handling
13. ✅ Breadcrumb navigation
14. ✅ Social sharing capabilities

## Integration Points
- Product data will be fetched from Odoo product catalog
- Related products will use recommendation engine
- Reviews will integrate with review system
- Inventory will sync with warehouse management

## Visual Requirements
- Professional B2B product presentation
- High-quality image display
- Clear pricing and availability information
- Intuitive variant selection
- Smooth animations and transitions

## Notes
- Focus on B2B-specific features like bulk pricing
- Optimize for large product catalogs
- Support complex product configurations
- Ensure fast loading times
- Implement proper SEO optimization
