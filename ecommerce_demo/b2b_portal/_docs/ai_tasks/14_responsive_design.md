# Task 14: Responsive Design and Mobile Optimization

## Objective
Implement comprehensive responsive design with mobile-first approach, touch-friendly interfaces, and optimized user experience across all device sizes.

## Technical Requirements

### Responsive Breakpoints

#### Breakpoint System
```typescript
interface Breakpoints {
  xs: string;    // Extra small devices (phones)
  sm: string;    // Small devices (large phones)
  md: string;    // Medium devices (tablets)
  lg: string;    // Large devices (desktops)
  xl: string;    // Extra large devices (large desktops)
  '2xl': string; // Ultra wide screens
}

const breakpoints: Breakpoints = {
  xs: '0px',      // 0px and up
  sm: '640px',    // 640px and up
  md: '768px',    // 768px and up
  lg: '1024px',   // 1024px and up
  xl: '1280px',   // 1280px and up
  '2xl': '1536px' // 1536px and up
};
```

#### Responsive Utilities
```css
/* Mobile First Approach */
.container {
  width: 100%;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    margin: 0 auto;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding: 0 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}
```

### Mobile Layout Components

#### Mobile Header
```typescript
interface MobileHeaderProps {
  title: string;
  showBack?: boolean;
  showMenu?: boolean;
  showSearch?: boolean;
  onBack?: () => void;
  onMenuToggle?: () => void;
  onSearchToggle?: () => void;
  actions?: React.ReactNode;
}
```

#### Mobile Header Design
```
┌─────────────────────────────────────┐
│ [☰] KAUST B2B Portal        [🔍] [👤] │
└─────────────────────────────────────┘

With Back Button:
┌─────────────────────────────────────┐
│ [←] Order Details           [⋯] [👤] │
└─────────────────────────────────────┘
```

#### Mobile Navigation
```typescript
interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
  items: NavigationItem[];
  user: User;
  onLogout: () => void;
}
```

#### Mobile Navigation Design
```
Slide-out Navigation:
┌─────────────────────────────────────┐
│ [×]                                 │
│                                     │
│ [Avatar] Ahmed Al-Rashid            │
│ Computer Science Dept               │
│                                     │
│ 🏠 Home                             │
│ 📦 Products                         │
│ 📋 Orders                      (3)  │
│ ❤️  Wishlist                   (5)  │
│ 👤 Account                          │
│                                     │
│ ⚙️  Settings                        │
│ 🚪 Logout                           │
│                                     │
│ 🌙 Dark Mode    [Toggle]            │
│ 🇸🇦 العربية      [Toggle]            │
└─────────────────────────────────────┘

Bottom Navigation (Alternative):
┌─────────────────────────────────────┐
│ [🏠] [📦] [📋] [❤️] [👤]              │
│ Home Prod Orders Wish Acct          │
└─────────────────────────────────────┘
```

### Touch-Friendly Components

#### Touch Target Specifications
```css
/* Minimum touch target sizes */
.touch-target {
  min-height: 44px;  /* iOS recommendation */
  min-width: 44px;
  padding: 8px;
}

.touch-target-large {
  min-height: 56px;  /* Material Design recommendation */
  min-width: 56px;
  padding: 12px;
}

/* Touch feedback */
.touch-feedback {
  transition: all 0.15s ease;
}

.touch-feedback:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* Swipe gestures */
.swipeable {
  touch-action: pan-x;
  user-select: none;
}
```

#### Mobile Product Card
```typescript
interface MobileProductCardProps {
  product: Product;
  onTap: () => void;
  onAddToOrder: () => void;
  onAddToWishlist: () => void;
  onQuickView: () => void;
}
```

#### Mobile Product Card Design
```
┌─────────────────────────────────────┐
│ [Product Image]                     │
│                                     │
│ MacBook Pro 16" M3                  │
│ Apple                               │
│ SAR 12,999                          │
│ ⭐⭐⭐⭐⭐ (4.8) • In Stock          │
│                                     │
│ [Add to Order] [♡] [👁]             │
└─────────────────────────────────────┘

Compact Version:
┌─────────────────────────────────────┐
│ [Img] MacBook Pro 16" M3      12,999│
│       Apple • ⭐4.8 • Stock    [+][♡]│
└─────────────────────────────────────┘
```

### Mobile Order Management

#### Mobile Order Interface
```typescript
interface MobileOrderManagerProps {
  orders: Order[];
  activeOrderId: string | null;
  onOrderSelect: (orderId: string) => void;
  onCreateOrder: () => void;
}
```

#### Mobile Order Selector
```
Bottom Sheet Style:
┌─────────────────────────────────────┐
│ ═══                                 │ ← Drag handle
│                                     │
│ My Orders                           │
│                                     │
│ ● Q1 2024 Laptops (5 items)        │
│   Draft • SAR 74,750                │
│                                     │
│   Emergency IT Equipment (2 items)  │
│   Submitted • SAR 25,000            │
│                                     │
│   Office Supplies (8 items)         │
│   Under Review • SAR 12,500         │
│                                     │
│ [+ Create New Order]                │
└─────────────────────────────────────┘
```

#### Mobile Order Detail
```typescript
interface MobileOrderDetailProps {
  order: Order;
  onUpdateItem: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onAddItem: () => void;
}
```

#### Mobile Order Item Design
```
Swipeable Item Card:
┌─────────────────────────────────────┐
│ [Img] MacBook Pro 16" M3            │
│       SKU: MBP16-M3-512             │
│       Qty: [−] 2 [+]  SAR 25,998    │
│                                     │
│ ← Swipe left for actions            │
└─────────────────────────────────────┘

Swiped State:
┌─────────────────────────────────────┐
│ [Edit] [Delete]  MacBook Pro 16"    │
└─────────────────────────────────────┘
```

### Mobile Search Interface

#### Mobile Search Component
```typescript
interface MobileSearchProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  onSearch: (query: string) => void;
  suggestions: SearchSuggestion[];
  recentSearches: string[];
}
```

#### Mobile Search Design
```
Search Overlay:
┌─────────────────────────────────────┐
│ [←] [Search products...     ] [×]   │
├─────────────────────────────────────┤
│ Recent Searches                     │
│ • macbook pro                       │
│ • dell laptop                       │
│ • iphone 15                         │
│                                     │
│ Popular Searches                    │
│ • laptops                           │
│ • smartphones                       │
│ • accessories                       │
│                                     │
│ [Voice Search] [Barcode Scan]       │
└─────────────────────────────────────┘
```

### Mobile Filters and Sorting

#### Mobile Filters Interface
```typescript
interface MobileFiltersProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  onApply: () => void;
  onClear: () => void;
}
```

#### Mobile Filters Design
```
Filter Modal:
┌─────────────────────────────────────┐
│ Filters                         [×] │
├─────────────────────────────────────┤
│ Category                        [>] │
│ Brand                           [>] │
│ Price Range                     [>] │
│ Availability                    [>] │
│                                     │
│ Active Filters (3):                 │
│ [Laptops ×] [Apple ×] [In Stock ×]  │
│                                     │
│ [Clear All]           [Apply (24)]  │
└─────────────────────────────────────┘

Expandable Filter Section:
┌─────────────────────────────────────┐
│ ▼ Category                          │
│   ☑ Laptops (45)                    │
│   ☐ Phones (32)                     │
│   ☐ Electronics (67)                │
│                                     │
│ ▶ Brand                             │
│ ▶ Price Range                       │
└─────────────────────────────────────┘
```

### Gesture Support

#### Swipe Gestures
```typescript
interface SwipeGestureProps {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
  children: React.ReactNode;
}

interface SwipeableItemProps {
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipe?: (direction: 'left' | 'right', action: string) => void;
  children: React.ReactNode;
}

interface SwipeAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
}
```

#### Pull-to-Refresh
```typescript
interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  refreshing: boolean;
  threshold?: number;
  children: React.ReactNode;
}
```

### Mobile Performance Optimizations

#### Lazy Loading and Virtualization
```typescript
interface VirtualizedListProps {
  items: any[];
  itemHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  overscan?: number;
  onEndReached?: () => void;
  loading?: boolean;
}

interface InfiniteScrollProps {
  hasMore: boolean;
  loadMore: () => void;
  loading: boolean;
  threshold?: number;
  children: React.ReactNode;
}
```

#### Image Optimization
```typescript
interface ResponsiveImageProps {
  src: string;
  alt: string;
  sizes: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
}

// Example usage:
// <ResponsiveImage
//   src="/product-image.jpg"
//   alt="Product"
//   sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//   priority={true}
// />
```

### Mobile-Specific Features

#### Device Capabilities
```typescript
interface DeviceCapabilities {
  hasCamera: boolean;
  hasGeolocation: boolean;
  hasVibration: boolean;
  hasTouchID: boolean;
  hasNotifications: boolean;
  isOnline: boolean;
  connectionType: string;
}

interface UseMobileCapabilitiesReturn {
  capabilities: DeviceCapabilities;
  requestPermission: (permission: string) => Promise<boolean>;
  vibrate: (pattern?: number | number[]) => void;
  shareContent: (data: ShareData) => Promise<void>;
  installApp: () => Promise<void>;
}
```

#### Progressive Web App Features
```typescript
interface PWAFeatures {
  isInstallable: boolean;
  isInstalled: boolean;
  isStandalone: boolean;
  promptInstall: () => Promise<void>;
  updateAvailable: boolean;
  updateApp: () => Promise<void>;
}
```

### Accessibility on Mobile

#### Touch Accessibility
```css
/* Larger touch targets for accessibility */
@media (max-width: 768px) {
  .btn {
    min-height: 48px;
    min-width: 48px;
    padding: 12px 16px;
  }
  
  .form-input {
    min-height: 48px;
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .nav-link {
    padding: 16px 20px;
    font-size: 18px;
  }
}

/* Focus indicators for keyboard navigation */
.focus-visible {
  outline: 2px solid #0066CC;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

### Mobile Testing Utilities

#### Responsive Testing Hook
```typescript
interface UseResponsiveReturn {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  orientation: 'portrait' | 'landscape';
  touchDevice: boolean;
}

const useResponsive = (): UseResponsiveReturn => {
  const [screenSize, setScreenSize] = useState<string>('md');
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
  
  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 640) setScreenSize('xs');
      else if (width < 768) setScreenSize('sm');
      else if (width < 1024) setScreenSize('md');
      else if (width < 1280) setScreenSize('lg');
      else if (width < 1536) setScreenSize('xl');
      else setScreenSize('2xl');
      
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };
    
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);
  
  return {
    isMobile: ['xs', 'sm'].includes(screenSize),
    isTablet: screenSize === 'md',
    isDesktop: ['lg', 'xl', '2xl'].includes(screenSize),
    screenSize: screenSize as any,
    orientation,
    touchDevice: 'ontouchstart' in window
  };
};
```

## Mock Data Requirements

### Mock Device Data
```typescript
const mockDeviceCapabilities: DeviceCapabilities = {
  hasCamera: true,
  hasGeolocation: true,
  hasVibration: true,
  hasTouchID: false,
  hasNotifications: true,
  isOnline: true,
  connectionType: '4g'
};
```

### Mock Responsive Breakpoints
```typescript
const mockBreakpointTests = [
  { width: 375, height: 667, device: 'iPhone SE' },
  { width: 414, height: 896, device: 'iPhone 11 Pro Max' },
  { width: 768, height: 1024, device: 'iPad' },
  { width: 1024, height: 768, device: 'iPad Landscape' },
  { width: 1920, height: 1080, device: 'Desktop' }
];
```

## Acceptance Criteria

1. ✅ Mobile-first responsive design implementation
2. ✅ Touch-friendly interface with proper target sizes
3. ✅ Mobile navigation with slide-out menu
4. ✅ Swipe gestures for common actions
5. ✅ Mobile-optimized product catalog
6. ✅ Mobile order management interface
7. ✅ Mobile search with overlay design
8. ✅ Mobile filters with modal interface
9. ✅ Pull-to-refresh functionality
10. ✅ Infinite scroll for product lists
11. ✅ Mobile accessibility compliance
12. ✅ Progressive Web App features
13. ✅ Offline support for core features
14. ✅ Performance optimization for mobile

## Integration Points
- Responsive images will use Next.js Image optimization
- PWA features will integrate with service worker
- Touch gestures will work with order management
- Mobile notifications will use web push API

## Visual Requirements
- Smooth animations and transitions
- Touch feedback for all interactive elements
- Consistent mobile design patterns
- Professional mobile B2B interface
- Optimized for one-handed use

## Notes
- Focus on mobile B2B user workflows
- Ensure fast loading on mobile networks
- Support both portrait and landscape orientations
- Implement proper keyboard handling for mobile
- Optimize for various screen densities
