# Task 09: Order Communication and Chatter System

## Objective
Implement a comprehensive messaging and communication system for orders, enabling real-time discussions between customers and vendors throughout the procurement lifecycle.

## Technical Requirements

### Communication Architecture

#### Message System Structure
```
OrderCommunication
├── MessageThread
│   ├── MessageList
│   ├── MessageInput
│   └── MessageActions
├── ParticipantsList
├── FileAttachments
├── MessageNotifications
└── MessageHistory
```

### Message Data Structure

#### Message Interface
```typescript
interface Message {
  id: string;
  orderId: string;
  threadId?: string;
  parentMessageId?: string;
  authorId: string;
  authorName: string;
  authorRole: 'customer' | 'vendor' | 'system';
  authorAvatar?: string;
  content: string;
  messageType: 'text' | 'system' | 'file' | 'status_update';
  attachments: MessageAttachment[];
  mentions: MessageMention[];
  timestamp: Date;
  editedAt?: Date;
  readBy: MessageRead[];
  reactions: MessageReaction[];
  metadata: MessageMetadata;
}

interface MessageAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  thumbnailUrl?: string;
}

interface MessageMention {
  userId: string;
  userName: string;
  startIndex: number;
  endIndex: number;
}

interface MessageRead {
  userId: string;
  readAt: Date;
}

interface MessageReaction {
  emoji: string;
  userId: string;
  userName: string;
  timestamp: Date;
}

interface MessageMetadata {
  systemGenerated: boolean;
  priority: 'low' | 'medium' | 'high';
  category: 'general' | 'technical' | 'pricing' | 'delivery';
  relatedData?: Record<string, any>;
}
```

### Message Thread Component

#### Thread Interface
```typescript
interface MessageThreadProps {
  orderId: string;
  messages: Message[];
  currentUser: User;
  onSendMessage: (content: string, attachments?: File[]) => void;
  onEditMessage: (messageId: string, content: string) => void;
  onDeleteMessage: (messageId: string) => void;
  onReactToMessage: (messageId: string, emoji: string) => void;
  participants: Participant[];
  readonly?: boolean;
}

interface Participant {
  id: string;
  name: string;
  role: 'customer' | 'vendor' | 'admin';
  avatar?: string;
  online: boolean;
  lastSeen?: Date;
}
```

#### Message Thread Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Order Discussion: Q1 2024 Laptops                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ Participants: Ahmed Al-Rashid (Customer), John Smith (Vendor) ● Online     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ [System] Order submitted for review - 2 days ago                           │
│                                                                             │
│ [Ahmed] Hi John, this is an urgent request for our AI research lab.        │
│         We need the equipment by end of month. Can you expedite?           │
│         📎 lab_requirements.pdf                                             │
│         2 days ago                                                          │
│                                                                             │
│ [John]  Hi Ahmed, I've reviewed your requirements. The MacBook Pros        │
│         are in stock and can be delivered within 5 business days.          │
│         However, the custom configurations might take 2 weeks.             │
│         1 day ago                                                           │
│                                                                             │
│ [Ahmed] @John Can we split the order? Ship standard configs first?         │
│         👍 1                                                                │
│         4 hours ago                                                         │
│                                                                             │
│ [John]  ✅ Absolutely! I'll prepare a revised quotation with split         │
│         delivery. Standard MacBooks can ship this week.                    │
│         2 hours ago                                                         │
│                                                                             │
│ [System] Status updated to "Revised" - 1 hour ago                          │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [Type your message...                                    ] [📎] [😊] [Send] │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Message Input Component

#### Message Input Interface
```typescript
interface MessageInputProps {
  onSendMessage: (content: string, attachments?: File[]) => void;
  onTyping?: (isTyping: boolean) => void;
  placeholder?: string;
  maxLength?: number;
  allowAttachments?: boolean;
  allowMentions?: boolean;
  participants?: Participant[];
  disabled?: boolean;
}

interface MessageInputState {
  content: string;
  attachments: File[];
  mentions: MessageMention[];
  showEmojiPicker: boolean;
  showMentionsList: boolean;
  isTyping: boolean;
}
```

#### Message Input Features
- **Rich Text Editing**: Basic formatting (bold, italic, links)
- **File Attachments**: Drag & drop file upload
- **Emoji Picker**: Emoji selection and reactions
- **@Mentions**: User mention with autocomplete
- **Typing Indicators**: Show when users are typing
- **Message Templates**: Quick response templates
- **Auto-save Drafts**: Save message drafts automatically

### File Attachment System

#### Attachment Interface
```typescript
interface AttachmentManagerProps {
  attachments: MessageAttachment[];
  onUpload: (files: File[]) => void;
  onRemove: (attachmentId: string) => void;
  onDownload: (attachment: MessageAttachment) => void;
  maxFileSize?: number;
  allowedTypes?: string[];
  maxFiles?: number;
}
```

#### Attachment Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Attachments (3)                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 📄 lab_requirements.pdf (2.3 MB)                              [↓] [×]       │
│ 📊 budget_breakdown.xlsx (1.1 MB)                             [↓] [×]       │
│ 🖼️ lab_layout.png (856 KB)                                    [👁] [↓] [×]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ [📎 Add Files] or drag and drop files here                                 │
│ Max 10MB per file • PDF, DOC, XLS, PNG, JPG allowed                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### System Messages

#### System Message Types
```typescript
interface SystemMessage {
  type: 'status_change' | 'item_added' | 'item_removed' | 'price_update' | 'user_joined' | 'user_left';
  data: Record<string, any>;
  template: string;
}

const systemMessageTemplates = {
  status_change: "Order status changed from {fromStatus} to {toStatus}",
  item_added: "{userName} added {itemName} (Qty: {quantity}) to the order",
  item_removed: "{userName} removed {itemName} from the order",
  price_update: "Price updated: {oldPrice} → {newPrice} ({change})",
  user_joined: "{userName} joined the conversation",
  user_left: "{userName} left the conversation"
};
```

### Real-time Communication

#### WebSocket Integration
```typescript
interface MessageWebSocketEvents {
  'message:new': (message: Message) => void;
  'message:edited': (messageId: string, content: string) => void;
  'message:deleted': (messageId: string) => void;
  'message:reaction': (messageId: string, reaction: MessageReaction) => void;
  'user:typing': (userId: string, isTyping: boolean) => void;
  'user:online': (userId: string) => void;
  'user:offline': (userId: string) => void;
}

interface UseMessageWebSocketReturn {
  connected: boolean;
  sendMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  editMessage: (messageId: string, content: string) => void;
  deleteMessage: (messageId: string) => void;
  addReaction: (messageId: string, emoji: string) => void;
  setTyping: (isTyping: boolean) => void;
}
```

### Message Notifications

#### Notification System
```typescript
interface MessageNotificationProps {
  orderId: string;
  unreadCount: number;
  lastMessage?: Message;
  onMarkAsRead: () => void;
  onOpenChat: () => void;
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  mentionNotifications: boolean;
  systemMessageNotifications: boolean;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}
```

#### Notification Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔔 New Messages (3)                                                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ Q1 2024 Laptops                                                             │
│ John Smith: "Standard MacBooks can ship this week"                          │
│ 2 minutes ago                                                               │
│                                                                             │
│ Emergency IT Equipment                                                       │
│ System: "Order status changed to Approved"                                  │
│ 1 hour ago                                                                  │
│                                                                             │
│ [Mark All Read] [Open Chat]                                                 │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Message Search and Filtering

#### Search Interface
```typescript
interface MessageSearchProps {
  orderId: string;
  onSearch: (query: string, filters: MessageFilters) => void;
  results: Message[];
  loading?: boolean;
}

interface MessageFilters {
  author?: string;
  messageType?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  hasAttachments?: boolean;
  category?: string;
}
```

### Mobile Chat Interface

#### Mobile Optimization
```typescript
interface MobileChatProps {
  orderId: string;
  isOpen: boolean;
  onClose: () => void;
  fullScreen?: boolean;
}
```

#### Mobile Chat Features
- **Full-screen Chat**: Dedicated chat view
- **Swipe Gestures**: Swipe to reply or react
- **Voice Messages**: Audio message recording
- **Image Capture**: Camera integration for photos
- **Offline Support**: Message queuing when offline
- **Push Notifications**: Real-time mobile notifications

### Message Analytics

#### Analytics Interface
```typescript
interface MessageAnalytics {
  orderId: string;
  totalMessages: number;
  participantActivity: ParticipantActivity[];
  responseTime: {
    average: number;
    median: number;
  };
  messageTypes: Record<string, number>;
  attachmentStats: {
    totalFiles: number;
    totalSize: number;
    fileTypes: Record<string, number>;
  };
}

interface ParticipantActivity {
  userId: string;
  userName: string;
  messageCount: number;
  lastActivity: Date;
  averageResponseTime: number;
}
```

## Mock Data Requirements

### Mock Messages
```typescript
const mockMessages: Message[] = [
  {
    id: 'msg-001',
    orderId: 'order-001',
    authorId: 'system',
    authorName: 'System',
    authorRole: 'system',
    content: 'Order submitted for review',
    messageType: 'system',
    attachments: [],
    mentions: [],
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    readBy: [],
    reactions: [],
    metadata: {
      systemGenerated: true,
      priority: 'medium',
      category: 'general'
    }
  },
  {
    id: 'msg-002',
    orderId: 'order-001',
    authorId: 'user-001',
    authorName: 'Ahmed Al-Rashid',
    authorRole: 'customer',
    content: 'Hi John, this is an urgent request for our AI research lab. We need the equipment by end of month. Can you expedite?',
    messageType: 'text',
    attachments: [
      {
        id: 'att-001',
        name: 'lab_requirements.pdf',
        url: '/files/lab_requirements.pdf',
        type: 'application/pdf',
        size: 2400000
      }
    ],
    mentions: [
      {
        userId: 'vendor-001',
        userName: 'John Smith',
        startIndex: 3,
        endIndex: 7
      }
    ],
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000),
    readBy: [
      {
        userId: 'vendor-001',
        readAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
      }
    ],
    reactions: [],
    metadata: {
      systemGenerated: false,
      priority: 'high',
      category: 'delivery'
    }
  }
  // ... more messages
];
```

### Mock Participants
```typescript
const mockParticipants: Participant[] = [
  {
    id: 'user-001',
    name: 'Ahmed Al-Rashid',
    role: 'customer',
    avatar: '/avatars/ahmed.jpg',
    online: true
  },
  {
    id: 'vendor-001',
    name: 'John Smith',
    role: 'vendor',
    avatar: '/avatars/john.jpg',
    online: true,
    lastSeen: new Date(Date.now() - 30 * 60 * 1000)
  }
];
```

## Acceptance Criteria

1. ✅ Real-time message thread with WebSocket support
2. ✅ Rich message input with formatting options
3. ✅ File attachment system with drag & drop
4. ✅ @Mentions with user autocomplete
5. ✅ Emoji reactions and picker
6. ✅ System-generated messages for order events
7. ✅ Message read receipts and typing indicators
8. ✅ Message search and filtering
9. ✅ Mobile-responsive chat interface
10. ✅ Message notifications and settings
11. ✅ Participant management and online status
12. ✅ Message history and pagination
13. ✅ Offline message queuing
14. ✅ Message analytics and reporting

## Integration Points
- Messages will sync with Odoo chatter system
- File attachments will be stored in cloud storage
- Notifications will integrate with email/SMS services
- Real-time updates via WebSocket connection

## Visual Requirements
- Clean, modern chat interface
- Professional B2B communication design
- Clear message threading and organization
- Responsive design for all devices
- Loading states and error handling

## Notes
- Focus on B2B communication requirements
- Ensure message persistence and reliability
- Support large file attachments
- Implement proper security and permissions
- Optimize for real-time performance
