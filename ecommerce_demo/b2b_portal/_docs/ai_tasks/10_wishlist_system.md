# Task 10: Wishlist Management System

## Objective
Implement a comprehensive wishlist system allowing users to save products for future consideration, organize them into collections, and easily add them to orders.

## Technical Requirements

### Wishlist Data Structure

#### Wishlist Interface
```typescript
interface Wishlist {
  id: string;
  userId: string;
  name: string;
  description?: string;
  isDefault: boolean;
  items: WishlistItem[];
  privacy: 'private' | 'shared' | 'public';
  createdAt: Date;
  updatedAt: Date;
  metadata: WishlistMetadata;
}

interface WishlistItem {
  id: string;
  productId: string;
  variantId?: string;
  addedAt: Date;
  notes?: string;
  priority: 'low' | 'medium' | 'high';
  targetPrice?: number;
  notifyOnPriceChange: boolean;
  tags: string[];
}

interface WishlistMetadata {
  totalItems: number;
  totalValue: number;
  lastActivity: Date;
  shareToken?: string;
  collaborators?: string[];
}
```

### Redux Store Integration

#### Wishlist Slice
```typescript
interface WishlistState {
  wishlists: Wishlist[];
  activeWishlistId: string | null;
  items: Record<string, Product>; // Product cache
  loading: boolean;
  error: string | null;
  filters: WishlistFilters;
}

interface WishlistFilters {
  searchQuery: string;
  priority: string[];
  priceRange: {
    min: number;
    max: number;
  };
  tags: string[];
  sortBy: 'name' | 'price' | 'addedAt' | 'priority';
  sortDirection: 'asc' | 'desc';
}

interface WishlistActions {
  // Wishlist Management
  createWishlist: (name: string, description?: string) => void;
  updateWishlist: (id: string, updates: Partial<Wishlist>) => void;
  deleteWishlist: (id: string) => void;
  setActiveWishlist: (id: string) => void;
  
  // Item Management
  addToWishlist: (productId: string, wishlistId?: string, options?: WishlistItemOptions) => void;
  removeFromWishlist: (productId: string, wishlistId?: string) => void;
  updateWishlistItem: (itemId: string, updates: Partial<WishlistItem>) => void;
  moveItemBetweenWishlists: (itemId: string, fromWishlistId: string, toWishlistId: string) => void;
  
  // Bulk Operations
  addMultipleToWishlist: (productIds: string[], wishlistId?: string) => void;
  removeMultipleFromWishlist: (itemIds: string[], wishlistId: string) => void;
  addWishlistToOrder: (wishlistId: string, orderId: string) => void;
  
  // Filters and Search
  setWishlistFilters: (filters: Partial<WishlistFilters>) => void;
  clearWishlistFilters: () => void;
}

interface WishlistItemOptions {
  notes?: string;
  priority?: 'low' | 'medium' | 'high';
  targetPrice?: number;
  notifyOnPriceChange?: boolean;
  tags?: string[];
}
```

### Wishlist Components

#### Wishlist Button Component
```typescript
interface WishlistButtonProps {
  productId: string;
  variant?: 'icon' | 'button' | 'text';
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  showDropdown?: boolean;
  className?: string;
}
```

#### Wishlist Button Design
```
Icon Variant:     [♡] → [♥] (filled when in wishlist)
Button Variant:   [♡ Add to Wishlist] → [♥ In Wishlist]
Dropdown Variant: [♡ Add to Wishlist ▼]
                  ├─ Default Wishlist
                  ├─ Research Equipment
                  ├─ Future Purchases
                  └─ + Create New List
```

### Wishlist Management Page

#### Wishlist Page Layout
```
WishlistPage
├── WishlistHeader
│   ├── WishlistSelector
│   ├── WishlistActions
│   └── WishlistStats
├── WishlistFilters
│   ├── SearchBar
│   ├── PriorityFilter
│   ├── PriceRangeFilter
│   └── TagFilter
├── WishlistGrid
│   └── WishlistItemCard[]
└── WishlistActions
    ├── BulkActions
    ├── ShareWishlist
    └── ExportWishlist
```

#### Wishlist Header Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ My Wishlists: [Default Wishlist ▼]                    [⚙️] [📤] [+ New List] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 12 items • Total value: SAR 156,750 • Last updated: 2 hours ago            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Wishlist Item Card

#### Item Card Interface
```typescript
interface WishlistItemCardProps {
  item: WishlistItem;
  product: Product;
  onRemove: () => void;
  onAddToOrder: () => void;
  onUpdateNotes: (notes: string) => void;
  onUpdatePriority: (priority: string) => void;
  onSetTargetPrice: (price: number) => void;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
}
```

#### Wishlist Item Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ☐ [Product Image]  MacBook Pro 16" M3 - Apple                              │
│                    SKU: MBP16-M3-512 • Added 3 days ago                    │
│                    Current: SAR 12,999 • Target: SAR 12,000 🔔             │
│                    Priority: ● High  Notes: "For AI research lab"          │
│                                                                             │
│                    [Add to Order] [Edit] [Remove] [Move to ▼]              │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Wishlist Collections

#### Collection Management
```typescript
interface WishlistCollection {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  wishlists: string[];
  createdAt: Date;
}

interface CollectionManagerProps {
  collections: WishlistCollection[];
  onCreateCollection: (collection: Omit<WishlistCollection, 'id' | 'createdAt'>) => void;
  onUpdateCollection: (id: string, updates: Partial<WishlistCollection>) => void;
  onDeleteCollection: (id: string) => void;
}
```

#### Collection Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Collections                                                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 🔬 Research Equipment (3 lists, 45 items)                                  │
│ 💼 Office Setup (2 lists, 23 items)                                        │
│ 🖥️ IT Infrastructure (4 lists, 67 items)                                   │
│ 📚 Educational Materials (1 list, 12 items)                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ + Create New Collection                                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Price Tracking and Notifications

#### Price Alert System
```typescript
interface PriceAlert {
  id: string;
  userId: string;
  productId: string;
  targetPrice: number;
  currentPrice: number;
  alertType: 'below' | 'above' | 'change';
  enabled: boolean;
  createdAt: Date;
  triggeredAt?: Date;
}

interface PriceTrackingProps {
  item: WishlistItem;
  product: Product;
  onSetAlert: (targetPrice: number, alertType: string) => void;
  onRemoveAlert: () => void;
  priceHistory?: PriceHistoryEntry[];
}

interface PriceHistoryEntry {
  price: number;
  date: Date;
  source: string;
}
```

#### Price Alert Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Price Tracking: MacBook Pro 16" M3                                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ Current Price: SAR 12,999                                                  │
│ Your Target: SAR 12,000 (7.7% below current)                              │
│                                                                             │
│ Price History (30 days):                                                   │
│ ╭─────────────────────────────────────────────────────────────────────────╮ │
│ │ 13,500 ┤                                                                │ │
│ │ 13,000 ┤  ●                                                             │ │
│ │ 12,500 ┤    ●─●─●                                                       │ │
│ │ 12,000 ┤         ●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─●─● │ │
│ │        └─────────────────────────────────────────────────────────────── │ │
│ ╰─────────────────────────────────────────────────────────────────────────╯ │
│                                                                             │
│ ☐ Email me when price drops below SAR 12,000                              │
│ ☐ SMS notification for price changes                                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Wishlist Sharing

#### Share Functionality
```typescript
interface WishlistSharingProps {
  wishlist: Wishlist;
  onShare: (shareOptions: ShareOptions) => void;
  onRevokeShare: () => void;
  onUpdatePermissions: (permissions: SharePermissions) => void;
}

interface ShareOptions {
  method: 'link' | 'email' | 'qr';
  permissions: SharePermissions;
  expiresAt?: Date;
  message?: string;
}

interface SharePermissions {
  canView: boolean;
  canComment: boolean;
  canAddItems: boolean;
  canRemoveItems: boolean;
}
```

#### Share Modal Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Share Wishlist: Research Equipment                                      [×] │
├─────────────────────────────────────────────────────────────────────────────┤
│ Share with colleagues or vendors to collaborate on equipment selection     │
│                                                                             │
│ Share Link:                                                                 │
│ [https://portal.kaust.edu.sa/wishlist/shared/abc123...] [📋 Copy]          │
│                                                                             │
│ Permissions:                                                                │
│ ☑ Can view items                                                           │
│ ☑ Can add comments                                                         │
│ ☐ Can add items                                                            │
│ ☐ Can remove items                                                         │
│                                                                             │
│ Expires: [Never ▼]                                                         │
│                                                                             │
│ Send via email:                                                             │
│ [<EMAIL>                                              ]     │
│                                                                             │
│ Message (optional):                                                         │
│ [Please review the equipment list for our new lab setup...          ]     │
│                                                                             │
│                                           [Cancel] [Share Wishlist]        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Bulk Operations

#### Bulk Actions Interface
```typescript
interface WishlistBulkActionsProps {
  selectedItems: string[];
  onAddToOrder: (orderId: string) => void;
  onRemoveItems: () => void;
  onMoveToWishlist: (wishlistId: string) => void;
  onUpdatePriority: (priority: string) => void;
  onExport: (format: 'csv' | 'pdf' | 'excel') => void;
}
```

#### Bulk Actions Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 5 items selected                                                            │
│ [Add to Order ▼] [Move to ▼] [Priority ▼] [Remove] [Export ▼]              │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Mobile Wishlist Interface

#### Mobile Optimization
```typescript
interface MobileWishlistProps {
  wishlists: Wishlist[];
  activeWishlistId: string;
  onWishlistChange: (id: string) => void;
}
```

#### Mobile Features
- **Swipe Actions**: Swipe to add to order or remove
- **Quick Add**: Floating action button for quick add
- **Bottom Sheet**: Wishlist selector as bottom sheet
- **Touch Gestures**: Long press for bulk selection
- **Offline Support**: Cache wishlist data for offline viewing

## Mock Data Requirements

### Mock Wishlists
```typescript
const mockWishlists: Wishlist[] = [
  {
    id: 'wishlist-001',
    userId: 'user-001',
    name: 'Default Wishlist',
    isDefault: true,
    items: [
      {
        id: 'item-001',
        productId: 'mbp-16-m3',
        addedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        notes: 'For AI research lab',
        priority: 'high',
        targetPrice: 12000,
        notifyOnPriceChange: true,
        tags: ['research', 'urgent']
      },
      // ... more items
    ],
    privacy: 'private',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    metadata: {
      totalItems: 12,
      totalValue: 156750,
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000)
    }
  },
  {
    id: 'wishlist-002',
    userId: 'user-001',
    name: 'Research Equipment',
    description: 'Equipment needed for AI research lab setup',
    isDefault: false,
    items: [],
    privacy: 'shared',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    metadata: {
      totalItems: 8,
      totalValue: 89500,
      lastActivity: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      shareToken: 'share-abc123',
      collaborators: ['<EMAIL>']
    }
  }
];
```

### Mock Price History
```typescript
const mockPriceHistory: PriceHistoryEntry[] = [
  {
    price: 13500,
    date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    source: 'vendor'
  },
  {
    price: 12999,
    date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    source: 'vendor'
  },
  // ... more price history
];
```

## Acceptance Criteria

1. ✅ Multiple wishlist management with default list
2. ✅ Add/remove products from wishlist with options
3. ✅ Wishlist item cards with full product information
4. ✅ Priority and notes management for items
5. ✅ Price tracking and alert system
6. ✅ Wishlist sharing with permissions
7. ✅ Bulk operations for multiple items
8. ✅ Search and filtering within wishlists
9. ✅ Add entire wishlist to order
10. ✅ Mobile-responsive wishlist interface
11. ✅ Wishlist collections and organization
12. ✅ Export functionality (CSV, PDF)
13. ✅ Offline wishlist support
14. ✅ Wishlist analytics and insights

## Integration Points
- Wishlist data will sync with user profile
- Price tracking will integrate with product pricing API
- Sharing will use email notification service
- Export will generate formatted documents

## Visual Requirements
- Clean, organized wishlist interface
- Clear product information display
- Intuitive bulk selection and actions
- Professional B2B wishlist design
- Responsive design for all devices

## Notes
- Focus on B2B use cases like project planning
- Support large wishlists with good performance
- Implement proper data persistence
- Ensure wishlist privacy and security
- Optimize for frequent wishlist interactions
