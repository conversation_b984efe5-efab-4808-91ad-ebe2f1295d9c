# Task 04: Product Catalog and Filtering System

## Objective

Implement a comprehensive product catalog with advanced filtering, sorting, and grid/list view options for the B2B marketplace.

stock availability is optional in b2b can sell wihout actuall stock and it be configured
product review is an optional and it be configured

## Technical Requirements

### Product Data Structure

#### Product Interface

```typescript
interface Product {
  id: string;
  sku: string;
  name: string;
  description: string;
  shortDescription?: string;
  category: ProductCategory;
  subcategory?: string;
  brand: string;
  price: number;
  currency: string;
  availability: 'in_stock' | 'out_of_stock' | 'limited' | 'discontinued';// is optional b2b can sell wihout actuall stock
  stockQuantity?: number;
  minimumOrderQuantity: number;
  images: ProductImage[];
  specifications: ProductSpecification[];
  variants?: ProductVariant[];
  tags: string[];
  featured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  parentId?: string;
  level: number;
  imageUrl?: string;
}

interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
}

interface ProductSpecification {
  name: string;
  value: string;
  unit?: string;
  category: string;
}

interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  attributes: Record<string, string>;
  availability: string;
  stockQuantity?: number;
}
```

### Product Catalog Layout

#### Catalog Page Structure

```
ProductCatalogPage
├── FiltersSidebar
│   ├── CategoryFilter
│   ├── BrandFilter
│   ├── PriceRangeFilter
│   ├── AvailabilityFilter
│   └── SpecificationFilters
├── CatalogHeader
│   ├── ResultsCount
│   ├── ViewToggle (Grid/List)
│   ├── SortSelector
│   └── FilterToggle (Mobile)
├── ProductGrid/ProductList
│   └── ProductCard[]
└── Pagination
```

### Filters Sidebar Component

#### Filter Interface

```typescript
interface FilterState {
  categories: string[];
  brands: string[];
  priceRange: {
    min: number;
    max: number;
  };
  availability: string[];
  specifications: Record<string, string[]>;
  searchQuery: string;
}

interface FiltersSidebarProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  categories: ProductCategory[];
  brands: string[];
  priceRange: { min: number; max: number };
  specifications: SpecificationFilter[];
  className?: string;
}

interface SpecificationFilter {
  name: string;
  type: 'select' | 'multiselect' | 'range' | 'boolean';
  options: string[];
  unit?: string;
}
```

#### Category Filter Design

```
┌─────────────────────────────────────┐
│ Categories                          │
├─────────────────────────────────────┤
│ ☐ Laptops (45)                      │
│   ☐ Business Laptops (20)           │
│   ☐ Gaming Laptops (15)             │
│   ☐ Ultrabooks (10)                 │
│ ☐ Phones (32)                       │
│   ☐ Smartphones (28)                │
│   ☐ Feature Phones (4)              │
│ ☐ Electronics (67)                  │
│   ☐ Accessories (45)                │
│   ☐ Components (22)                 │
└─────────────────────────────────────┘
```

#### Brand Filter Design

```
┌─────────────────────────────────────┐
│ Brands                              │
├─────────────────────────────────────┤
│ [Search brands...]                  │
│ ☐ Apple (12)                        │
│ ☐ Dell (18)                         │
│ ☐ HP (15)                           │
│ ☐ Lenovo (22)                       │
│ ☐ Samsung (8)                       │
│ + Show more brands                  │
└─────────────────────────────────────┘
```

#### Price Range Filter Design

```
┌─────────────────────────────────────┐
│ Price Range (SAR)                   │
├─────────────────────────────────────┤
│ Min: [1000    ] Max: [50000    ]    │
│ ●────────●──────────────────────●   │
│ 1K      15K                   50K   │
│                                     │
│ ☐ Under 5,000 SAR (25)              │
│ ☐ 5,000 - 15,000 SAR (45)           │
│ ☐ 15,000 - 30,000 SAR (32)          │
│ ☐ Over 30,000 SAR (18)              │
└─────────────────────────────────────┘
```

### Product Card Component

#### Product Card Interface

```typescript
interface ProductCardProps {
  product: Product;
  view: 'grid' | 'list';
  onAddToOrder?: (product: Product) => void;
  onAddToWishlist?: (product: Product) => void;
  onQuickView?: (product: Product) => void;
  showCompare?: boolean;
  className?: string;
}
```

#### Grid View Card Design

```
┌─────────────────────────────────────┐
│ [Product Image]                     │
│                                     │
│ MacBook Pro 16" M3                  │
│ Apple                               │
│ SAR 12,999                          │
│ ⭐⭐⭐⭐⭐ (4.8) • In Stock          │
│                                     │
│ [Add to Order] [♡] [👁]             │
└─────────────────────────────────────┘
```

#### List View Card Design

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [Image] MacBook Pro 16" M3 - Apple                           SAR 12,999     │
│         High-performance laptop for professional use         ⭐⭐⭐⭐⭐ (4.8)  │
│         • M3 Pro chip • 16GB RAM • 512GB SSD • In Stock     [Add] [♡] [👁] │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Catalog Header Component

#### Catalog Header Interface

```typescript
interface CatalogHeaderProps {
  totalResults: number;
  currentView: 'grid' | 'list';
  onViewChange: (view: 'grid' | 'list') => void;
  sortBy: SortOption;
  onSortChange: (sort: SortOption) => void;
  onToggleFilters?: () => void;
  showFilters?: boolean;
}

interface SortOption {
  field: 'name' | 'price' | 'rating' | 'newest' | 'popularity';
  direction: 'asc' | 'desc';
  label: string;
}
```

#### Catalog Header Design

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 144 products found                    [Grid][List] Sort: [Price: Low to High ▼] │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Product Grid/List Components

#### Product Grid Interface

```typescript
interface ProductGridProps {
  products: Product[];
  loading?: boolean;
  view: 'grid' | 'list';
  columns?: 2 | 3 | 4 | 5;
  onProductClick?: (product: Product) => void;
  onAddToOrder?: (product: Product) => void;
  onAddToWishlist?: (product: Product) => void;
  className?: string;
}
```

#### Grid Layout Responsive Design

```css
/* Grid responsive columns */
.product-grid {
  display: grid;
  gap: 1.5rem;
  
  /* Mobile: 1 column */
  grid-template-columns: 1fr;
  
  /* Tablet: 2 columns */
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Desktop: 3-4 columns */
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (min-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### Pagination Component

#### Pagination Interface

```typescript
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showItemsPerPage?: boolean;
  className?: string;
}
```

#### Pagination Design

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Showing 1-20 of 144 products    [10 ▼] per page    [‹] 1 2 3 ... 8 [›]      │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Search and Filter Logic

#### Filter State Management

```typescript
interface UseProductFiltersReturn {
  filters: FilterState;
  filteredProducts: Product[];
  totalResults: number;
  loading: boolean;
  updateFilter: (key: keyof FilterState, value: any) => void;
  clearFilters: () => void;
  clearFilter: (key: keyof FilterState) => void;
}

// Hook should handle:
// - Filter state management
// - Product filtering logic
// - Debounced search
// - URL parameter synchronization
// - Filter persistence
```

#### Sort Logic Implementation

```typescript
const sortOptions: SortOption[] = [
  { field: 'name', direction: 'asc', label: 'Name: A to Z' },
  { field: 'name', direction: 'desc', label: 'Name: Z to A' },
  { field: 'price', direction: 'asc', label: 'Price: Low to High' },
  { field: 'price', direction: 'desc', label: 'Price: High to Low' },
  { field: 'newest', direction: 'desc', label: 'Newest First' },
  { field: 'popularity', direction: 'desc', label: 'Most Popular' },
  { field: 'rating', direction: 'desc', label: 'Highest Rated' }
];
```

### Loading States and Skeletons

#### Product Card Skeleton

```typescript
interface ProductCardSkeletonProps {
  view: 'grid' | 'list';
  count?: number;
}

// Skeleton should show:
// - Image placeholder
// - Text line placeholders
// - Button placeholders
// - Shimmer animation
// - Proper aspect ratios
```

### Mobile Responsiveness

#### Mobile Filter Modal

```typescript
interface MobileFiltersModalProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
}
```

#### Mobile Catalog Layout

- Collapsible filters in modal/drawer
- Sticky header with filter toggle
- Single column product grid
- Infinite scroll or load more button
- Touch-friendly interactions

## Mock Data Requirements

### Mock Product Categories

```typescript
const mockCategories: ProductCategory[] = [
  {
    id: 'laptops',
    name: 'Laptops',
    slug: 'laptops',
    level: 1,
    imageUrl: '/images/categories/laptops.jpg'
  },
  {
    id: 'business-laptops',
    name: 'Business Laptops',
    slug: 'business-laptops',
    parentId: 'laptops',
    level: 2
  },
  // ... more categories
];
```

### Mock Products Dataset

```typescript
// Generate 100+ mock products across categories:
// - Laptops (Business, Gaming, Ultrabooks)
// - Phones (Smartphones, Feature phones)
// - Electronics (Accessories, Components)
// - Include realistic specifications
// - Varied pricing (1,000 - 50,000 SAR)
// - Different availability states
// - Multiple images per product
```

### Mock Filter Options

```typescript
const mockBrands = ['Apple', 'Dell', 'HP', 'Lenovo', 'Samsung', 'Asus', 'Acer'];
const mockSpecifications = [
  {
    name: 'RAM',
    type: 'multiselect',
    options: ['8GB', '16GB', '32GB', '64GB'],
    unit: 'GB'
  },
  {
    name: 'Storage',
    type: 'multiselect',
    options: ['256GB SSD', '512GB SSD', '1TB SSD', '2TB SSD']
  }
];
```

## Acceptance Criteria

1. ✅ Product catalog page with grid and list views
2. ✅ Comprehensive filters sidebar with all filter types
3. ✅ Category hierarchy with expandable tree
4. ✅ Brand filter with search functionality
5. ✅ Price range filter with slider and presets
6. ✅ Specification filters for technical attributes
7. ✅ Sort functionality with multiple options
8. ✅ Pagination with configurable items per page
9. ✅ Product cards with all required information
10. ✅ Loading states and skeleton screens
11. ✅ Mobile-responsive design with filter modal
12. ✅ Filter state persistence in URL
13. ✅ Clear filters and individual filter removal
14. ✅ Results count and empty state handling

## Integration Points

- Product data will be fetched from Odoo product catalog
- Filter options will be dynamically loaded from backend
- Search functionality will integrate with Elasticsearch
- Product images will be served from CDN

## Visual Requirements

- Clean, professional B2B catalog design
- Consistent spacing and typography
- Hover effects and smooth transitions
- Accessibility-compliant color contrast
- Loading animations and feedback

## Notes

- Focus on performance with large product datasets
- Implement virtual scrolling for better performance
- Support keyboard navigation for accessibility
- Optimize images with lazy loading
- Cache filter options for better UX
