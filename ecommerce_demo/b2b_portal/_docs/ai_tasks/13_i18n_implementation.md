# Task 13: Internationalization (i18n) Implementation

## Objective
Implement comprehensive internationalization support for English and Arabic languages with RTL layout, cultural adaptations, and seamless language switching.

## Technical Requirements

### i18n Architecture

#### Language Context System
```typescript
interface LanguageContextType {
  language: 'en' | 'ar';
  direction: 'ltr' | 'rtl';
  setLanguage: (lang: 'en' | 'ar') => void;
  t: (key: string, params?: Record<string, any>) => string;
  formatNumber: (num: number, options?: Intl.NumberFormatOptions) => string;
  formatCurrency: (amount: number, currency?: string) => string;
  formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => string;
  formatRelativeTime: (date: Date) => string;
}

interface TranslationFunction {
  (key: string): string;
  (key: string, params: Record<string, any>): string;
  (key: string, params: Record<string, any>, fallback: string): string;
}
```

### Translation Structure

#### Translation Keys Organization
```typescript
interface TranslationKeys {
  // Navigation
  nav: {
    home: string;
    products: string;
    orders: string;
    account: string;
    wishlist: string;
    logout: string;
  };
  
  // Common UI Elements
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    save: string;
    delete: string;
    edit: string;
    view: string;
    search: string;
    filter: string;
    sort: string;
    clear: string;
    apply: string;
    close: string;
    back: string;
    next: string;
    previous: string;
    submit: string;
    confirm: string;
  };
  
  // Products
  products: {
    title: string;
    search_placeholder: string;
    no_results: string;
    filters: {
      category: string;
      brand: string;
      price_range: string;
      availability: string;
    };
    card: {
      add_to_order: string;
      add_to_wishlist: string;
      quick_view: string;
      in_stock: string;
      out_of_stock: string;
      limited_stock: string;
    };
  };
  
  // Orders
  orders: {
    title: string;
    create_order: string;
    order_name: string;
    order_status: {
      draft: string;
      submitted: string;
      under_review: string;
      approved: string;
      rejected: string;
      expired: string;
      converted: string;
    };
    actions: {
      submit: string;
      approve: string;
      reject: string;
      convert: string;
      duplicate: string;
    };
  };
  
  // User Interface
  user: {
    profile: string;
    settings: string;
    preferences: string;
    personal_info: string;
    contact_info: string;
    organization: string;
    security: string;
  };
  
  // Notifications
  notifications: {
    title: string;
    mark_all_read: string;
    clear_all: string;
    no_notifications: string;
    settings: string;
  };
  
  // Validation Messages
  validation: {
    required: string;
    email_invalid: string;
    phone_invalid: string;
    min_length: string;
    max_length: string;
    numeric_only: string;
  };
  
  // Date and Time
  datetime: {
    now: string;
    today: string;
    yesterday: string;
    tomorrow: string;
    days_ago: string;
    hours_ago: string;
    minutes_ago: string;
    just_now: string;
  };
}
```

### Translation Files

#### English Translations (en.json)
```json
{
  "nav": {
    "home": "Home",
    "products": "Products",
    "orders": "Orders",
    "account": "Account",
    "wishlist": "Wishlist",
    "logout": "Logout"
  },
  "common": {
    "loading": "Loading...",
    "error": "Error",
    "success": "Success",
    "cancel": "Cancel",
    "save": "Save",
    "delete": "Delete",
    "edit": "Edit",
    "view": "View",
    "search": "Search",
    "filter": "Filter",
    "sort": "Sort",
    "clear": "Clear",
    "apply": "Apply",
    "close": "Close",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "submit": "Submit",
    "confirm": "Confirm"
  },
  "products": {
    "title": "Products",
    "search_placeholder": "Search products, brands, categories...",
    "no_results": "No products found",
    "filters": {
      "category": "Category",
      "brand": "Brand",
      "price_range": "Price Range",
      "availability": "Availability"
    },
    "card": {
      "add_to_order": "Add to Order",
      "add_to_wishlist": "Add to Wishlist",
      "quick_view": "Quick View",
      "in_stock": "In Stock",
      "out_of_stock": "Out of Stock",
      "limited_stock": "Limited Stock"
    }
  },
  "orders": {
    "title": "Orders",
    "create_order": "Create New Order",
    "order_name": "Order Name",
    "order_status": {
      "draft": "Draft",
      "submitted": "Submitted",
      "under_review": "Under Review",
      "approved": "Approved",
      "rejected": "Rejected",
      "expired": "Expired",
      "converted": "Order Created"
    },
    "actions": {
      "submit": "Submit for Review",
      "approve": "Approve",
      "reject": "Reject",
      "convert": "Convert to Order",
      "duplicate": "Duplicate"
    }
  }
}
```

#### Arabic Translations (ar.json)
```json
{
  "nav": {
    "home": "الرئيسية",
    "products": "المنتجات",
    "orders": "الطلبات",
    "account": "الحساب",
    "wishlist": "المفضلة",
    "logout": "تسجيل الخروج"
  },
  "common": {
    "loading": "جاري التحميل...",
    "error": "خطأ",
    "success": "نجح",
    "cancel": "إلغاء",
    "save": "حفظ",
    "delete": "حذف",
    "edit": "تعديل",
    "view": "عرض",
    "search": "بحث",
    "filter": "تصفية",
    "sort": "ترتيب",
    "clear": "مسح",
    "apply": "تطبيق",
    "close": "إغلاق",
    "back": "رجوع",
    "next": "التالي",
    "previous": "السابق",
    "submit": "إرسال",
    "confirm": "تأكيد"
  },
  "products": {
    "title": "المنتجات",
    "search_placeholder": "البحث في المنتجات والعلامات التجارية والفئات...",
    "no_results": "لم يتم العثور على منتجات",
    "filters": {
      "category": "الفئة",
      "brand": "العلامة التجارية",
      "price_range": "نطاق السعر",
      "availability": "التوفر"
    },
    "card": {
      "add_to_order": "إضافة للطلب",
      "add_to_wishlist": "إضافة للمفضلة",
      "quick_view": "عرض سريع",
      "in_stock": "متوفر",
      "out_of_stock": "غير متوفر",
      "limited_stock": "مخزون محدود"
    }
  },
  "orders": {
    "title": "الطلبات",
    "create_order": "إنشاء طلب جديد",
    "order_name": "اسم الطلب",
    "order_status": {
      "draft": "مسودة",
      "submitted": "مُرسل",
      "under_review": "قيد المراجعة",
      "approved": "موافق عليه",
      "rejected": "مرفوض",
      "expired": "منتهي الصلاحية",
      "converted": "تم إنشاء الطلب"
    },
    "actions": {
      "submit": "إرسال للمراجعة",
      "approve": "موافقة",
      "reject": "رفض",
      "convert": "تحويل لطلب",
      "duplicate": "نسخ"
    }
  }
}
```

### RTL Layout Support

#### CSS RTL Implementation
```css
/* Base RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* Flexbox RTL */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

/* Spacing RTL */
[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* Border RTL */
[dir="rtl"] .border-l {
  border-left: none;
  border-right: 1px solid;
}

[dir="rtl"] .border-r {
  border-right: none;
  border-left: 1px solid;
}

/* Text Alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Icons RTL - Flip directional icons */
[dir="rtl"] .icon-arrow-left {
  transform: scaleX(-1);
}

[dir="rtl"] .icon-arrow-right {
  transform: scaleX(-1);
}

/* Form Elements RTL */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
}

/* Navigation RTL */
[dir="rtl"] .breadcrumb {
  flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-separator::before {
  content: "‹";
}

/* Tables RTL */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}

/* Dropdown RTL */
[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}
```

### Language Toggle Component

#### Language Toggle Interface
```typescript
interface LanguageToggleProps {
  variant?: 'button' | 'dropdown' | 'flag';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  showFlag?: boolean;
  className?: string;
}
```

#### Language Toggle Design
```
Button Variant:    [EN] ↔ [AR]
Dropdown Variant:  [🇺🇸 English ▼]
                   ├─ 🇺🇸 English
                   └─ 🇸🇦 العربية
Flag Variant:      [🇺🇸] ↔ [🇸🇦]
```

### Number and Currency Formatting

#### Formatting Functions
```typescript
interface NumberFormatting {
  formatNumber: (num: number, locale: string, options?: Intl.NumberFormatOptions) => string;
  formatCurrency: (amount: number, currency: string, locale: string) => string;
  formatPercentage: (value: number, locale: string) => string;
  parseNumber: (str: string, locale: string) => number;
}

// Examples:
// English: 1,234.56 SAR
// Arabic: ١٬٢٣٤٫٥٦ ر.س

const formatCurrency = (amount: number, currency: string, locale: string): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    currencyDisplay: 'symbol'
  }).format(amount);
};

const formatNumber = (num: number, locale: string): string => {
  return new Intl.NumberFormat(locale).format(num);
};
```

### Date and Time Formatting

#### Date Formatting
```typescript
interface DateFormatting {
  formatDate: (date: Date, locale: string, options?: Intl.DateTimeFormatOptions) => string;
  formatTime: (date: Date, locale: string) => string;
  formatDateTime: (date: Date, locale: string) => string;
  formatRelativeTime: (date: Date, locale: string) => string;
  getCalendarType: (locale: string) => 'gregorian' | 'islamic';
}

// Examples:
// English: March 15, 2024 at 2:30 PM
// Arabic: ١٥ مارس ٢٠٢٤ في ٢:٣٠ م

const formatDate = (date: Date, locale: string): string => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

const formatRelativeTime = (date: Date, locale: string): string => {
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
  const diffInSeconds = (date.getTime() - Date.now()) / 1000;
  
  if (Math.abs(diffInSeconds) < 60) {
    return rtf.format(Math.round(diffInSeconds), 'second');
  } else if (Math.abs(diffInSeconds) < 3600) {
    return rtf.format(Math.round(diffInSeconds / 60), 'minute');
  } else if (Math.abs(diffInSeconds) < 86400) {
    return rtf.format(Math.round(diffInSeconds / 3600), 'hour');
  } else {
    return rtf.format(Math.round(diffInSeconds / 86400), 'day');
  }
};
```

### Cultural Adaptations

#### Cultural Considerations
```typescript
interface CulturalAdaptations {
  // Number Systems
  numberSystem: 'latin' | 'arabic-indic'; // 0123456789 vs ٠١٢٣٤٥٦٧٨٩
  
  // Calendar Systems
  calendar: 'gregorian' | 'islamic';
  
  // Week Start
  weekStart: 'sunday' | 'monday' | 'saturday';
  
  // Business Hours
  businessHours: {
    start: string;
    end: string;
    breakStart?: string;
    breakEnd?: string;
  };
  
  // Holidays and Working Days
  workingDays: number[]; // 0 = Sunday, 1 = Monday, etc.
  holidays: Date[];
  
  // Address Format
  addressFormat: string[];
  
  // Phone Number Format
  phoneFormat: string;
}

const culturalSettings = {
  en: {
    numberSystem: 'latin',
    calendar: 'gregorian',
    weekStart: 'sunday',
    businessHours: { start: '08:00', end: '17:00' },
    workingDays: [0, 1, 2, 3, 4], // Sunday to Thursday
    addressFormat: ['street', 'city', 'state', 'country', 'postal']
  },
  ar: {
    numberSystem: 'arabic-indic',
    calendar: 'islamic',
    weekStart: 'saturday',
    businessHours: { start: '08:00', end: '17:00', breakStart: '12:00', breakEnd: '13:00' },
    workingDays: [6, 0, 1, 2, 3], // Saturday to Wednesday
    addressFormat: ['country', 'city', 'district', 'street', 'building']
  }
};
```

### Translation Management

#### Translation Hook
```typescript
interface UseTranslationReturn {
  t: TranslationFunction;
  language: 'en' | 'ar';
  direction: 'ltr' | 'rtl';
  setLanguage: (lang: 'en' | 'ar') => void;
  isLoading: boolean;
  error: string | null;
}

const useTranslation = (): UseTranslationReturn => {
  const { language, setLanguage } = useLanguageContext();
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const t: TranslationFunction = useCallback((key: string, params?: Record<string, any>, fallback?: string) => {
    let translation = translations[key] || fallback || key;
    
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, String(value));
      });
    }
    
    return translation;
  }, [translations]);

  return {
    t,
    language,
    direction: language === 'ar' ? 'rtl' : 'ltr',
    setLanguage,
    isLoading,
    error
  };
};
```

### Form Validation i18n

#### Validation Messages
```typescript
interface ValidationMessages {
  required: (field: string) => string;
  email: () => string;
  minLength: (field: string, min: number) => string;
  maxLength: (field: string, max: number) => string;
  pattern: (field: string, pattern: string) => string;
}

const validationMessages = {
  en: {
    required: (field: string) => `${field} is required`,
    email: () => 'Please enter a valid email address',
    minLength: (field: string, min: number) => `${field} must be at least ${min} characters`,
    maxLength: (field: string, max: number) => `${field} must not exceed ${max} characters`,
    pattern: (field: string, pattern: string) => `${field} format is invalid`
  },
  ar: {
    required: (field: string) => `${field} مطلوب`,
    email: () => 'يرجى إدخال عنوان بريد إلكتروني صحيح',
    minLength: (field: string, min: number) => `${field} يجب أن يكون على الأقل ${min} أحرف`,
    maxLength: (field: string, max: number) => `${field} يجب ألا يتجاوز ${max} أحرف`,
    pattern: (field: string, pattern: string) => `تنسيق ${field} غير صحيح`
  }
};
```

### SEO and Meta Tags i18n

#### Meta Tags Management
```typescript
interface MetaTagsI18n {
  title: Record<string, string>;
  description: Record<string, string>;
  keywords: Record<string, string[]>;
  ogTitle: Record<string, string>;
  ogDescription: Record<string, string>;
}

const metaTags: MetaTagsI18n = {
  title: {
    en: 'KAUST B2B Marketplace - Procurement Portal',
    ar: 'سوق جامعة الملك عبدالله للأعمال - بوابة المشتريات'
  },
  description: {
    en: 'Professional B2B procurement platform for KAUST university',
    ar: 'منصة مشتريات احترافية للأعمال لجامعة الملك عبدالله'
  },
  keywords: {
    en: ['B2B', 'procurement', 'KAUST', 'marketplace', 'university'],
    ar: ['أعمال', 'مشتريات', 'جامعة الملك عبدالله', 'سوق', 'جامعة']
  }
};
```

## Mock Data Requirements

### Mock Translation Data
```typescript
const mockTranslations = {
  en: {
    // Complete English translation object
  },
  ar: {
    // Complete Arabic translation object
  }
};
```

### Mock Cultural Settings
```typescript
const mockCulturalSettings = {
  en: {
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    currency: 'SAR',
    numberFormat: 'en-US'
  },
  ar: {
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '12h',
    currency: 'SAR',
    numberFormat: 'ar-SA'
  }
};
```

## Acceptance Criteria

1. ✅ Complete English and Arabic translation support
2. ✅ RTL layout implementation for Arabic
3. ✅ Language toggle component with persistence
4. ✅ Number and currency formatting for both locales
5. ✅ Date and time formatting with cultural adaptations
6. ✅ Form validation messages in both languages
7. ✅ SEO meta tags internationalization
8. ✅ Cultural adaptations (calendar, business hours)
9. ✅ Translation management system
10. ✅ Fallback handling for missing translations
11. ✅ Dynamic language switching without page reload
12. ✅ Proper text direction handling in all components

## Integration Points
- Translation files will be loaded from CDN or API
- Language preference will sync with user profile
- Cultural settings will integrate with business logic
- SEO meta tags will be managed by Next.js head

## Visual Requirements
- Seamless RTL/LTR layout transitions
- Proper Arabic typography and fonts
- Cultural color and design adaptations
- Professional bilingual interface
- Consistent spacing in both directions

## Notes
- Focus on Saudi Arabian cultural context
- Ensure proper Arabic text rendering
- Support both Western and Arabic-Indic numerals
- Implement proper text truncation for RTL
- Optimize font loading for Arabic text
