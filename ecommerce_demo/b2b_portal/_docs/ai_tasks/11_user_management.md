# Task 11: User Management and Authentication

## Objective
Implement comprehensive user management with Nafath authentication integration, user profiles, role-based access control, and B2B organization management.

## Technical Requirements

### User Data Structure

#### User Interface
```typescript
interface User {
  id: string;
  nationalId: string; // Saudi National ID (Primary Key)
  nafathVerified: boolean;
  
  // Personal Information
  personalInfo: {
    fullNameArabic: string;
    fullNameEnglish: string;
    dateOfBirth: Date;
    gender: 'male' | 'female';
    nationality: string;
  };
  
  // Contact Information
  contactInfo: {
    email: string;
    mobilePhone: string;
    officePhone?: string;
    departmentAddress?: string;
  };
  
  // Organization Context
  organizationInfo: {
    customerId: string;
    customerName: string;
    department: string;
    jobTitle: string;
    managerId?: string;
    budgetAuthority: number;
  };
  
  // System Information
  systemInfo: {
    roles: UserRole[];
    permissions: Permission[];
    status: UserStatus;
    createdAt: Date;
    lastLoginAt?: Date;
    approvedBy?: string;
    approvedAt?: Date;
  };
  
  // Preferences
  preferences: UserPreferences;
  
  // Profile
  profile: {
    avatar?: string;
    bio?: string;
    timezone: string;
    language: 'en' | 'ar';
    theme: 'light' | 'dark';
  };
}

interface UserRole {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  level: number;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

type UserStatus = 
  | 'pending'      // User submitted request
  | 'more_info'    // Additional documentation needed
  | 'approved'     // Admin approved access
  | 'rejected'     // Admin rejected request
  | 'active'       // Full portal access
  | 'suspended'    // Temporary access restriction
  | 'deactivated'; // Account permanently disabled

interface UserPreferences {
  notifications: NotificationPreferences;
  dashboard: DashboardPreferences;
  ordering: OrderingPreferences;
}
```

### Authentication System

#### Nafath Integration
```typescript
interface NafathAuthProvider {
  initiateAuth: () => Promise<NafathAuthResponse>;
  verifyToken: (token: string) => Promise<NafathUserData>;
  refreshToken: (refreshToken: string) => Promise<NafathAuthResponse>;
  logout: () => Promise<void>;
}

interface NafathAuthResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
  userData: NafathUserData;
}

interface NafathUserData {
  nationalId: string;
  fullNameArabic: string;
  fullNameEnglish: string;
  dateOfBirth: Date;
  gender: string;
  nationality: string;
  verified: boolean;
}
```

#### Authentication Hook
```typescript
interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Authentication methods
  loginWithNafath: () => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  
  // User management
  updateProfile: (updates: Partial<User>) => Promise<void>;
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>;
  
  // Permissions
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  canAccess: (resource: string, action: string) => boolean;
}
```

### User Registration Flow

#### Registration Process
```typescript
interface UserRegistrationFlow {
  step: 'nafath' | 'profile' | 'organization' | 'approval' | 'complete';
  data: Partial<User>;
  errors: Record<string, string>;
  isSubmitting: boolean;
}

interface RegistrationStepProps {
  data: Partial<User>;
  onNext: (stepData: any) => void;
  onBack: () => void;
  onCancel: () => void;
}
```

#### Registration Steps Design

**Step 1: Nafath Verification**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Welcome to KAUST B2B Marketplace                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ To access the portal, please verify your identity using Nafath             │
│                                                                             │
│ [🇸🇦 Verify with Nafath]                                                   │
│                                                                             │
│ Nafath provides secure government-backed identity verification              │
│ • National ID verification                                                  │
│ • Multi-factor authentication                                               │
│ • Secure session management                                                 │
└─────────────────────────────────────────────────────────────────────────────┘
```

**Step 2: Profile Information**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Complete Your Profile                                              Step 2/4 │
├─────────────────────────────────────────────────────────────────────────────┤
│ Contact Information:                                                        │
│ Email: [<EMAIL>                               ]        │
│ Mobile: [+966 50 123 4567                                         ]        │
│ Office Phone: [+966 12 808 1234                                   ]        │
│                                                                             │
│ Address:                                                                    │
│ Department: [Computer Science Department, Building 1              ]        │
│                                                                             │
│ Preferences:                                                                │
│ Language: ● English  ○ Arabic                                              │
│ Theme: ● Light  ○ Dark                                                     │
│                                                                             │
│                                           [Back] [Continue]                │
└─────────────────────────────────────────────────────────────────────────────┘
```

**Step 3: Organization Details**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Organization Information                                           Step 3/4 │
├─────────────────────────────────────────────────────────────────────────────┤
│ Department: [Computer Science ▼]                                           │
│ Job Title: [Research Scientist                                    ]        │
│ Manager/Supervisor: [Dr. Sarah Johnson ▼]                                  │
│                                                                             │
│ Purchasing Authority:                                                       │
│ Budget Limit: [SAR] [50,000                    ] per order                 │
│                                                                             │
│ Role Request:                                                               │
│ ● Purchasing Representative - Create and manage orders                     │
│ ○ Department Manager - Approve orders and manage users                     │
│ ○ End User - Submit purchase requests                                      │
│                                                                             │
│                                           [Back] [Submit Request]          │
└─────────────────────────────────────────────────────────────────────────────┘
```

### User Profile Management

#### Profile Page Layout
```
UserProfilePage
├── ProfileHeader
│   ├── UserAvatar
│   ├── UserInfo
│   └── ProfileActions
├── ProfileTabs
│   ├── PersonalInfoTab
│   ├── ContactInfoTab
│   ├── OrganizationTab
│   ├── PreferencesTab
│   └── SecurityTab
└── ActivityFeed
```

#### Profile Header Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [Avatar] Dr. Ahmed Al-Rashid                                    [Edit Profile] │
│          Computer Science Department • Research Scientist                   │
│          <EMAIL> • +966 50 123 4567                    │
│          Member since Jan 2024 • Last login: 2 hours ago                   │
│                                                                             │
│ Roles: Purchasing Representative • Budget Authority: SAR 50,000             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Role-Based Access Control

#### Permission System
```typescript
interface PermissionMatrix {
  [role: string]: {
    [resource: string]: string[]; // actions
  };
}

const rolePermissions: PermissionMatrix = {
  'company_administrator': {
    'users': ['create', 'read', 'update', 'delete', 'approve'],
    'orders': ['create', 'read', 'update', 'delete', 'approve'],
    'contracts': ['read', 'update'],
    'analytics': ['read', 'export']
  },
  'department_manager': {
    'users': ['read', 'approve'],
    'orders': ['create', 'read', 'update', 'approve'],
    'analytics': ['read']
  },
  'purchasing_representative': {
    'orders': ['create', 'read', 'update'],
    'products': ['read'],
    'wishlist': ['create', 'read', 'update', 'delete']
  },
  'end_user': {
    'orders': ['create', 'read'],
    'products': ['read'],
    'wishlist': ['create', 'read', 'update', 'delete']
  }
};
```

#### Permission Guard Component
```typescript
interface PermissionGuardProps {
  permission: string;
  resource?: string;
  action?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

// Usage example:
// <PermissionGuard permission="orders.approve">
//   <ApproveOrderButton />
// </PermissionGuard>
```

### User Approval Workflow

#### Approval Interface
```typescript
interface UserApprovalProps {
  pendingUsers: User[];
  onApproveUser: (userId: string, approvalData: ApprovalData) => void;
  onRejectUser: (userId: string, reason: string) => void;
  onRequestMoreInfo: (userId: string, message: string) => void;
}

interface ApprovalData {
  roles: string[];
  budgetAuthority: number;
  permissions: string[];
  notes?: string;
}
```

#### Approval Dashboard Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Pending User Approvals (3)                                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ Dr. Ahmed Al-Rashid                                            Submitted 2d │
│ Computer Science • Research Scientist                                       │
│ Requested: Purchasing Representative (SAR 50,000 budget)                    │
│ [👁 Review] [✅ Approve] [❌ Reject] [📝 Request Info]                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ Sarah Johnson                                                   Submitted 1d │
│ Engineering • Department Manager                                             │
│ Requested: Department Manager (SAR 200,000 budget)                          │
│ [👁 Review] [✅ Approve] [❌ Reject] [📝 Request Info]                      │
└─────────────────────────────────────────────────────────────────────────────┘
```

### User Preferences

#### Preferences Interface
```typescript
interface UserPreferences {
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    orderUpdates: boolean;
    priceAlerts: boolean;
    systemMessages: boolean;
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
  
  dashboard: {
    defaultView: 'grid' | 'list';
    itemsPerPage: number;
    showWelcomeMessage: boolean;
    widgets: string[];
  };
  
  ordering: {
    defaultOrderName: string;
    autoSaveInterval: number;
    confirmBeforeSubmit: boolean;
    defaultPriority: 'low' | 'medium' | 'high';
  };
}
```

### Security Features

#### Security Settings
```typescript
interface SecuritySettings {
  twoFactorAuth: {
    enabled: boolean;
    method: 'sms' | 'email' | 'app';
    backupCodes: string[];
  };
  
  sessions: {
    current: UserSession[];
    maxConcurrent: number;
    timeoutMinutes: number;
  };
  
  loginHistory: LoginHistoryEntry[];
  
  privacy: {
    profileVisibility: 'private' | 'organization' | 'public';
    activityTracking: boolean;
    dataRetention: number; // days
  };
}

interface UserSession {
  id: string;
  deviceInfo: string;
  ipAddress: string;
  location: string;
  createdAt: Date;
  lastActivity: Date;
  current: boolean;
}
```

### Mobile User Management

#### Mobile Profile Interface
```typescript
interface MobileUserProfileProps {
  user: User;
  onUpdateProfile: (updates: Partial<User>) => void;
}
```

#### Mobile Features
- **Touch ID/Face ID**: Biometric authentication
- **Quick Settings**: Essential preferences access
- **Offline Profile**: Cached profile data
- **Push Notifications**: Real-time updates
- **Profile Photo**: Camera integration for avatar

## Mock Data Requirements

### Mock User Data
```typescript
const mockUser: User = {
  id: 'user-001',
  nationalId: '1234567890',
  nafathVerified: true,
  personalInfo: {
    fullNameArabic: 'أحمد الراشد',
    fullNameEnglish: 'Ahmed Al-Rashid',
    dateOfBirth: new Date('1985-03-15'),
    gender: 'male',
    nationality: 'Saudi'
  },
  contactInfo: {
    email: '<EMAIL>',
    mobilePhone: '+966501234567',
    officePhone: '+966128081234',
    departmentAddress: 'Computer Science Department, Building 1'
  },
  organizationInfo: {
    customerId: 'kaust',
    customerName: 'King Abdullah University of Science and Technology',
    department: 'Computer Science',
    jobTitle: 'Research Scientist',
    budgetAuthority: 50000
  },
  systemInfo: {
    roles: [
      {
        id: 'purchasing_rep',
        name: 'Purchasing Representative',
        description: 'Can create and manage orders',
        permissions: ['orders.create', 'orders.read', 'orders.update'],
        level: 2
      }
    ],
    permissions: [],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    approvedBy: 'admin-001',
    approvedAt: new Date('2024-01-16')
  },
  preferences: {
    notifications: {
      email: true,
      sms: false,
      push: true,
      orderUpdates: true,
      priceAlerts: true,
      systemMessages: false,
      quietHours: {
        enabled: true,
        start: '22:00',
        end: '08:00'
      }
    },
    dashboard: {
      defaultView: 'grid',
      itemsPerPage: 20,
      showWelcomeMessage: true,
      widgets: ['recent_orders', 'wishlist', 'notifications']
    },
    ordering: {
      defaultOrderName: 'New Order',
      autoSaveInterval: 30,
      confirmBeforeSubmit: true,
      defaultPriority: 'medium'
    }
  },
  profile: {
    avatar: '/avatars/ahmed.jpg',
    bio: 'Research Scientist specializing in AI and Machine Learning',
    timezone: 'Asia/Riyadh',
    language: 'en',
    theme: 'light'
  }
};
```

### Mock Pending Users
```typescript
const mockPendingUsers: User[] = [
  // Users awaiting approval with different statuses
];
```

## Acceptance Criteria

1. ✅ Nafath authentication integration (mocked)
2. ✅ User registration flow with multi-step process
3. ✅ Comprehensive user profile management
4. ✅ Role-based access control system
5. ✅ User approval workflow for administrators
6. ✅ User preferences and settings management
7. ✅ Security settings and session management
8. ✅ Permission guard components
9. ✅ Mobile-responsive user interface
10. ✅ User activity tracking and history
11. ✅ Profile photo upload and management
12. ✅ Multi-language support for user interface

## Integration Points
- Nafath authentication will integrate with Saudi government services
- User data will sync with Odoo partner records
- Permissions will integrate with backend authorization
- Profile photos will be stored in cloud storage

## Visual Requirements
- Professional B2B user management interface
- Clear role and permission indicators
- Intuitive profile editing experience
- Responsive design for all devices
- Accessibility compliance for all users

## Notes
- Focus on B2B organization structure
- Implement proper security measures
- Support complex approval workflows
- Ensure data privacy compliance
- Optimize for enterprise user management
