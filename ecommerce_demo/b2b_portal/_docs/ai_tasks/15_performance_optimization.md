# Task 15: Performance Optimization and Loading States

## Objective
Implement comprehensive performance optimizations, loading states, error handling, and user feedback systems to ensure fast and responsive user experience.

## Technical Requirements

### Loading State Management

#### Loading State Types
```typescript
interface LoadingState {
  type: 'initial' | 'loading' | 'success' | 'error' | 'empty';
  message?: string;
  progress?: number;
  retryable?: boolean;
}

interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch?: Date;
  stale?: boolean;
}

interface PageLoadingState {
  isLoading: boolean;
  loadingText: string;
  progress: number;
  stage: 'initializing' | 'fetching' | 'processing' | 'rendering' | 'complete';
}
```

#### Loading Components
```typescript
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: string;
  text?: string;
  overlay?: boolean;
  className?: string;
}

interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  variant?: 'text' | 'rectangular' | 'circular';
  animation?: 'pulse' | 'wave' | 'none';
  count?: number;
  className?: string;
}

interface ProgressBarProps {
  value: number;
  max?: number;
  variant?: 'linear' | 'circular';
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  showLabel?: boolean;
  indeterminate?: boolean;
}
```

### Skeleton Loading Screens

#### Product Card Skeleton
```typescript
interface ProductCardSkeletonProps {
  count?: number;
  view?: 'grid' | 'list';
}
```

#### Skeleton Design
```
Grid View Skeleton:
┌─────────────────────────────────────┐
│ ████████████████████████████████    │ ← Image placeholder
│ ████████████████████████████████    │
│ ████████████████████████████████    │
│                                     │
│ ████████████████████                │ ← Title placeholder
│ ████████████                        │ ← Brand placeholder
│ ████████                            │ ← Price placeholder
│ ████████████████████████            │ ← Rating placeholder
│                                     │
│ ████████████ ██ ██                  │ ← Buttons placeholder
└─────────────────────────────────────┘

List View Skeleton:
┌─────────────────────────────────────────────────────────────────────────────┐
│ ████████ ████████████████████████████████████████████████████ ████████████ │
│ ████████ ████████████████████████████████████████████████████ ████████████ │
│ ████████ ████████████████████████████████████████████████████ ████████████ │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### Order List Skeleton
```typescript
interface OrderListSkeletonProps {
  count?: number;
}
```

#### Order Skeleton Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ████████████████████    ██    ████████████    ████████    ████████    ███   │
│ ████████████████████    ██    ████████████    ████████    ████████    ███   │
│ ████████████████████    ██    ████████████    ████████    ████████    ███   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Image Optimization

#### Optimized Image Component
```typescript
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  fallback?: string;
  className?: string;
}
```

#### Image Loading States
```typescript
interface ImageWithLoadingProps extends OptimizedImageProps {
  showSkeleton?: boolean;
  skeletonClassName?: string;
}

// Component should handle:
// - Progressive loading with blur placeholder
// - Lazy loading for off-screen images
// - Error fallback images
// - Loading skeleton while image loads
// - WebP format with fallbacks
// - Responsive image sizes
```

### Data Fetching Optimization

#### Caching Strategy
```typescript
interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  staleWhileRevalidate: boolean;
  maxAge: number;
  revalidateOnFocus: boolean;
  revalidateOnReconnect: boolean;
  dedupingInterval: number;
}

interface UseCachedDataReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  mutate: (data?: T) => void;
  revalidate: () => void;
  isValidating: boolean;
  isStale: boolean;
}

const useCachedData = <T>(
  key: string,
  fetcher: () => Promise<T>,
  config?: CacheConfig
): UseCachedDataReturn<T> => {
  // Implementation with SWR-like behavior
  // - Cache responses in memory/localStorage
  // - Stale-while-revalidate pattern
  // - Background revalidation
  // - Deduplication of requests
  // - Error retry with exponential backoff
};
```

#### Prefetching Strategy
```typescript
interface PrefetchManager {
  prefetchRoute: (route: string) => void;
  prefetchData: (key: string, fetcher: () => Promise<any>) => void;
  prefetchImage: (src: string) => void;
  prefetchOnHover: (element: HTMLElement, route: string) => void;
  prefetchOnVisible: (element: HTMLElement, data: () => Promise<any>) => void;
}

// Prefetch strategies:
// - Prefetch next page data on current page load
// - Prefetch on link hover
// - Prefetch on element visibility
// - Prefetch critical images
// - Prefetch user's likely next actions
```

### Virtual Scrolling

#### Virtual List Component
```typescript
interface VirtualListProps<T> {
  items: T[];
  itemHeight: number | ((index: number) => number);
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

// Features:
// - Render only visible items
// - Dynamic item heights
// - Smooth scrolling
// - Infinite loading
// - Memory efficient for large lists
// - Keyboard navigation support
```

#### Virtual Grid Component
```typescript
interface VirtualGridProps<T> {
  items: T[];
  itemWidth: number;
  itemHeight: number;
  containerWidth: number;
  containerHeight: number;
  columns: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  gap?: number;
  overscan?: number;
}
```

### Code Splitting and Lazy Loading

#### Route-Based Code Splitting
```typescript
// Lazy load page components
const ProductsPage = lazy(() => import('../pages/ProductsPage'));
const OrdersPage = lazy(() => import('../pages/OrdersPage'));
const AccountPage = lazy(() => import('../pages/AccountPage'));

// Component-based code splitting
const ProductDetail = lazy(() => import('../components/ProductDetail'));
const OrderDetail = lazy(() => import('../components/OrderDetail'));

// Feature-based code splitting
const AdvancedFilters = lazy(() => import('../components/AdvancedFilters'));
const ReportingDashboard = lazy(() => import('../components/ReportingDashboard'));
```

#### Lazy Loading Wrapper
```typescript
interface LazyComponentProps {
  fallback?: React.ReactNode;
  error?: React.ReactNode;
  retry?: boolean;
  children: React.ReactNode;
}

const LazyComponent: React.FC<LazyComponentProps> = ({
  fallback = <LoadingSpinner />,
  error = <ErrorBoundary />,
  retry = true,
  children
}) => {
  return (
    <Suspense fallback={fallback}>
      <ErrorBoundary fallback={error} retry={retry}>
        {children}
      </ErrorBoundary>
    </Suspense>
  );
};
```

### Error Handling

#### Error Boundary Component
```typescript
interface ErrorBoundaryProps {
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  retry?: boolean;
  children: React.ReactNode;
}

interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  retry?: () => void;
}

interface ErrorState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}
```

#### Error Fallback Components
```typescript
interface ErrorFallbackVariants {
  NetworkError: React.FC<ErrorFallbackProps>;
  NotFound: React.FC<ErrorFallbackProps>;
  ServerError: React.FC<ErrorFallbackProps>;
  ValidationError: React.FC<ErrorFallbackProps>;
  GenericError: React.FC<ErrorFallbackProps>;
}
```

#### Error Fallback Design
```
Network Error:
┌─────────────────────────────────────┐
│ 🌐 Connection Problem               │
│                                     │
│ Please check your internet         │
│ connection and try again.           │
│                                     │
│ [Retry] [Go Offline]                │
└─────────────────────────────────────┘

Not Found:
┌─────────────────────────────────────┐
│ 🔍 Page Not Found                   │
│                                     │
│ The page you're looking for         │
│ doesn't exist or has been moved.    │
│                                     │
│ [Go Home] [Go Back]                 │
└─────────────────────────────────────┘

Server Error:
┌─────────────────────────────────────┐
│ ⚠️ Something Went Wrong             │
│                                     │
│ We're experiencing technical        │
│ difficulties. Please try again.     │
│                                     │
│ [Retry] [Report Issue]              │
└─────────────────────────────────────┘
```

### Performance Monitoring

#### Performance Metrics
```typescript
interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  
  // Loading Performance
  ttfb: number; // Time to First Byte
  fcp: number;  // First Contentful Paint
  tti: number;  // Time to Interactive
  
  // Custom Metrics
  pageLoadTime: number;
  apiResponseTime: number;
  renderTime: number;
  bundleSize: number;
  
  // User Experience
  errorRate: number;
  bounceRate: number;
  sessionDuration: number;
}

interface PerformanceMonitor {
  trackPageLoad: (route: string) => void;
  trackApiCall: (endpoint: string, duration: number) => void;
  trackUserInteraction: (action: string, duration: number) => void;
  trackError: (error: Error, context: string) => void;
  getMetrics: () => PerformanceMetrics;
  reportMetrics: () => void;
}
```

### Bundle Optimization

#### Bundle Analysis
```typescript
interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: BundleChunk[];
  dependencies: BundleDependency[];
  duplicates: string[];
  unusedCode: string[];
}

interface BundleChunk {
  name: string;
  size: number;
  modules: string[];
  loadTime: number;
  critical: boolean;
}

interface BundleDependency {
  name: string;
  version: string;
  size: number;
  treeshakeable: boolean;
  usage: number;
}
```

#### Optimization Strategies
```typescript
// Tree shaking configuration
const optimizationConfig = {
  // Remove unused code
  usedExports: true,
  sideEffects: false,
  
  // Code splitting
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all',
      },
      common: {
        name: 'common',
        minChunks: 2,
        chunks: 'all',
        enforce: true,
      },
    },
  },
  
  // Minification
  minimize: true,
  minimizer: ['terser', 'css-minimizer'],
  
  // Module concatenation
  concatenateModules: true,
};
```

### Offline Support

#### Service Worker Strategy
```typescript
interface OfflineStrategy {
  cacheFirst: string[]; // Static assets
  networkFirst: string[]; // API calls
  staleWhileRevalidate: string[]; // Dynamic content
  cacheOnly: string[]; // Offline fallbacks
  networkOnly: string[]; // Always fresh data
}

interface OfflineCapabilities {
  isOnline: boolean;
  hasCache: boolean;
  canSync: boolean;
  queuedActions: OfflineAction[];
  syncStatus: 'idle' | 'syncing' | 'failed';
}

interface OfflineAction {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  retryCount: number;
}
```

### Memory Management

#### Memory Optimization
```typescript
interface MemoryManager {
  clearCache: (pattern?: string) => void;
  getMemoryUsage: () => MemoryInfo;
  optimizeImages: () => void;
  cleanupEventListeners: () => void;
  garbageCollect: () => void;
}

interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  cacheSize: number;
  imageCache: number;
}

// Memory optimization strategies:
// - Cleanup event listeners on unmount
// - Clear image cache periodically
// - Remove unused data from Redux store
// - Implement weak references for large objects
// - Use object pooling for frequently created objects
```

## Mock Data Requirements

### Mock Performance Data
```typescript
const mockPerformanceMetrics: PerformanceMetrics = {
  lcp: 1200,
  fid: 50,
  cls: 0.05,
  ttfb: 200,
  fcp: 800,
  tti: 1500,
  pageLoadTime: 2000,
  apiResponseTime: 300,
  renderTime: 100,
  bundleSize: 250000,
  errorRate: 0.01,
  bounceRate: 0.15,
  sessionDuration: 180000
};
```

### Mock Loading States
```typescript
const mockLoadingStates = {
  products: { loading: true, progress: 60, stage: 'fetching' },
  orders: { loading: false, data: [], error: null },
  user: { loading: false, data: mockUser, stale: false }
};
```

## Acceptance Criteria

1. ✅ Comprehensive loading states for all async operations
2. ✅ Skeleton screens for major components
3. ✅ Image optimization with lazy loading
4. ✅ Virtual scrolling for large lists
5. ✅ Code splitting and lazy loading implementation
6. ✅ Error boundaries with retry functionality
7. ✅ Performance monitoring and metrics
8. ✅ Bundle optimization and analysis
9. ✅ Offline support with service worker
10. ✅ Memory management and cleanup
11. ✅ Caching strategy implementation
12. ✅ Prefetching for improved UX
13. ✅ Progressive loading indicators
14. ✅ Error fallback components

## Integration Points
- Performance metrics will integrate with analytics
- Caching will work with API layer
- Service worker will handle offline functionality
- Error reporting will connect to monitoring service

## Visual Requirements
- Smooth loading animations and transitions
- Consistent loading state designs
- Professional error message presentation
- Clear progress indicators
- Responsive performance across devices

## Notes
- Focus on perceived performance improvements
- Implement progressive enhancement
- Ensure graceful degradation
- Monitor real-world performance metrics
- Optimize for mobile network conditions
