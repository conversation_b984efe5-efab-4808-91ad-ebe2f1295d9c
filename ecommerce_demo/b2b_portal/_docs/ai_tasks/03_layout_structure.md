# Task 03: Core Layout Structure and Navigation

## Objective
Implement the main layout structure with header, navigation, and footer components that support the KAUST B2B portal requirements.

## Technical Requirements

### Layout Architecture

#### Main Layout Component Structure
```
PortalLayout
├── Header
│   ├── Logo
│   ├── SearchBar
│   ├── OrderSelector
│   ├── NotificationBell
│   ├── UserMenu
│   ├── ThemeToggle
│   └── LanguageToggle
├── NavigationBar
│   ├── HomeLink
│   ├── ProductsLink
│   ├── OrdersLink
│   ├── AccountLink
│   └── WishlistLink
├── MainContent
│   └── {children}
└── Footer
    ├── CompanyInfo
    ├── SupportLinks
    └── LegalLinks
```

### Header Component Specifications

#### Header Layout Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [KAUST Logo] [Search: "Search products..."] [My Order ▼] [🔔] [👤] [🌙] [🇸🇦] │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### Header Component Interface
```typescript
interface HeaderProps {
  className?: string;
  sticky?: boolean;
  showSearch?: boolean;
  showOrderSelector?: boolean;
}

interface HeaderElements {
  logo: LogoComponent;
  searchBar: SearchBarComponent;
  orderSelector: OrderSelectorComponent;
  notifications: NotificationBellComponent;
  userMenu: UserMenuComponent;
  themeToggle: ThemeToggleComponent;
  languageToggle: LanguageToggleComponent;
}
```

#### Header Responsive Behavior
- **Desktop (>1024px)**: Full header with all elements visible
- **Tablet (768px-1024px)**: Compact search, condensed user menu
- **Mobile (<768px)**: Hamburger menu, search icon only, minimal header

### Navigation Bar Specifications

#### Navigation Layout Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│     [Home]     [Products]     [Orders]     [Account]     [Wishlist]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### Navigation Component Interface
```typescript
interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: LucideIcon;
  badge?: number;
  active?: boolean;
  disabled?: boolean;
}

interface NavigationBarProps {
  items: NavigationItem[];
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'pills' | 'underline';
  className?: string;
}
```

#### Navigation Items Configuration
```typescript
const navigationItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    icon: Home,
    active: true
  },
  {
    id: 'products',
    label: 'Products',
    href: '/products',
    icon: Package
  },
  {
    id: 'orders',
    label: 'Orders',
    href: '/orders',
    icon: ShoppingCart,
    badge: 3 // Active orders count
  },
  {
    id: 'account',
    label: 'Account',
    href: '/account',
    icon: User
  },
  {
    id: 'wishlist',
    label: 'Wishlist',
    href: '/wishlist',
    icon: Heart,
    badge: 5 // Wishlist items count
  }
];
```

### Search Bar Component

#### Search Bar Interface
```typescript
interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  showSuggestions?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

interface SearchSuggestion {
  id: string;
  type: 'product' | 'category' | 'brand';
  title: string;
  subtitle?: string;
  imageUrl?: string;
  href: string;
}
```

#### Search Bar Features
- Real-time search input with debouncing
- Keyboard navigation (arrow keys, enter, escape)
- Search suggestions dropdown
- Recent searches history
- Category and product type filtering
- Mobile-responsive design
- Loading states and error handling

### Order Selector Component

#### Order Selector Interface
```typescript
interface OrderSelectorProps {
  orders: Order[];
  activeOrderId: string | null;
  onOrderSelect: (orderId: string) => void;
  onCreateOrder: () => void;
  className?: string;
}

interface OrderSummary {
  id: string;
  name: string;
  itemCount: number;
  status: OrderStatus;
  totalAmount?: number;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Order Selector Design
```
┌─────────────────────────────────────┐
│ My Order: "Q1 2024 Laptops" (5) ▼  │
├─────────────────────────────────────┤
│ ✓ Q1 2024 Laptops (5 items)        │
│   Emergency IT Equipment (2 items)  │
│   Office Supplies (8 items)         │
├─────────────────────────────────────┤
│ + Create New Order                  │
└─────────────────────────────────────┘
```

### User Menu Component

#### User Menu Interface
```typescript
interface UserMenuProps {
  user: User;
  onLogout: () => void;
  className?: string;
}

interface UserMenuItems {
  profile: MenuItem;
  settings: MenuItem;
  help: MenuItem;
  logout: MenuItem;
}

interface MenuItem {
  id: string;
  label: string;
  icon: LucideIcon;
  href?: string;
  onClick?: () => void;
  divider?: boolean;
}
```

#### User Menu Design
```
┌─────────────────────────────────────┐
│ [Avatar] Dr. Ahmed Al-Rashid    ▼   │
├─────────────────────────────────────┤
│ 👤 My Profile                       │
│ ⚙️  Settings                        │
│ ❓ Help & Support                   │
│ ─────────────────────────────────   │
│ 🚪 Logout                           │
└─────────────────────────────────────┘
```

### Notification Bell Component

#### Notification Interface
```typescript
interface NotificationBellProps {
  notifications: Notification[];
  unreadCount: number;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  className?: string;
}

interface Notification {
  id: string;
  type: 'order' | 'system' | 'message';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high';
}
```

#### Notification Bell Design
```
┌─────────────────────────────────────┐
│ 🔔 (3)                              │
├─────────────────────────────────────┤
│ • Order #1234 approved              │
│   2 minutes ago                     │
│ • New message from vendor           │
│   1 hour ago                        │
│ • System maintenance scheduled      │
│   3 hours ago                       │
├─────────────────────────────────────┤
│ Mark all as read                    │
└─────────────────────────────────────┘
```

### Footer Component

#### Footer Interface
```typescript
interface FooterProps {
  variant?: 'simple' | 'detailed';
  showSocialLinks?: boolean;
  className?: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

interface FooterLink {
  label: string;
  href: string;
  external?: boolean;
}
```

#### Footer Layout Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ KAUST B2B Marketplace                                                       │
│ © 2024 King Abdullah University of Science and Technology                   │
│                                                                             │
│ Support: <EMAIL> | Privacy Policy | Terms of Service             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Layout Responsive Behavior

#### Breakpoint Specifications
```css
/* Mobile First Approach */
.layout-container {
  /* Mobile: <768px */
  --header-height: 3.5rem;
  --nav-height: 3rem;
  --sidebar-width: 100%;
}

@media (min-width: 768px) {
  /* Tablet: 768px-1024px */
  .layout-container {
    --header-height: 4rem;
    --nav-height: 3.5rem;
    --sidebar-width: 16rem;
  }
}

@media (min-width: 1024px) {
  /* Desktop: >1024px */
  .layout-container {
    --header-height: 4.5rem;
    --nav-height: 4rem;
    --sidebar-width: 18rem;
  }
}
```

#### Mobile Navigation
- Hamburger menu for navigation items
- Collapsible search bar
- Simplified user menu
- Bottom navigation option for key actions
- Swipe gestures for navigation

### Accessibility Features

#### Keyboard Navigation
- Tab order: Logo → Search → Order Selector → Notifications → User Menu → Theme → Language
- Arrow key navigation in dropdowns
- Enter/Space activation for interactive elements
- Escape key to close dropdowns

#### Screen Reader Support
- Proper ARIA labels and roles
- Live regions for dynamic content
- Descriptive alt text for images
- Semantic HTML structure

#### Focus Management
- Visible focus indicators
- Focus trapping in modals
- Skip links for main content
- Logical tab order

## Mock Data Requirements

### Mock User Data
```typescript
const mockUser = {
  id: "user-001",
  name: "Dr. Ahmed Al-Rashid",
  email: "<EMAIL>",
  department: "Computer Science",
  role: "Purchasing Representative",
  avatar: "https://randomuser.me/api/portraits/men/1.jpg"
};
```

### Mock Notifications
```typescript
const mockNotifications = [
  {
    id: "notif-001",
    type: "order",
    title: "Order Approved",
    message: "Your order #1234 has been approved",
    timestamp: new Date(Date.now() - 2 * 60 * 1000),
    read: false,
    priority: "high"
  },
  // ... more notifications
];
```

### Mock Orders for Selector
```typescript
const mockOrders = [
  {
    id: "order-001",
    name: "Q1 2024 Laptops",
    itemCount: 5,
    status: "draft",
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
  },
  // ... more orders
];
```

## Acceptance Criteria

1. ✅ Header component with all required elements
2. ✅ Responsive navigation bar with active states
3. ✅ Search bar with placeholder and basic functionality
4. ✅ Order selector dropdown with mock orders
5. ✅ User menu with profile information
6. ✅ Notification bell with unread count
7. ✅ Theme and language toggles working
8. ✅ Footer with company information
9. ✅ Mobile-responsive layout behavior
10. ✅ Keyboard navigation and accessibility
11. ✅ Smooth animations and transitions
12. ✅ Proper z-index layering for dropdowns

## Integration Points
- Search functionality will connect to product search API
- Order selector will integrate with order management system
- Notifications will connect to real-time notification service
- User menu will integrate with authentication system

## Visual Requirements
- Consistent with KAUST branding and theme system
- Clean, professional B2B interface design
- Proper spacing and typography
- Loading states for all interactive elements
- Error states and fallbacks

## Notes
- Focus on creating reusable, composable components
- Ensure layout doesn't shift during loading
- Optimize for both touch and mouse interactions
- Support both LTR and RTL layouts for Arabic
