# Task 07: Multi-Order Management System (B2B Quotations)

## Objective
Implement the core B2B order management system where Order = Quotation = Cart, allowing users to manage multiple quotations simultaneously with different states and workflows.

## Technical Requirements

### B2B Order Concept

#### Unified Order Model
In B2B procurement, the traditional e-commerce "cart" concept is enhanced:
- **Cart (Draft)**: User's working collection of products
- **Quotation (Submitted)**: Formal request for pricing and terms
- **Order (Confirmed)**: Final purchase after approval

This unified model enables seamless transitions through the B2B procurement lifecycle.

### Key B2B Features
- **Multi-Quotation Management**: Handle multiple active quotations
- **Project-Based Organization**: Group quotations by project/department
- **Approval Workflows**: Built-in approval and review processes
- **Contract Integration**: Link to frame contracts and pricing
- **Bulk Operations**: Efficient handling of large orders

### Order Data Structure

#### Order Interface
```typescript
interface Order {
  id: string;
  name: string;
  customerId: string;
  userId: string;
  status: OrderStatus;
  type: 'quotation' | 'order';
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  currency: string;
  notes?: string;
  internalNotes?: string;
  targetPrice?: number;
  validUntil?: Date;
  createdAt: Date;
  updatedAt: Date;
  submittedAt?: Date;
  approvedAt?: Date;
  convertedAt?: Date;
  metadata: OrderMetadata;
}

interface OrderItem {
  id: string;
  productId: string;
  variantId?: string;
  sku: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  imageUrl?: string;
  specifications?: Record<string, string>;
  notes?: string;
  addedAt: Date;
}

interface OrderMetadata {
  department?: string;
  project?: string;
  budgetCode?: string;
  requestedBy?: string;
  approver?: string;
  deliveryAddress?: Address;
  deliveryDate?: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

type OrderStatus = 
  | 'draft'           // User building quotation
  | 'submitted'       // Sent to vendor for review
  | 'under_review'    // Vendor evaluating quotation
  | 'revised'         // Updated due to negotiation
  | 'approved'        // Vendor accepted terms
  | 'rejected'        // Vendor declined quotation
  | 'expired'         // Validity period lapsed
  | 'converted_to_order' // Final order created
  | 'cancelled';      // Cancelled by user
```

### Redux Store Structure

#### Orders Slice Interface
```typescript
interface OrdersState {
  orders: Order[];
  activeOrderId: string | null;
  loading: boolean;
  error: string | null;
  filters: OrderFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

interface OrderFilters {
  status: OrderStatus[];
  dateRange: {
    start?: Date;
    end?: Date;
  };
  searchQuery: string;
  sortBy: 'name' | 'createdAt' | 'updatedAt' | 'total';
  sortDirection: 'asc' | 'desc';
}
```

#### Orders Actions
```typescript
interface OrdersActions {
  // Order CRUD
  createOrder: (name: string, metadata?: Partial<OrderMetadata>) => void;
  updateOrder: (id: string, updates: Partial<Order>) => void;
  deleteOrder: (id: string) => void;
  duplicateOrder: (id: string, newName: string) => void;
  
  // Order Management
  setActiveOrder: (id: string) => void;
  clearActiveOrder: () => void;
  
  // Item Management
  addItemToOrder: (orderId: string, item: Omit<OrderItem, 'id' | 'addedAt'>) => void;
  updateItemQuantity: (orderId: string, itemId: string, quantity: number) => void;
  removeItemFromOrder: (orderId: string, itemId: string) => void;
  clearOrderItems: (orderId: string) => void;
  
  // Status Management
  submitOrder: (id: string) => void;
  approveOrder: (id: string) => void;
  rejectOrder: (id: string, reason: string) => void;
  convertToOrder: (id: string) => void;
  
  // Bulk Operations
  addItemsToOrder: (orderId: string, items: Omit<OrderItem, 'id' | 'addedAt'>[]) => void;
  moveItemsBetweenOrders: (fromOrderId: string, toOrderId: string, itemIds: string[]) => void;
}
```

### Order Selector Component

#### Order Selector Interface
```typescript
interface OrderSelectorProps {
  orders: Order[];
  activeOrderId: string | null;
  onOrderSelect: (orderId: string) => void;
  onCreateOrder: () => void;
  onManageOrders: () => void;
  className?: string;
}

interface OrderSelectorState {
  isOpen: boolean;
  searchQuery: string;
  filteredOrders: Order[];
}
```

#### Order Selector Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ My Order: "Q1 2024 Laptops" (5 items) ▼                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [Search orders...]                                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ Active Orders                                                               │
│ ✓ Q1 2024 Laptops (5 items) - Draft                                        │
│   Emergency IT Equipment (2 items) - Submitted                             │
│   Office Supplies (8 items) - Under Review                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ Recent Orders                                                               │
│   Server Hardware (12 items) - Approved                                    │
│   Network Equipment (6 items) - Converted                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ + Create New Order                                                          │
│ 📋 Manage All Orders                                                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Order Creation Modal

#### Create Order Interface
```typescript
interface CreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateOrder: (orderData: CreateOrderData) => void;
  templates?: OrderTemplate[];
}

interface CreateOrderData {
  name: string;
  description?: string;
  metadata: Partial<OrderMetadata>;
  templateId?: string;
}

interface OrderTemplate {
  id: string;
  name: string;
  description: string;
  items: Omit<OrderItem, 'id' | 'addedAt'>[];
  metadata: Partial<OrderMetadata>;
}
```

#### Create Order Modal Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Create New Order                                                        [×] │
├─────────────────────────────────────────────────────────────────────────────┤
│ Order Name: [Q2 2024 Equipment Purchase                              ]     │
│                                                                             │
│ Description: [Optional description...                                 ]     │
│                                                                             │
│ Department: [Computer Science ▼]                                           │
│ Project: [AI Research Lab Setup                                       ]     │
│ Budget Code: [CS-2024-Q2                                              ]     │
│ Priority: [● Medium  ○ High  ○ Urgent]                                     │
│                                                                             │
│ Start from template:                                                        │
│ ○ Blank Order                                                               │
│ ○ Standard IT Equipment                                                     │
│ ○ Research Lab Setup                                                        │
│                                                                             │
│                                           [Cancel] [Create Order]          │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Order Management Page

#### Order List Interface
```typescript
interface OrderListProps {
  orders: Order[];
  filters: OrderFilters;
  onFiltersChange: (filters: OrderFilters) => void;
  onOrderSelect: (order: Order) => void;
  onOrderAction: (orderId: string, action: OrderAction) => void;
  loading?: boolean;
}

type OrderAction = 
  | 'view' | 'edit' | 'duplicate' | 'submit' 
  | 'approve' | 'reject' | 'convert' | 'delete';
```

#### Order List Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ My Orders                                                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ [All ▼] [Search orders...] [+ New Order]                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ Name                    Items  Status        Total      Updated    Actions  │
├─────────────────────────────────────────────────────────────────────────────┤
│ Q1 2024 Laptops           5    Draft         65,000     2h ago     [⋯]      │
│ Emergency IT Equipment    2    Submitted     25,000     1d ago     [⋯]      │
│ Office Supplies           8    Under Review  12,500     3d ago     [⋯]      │
│ Server Hardware          12    Approved     150,000     1w ago     [⋯]      │
│ Network Equipment         6    Converted     45,000     2w ago     [⋯]      │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Order Detail View

#### Order Detail Interface
```typescript
interface OrderDetailProps {
  order: Order;
  onUpdateOrder: (updates: Partial<Order>) => void;
  onAddItem: (item: Omit<OrderItem, 'id' | 'addedAt'>) => void;
  onUpdateItem: (itemId: string, updates: Partial<OrderItem>) => void;
  onRemoveItem: (itemId: string) => void;
  onSubmitOrder: () => void;
  onConvertOrder: () => void;
  editable?: boolean;
}
```

#### Order Detail Layout
```
OrderDetailPage
├── OrderHeader
│   ├── OrderTitle
│   ├── StatusBadge
│   ├── OrderActions
│   └── OrderMetadata
├── OrderItems
│   ├── ItemsHeader
│   ├── ItemsList
│   └── AddItemsSection
├── OrderSummary
│   ├── PricingBreakdown
│   ├── TaxCalculation
│   └── TotalAmount
└── OrderNotes
    ├── CustomerNotes
    └── InternalNotes
```

### Order Item Management

#### Order Item Component
```typescript
interface OrderItemProps {
  item: OrderItem;
  onUpdateQuantity: (quantity: number) => void;
  onUpdateNotes: (notes: string) => void;
  onRemove: () => void;
  editable?: boolean;
  showNotes?: boolean;
}
```

#### Order Item Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [Image] MacBook Pro 16" M3 - Apple                                         │
│         SKU: MBP16-M3-512 • Space Gray, 512GB SSD                          │
│         Unit Price: SAR 12,999.00                                           │
│                                                                             │
│         Qty: [−] [2] [+]    Total: SAR 25,998.00                    [🗑]   │
│                                                                             │
│         Notes: [For AI Research Lab - urgent delivery needed        ]      │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Order Status Management

#### Status Workflow Component
```typescript
interface OrderStatusWorkflowProps {
  currentStatus: OrderStatus;
  allowedTransitions: OrderStatus[];
  onStatusChange: (newStatus: OrderStatus) => void;
  showHistory?: boolean;
}

interface StatusTransition {
  from: OrderStatus;
  to: OrderStatus;
  label: string;
  requiresConfirmation: boolean;
  requiresReason?: boolean;
}
```

#### Status Workflow Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Order Status: Draft                                                         │
│                                                                             │
│ ○ Draft → ● Submitted → ○ Under Review → ○ Approved → ○ Converted          │
│                                                                             │
│ Available Actions:                                                          │
│ [Submit for Review] [Save as Template] [Delete Order]                      │
│                                                                             │
│ Status History:                                                             │
│ • Created as Draft - 2 hours ago by Ahmed Al-Rashid                        │
│ • Items added - 1 hour ago                                                  │
│ • Last updated - 30 minutes ago                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Bulk Operations

#### Bulk Actions Interface
```typescript
interface BulkActionsProps {
  selectedOrders: string[];
  onBulkAction: (action: BulkAction, orderIds: string[]) => void;
  availableActions: BulkAction[];
}

type BulkAction = 
  | 'submit' | 'approve' | 'reject' | 'delete' 
  | 'export' | 'merge' | 'duplicate';
```

### Mobile Order Management

#### Mobile Order Interface
```typescript
interface MobileOrderManagerProps {
  orders: Order[];
  activeOrderId: string | null;
  onOrderSelect: (orderId: string) => void;
  onCreateOrder: () => void;
}
```

#### Mobile Order Design
- **Bottom Sheet**: Order selector as bottom sheet
- **Swipe Actions**: Swipe to edit, delete, or submit
- **Floating Action Button**: Quick add to order
- **Simplified View**: Essential information only
- **Touch Optimization**: Large touch targets

## Mock Data Requirements

### Mock Orders Dataset
```typescript
const mockOrders: Order[] = [
  {
    id: 'order-001',
    name: 'Q1 2024 Laptops',
    customerId: 'kaust',
    userId: 'user-001',
    status: 'draft',
    type: 'quotation',
    items: [
      {
        id: 'item-001',
        productId: 'mbp-16-m3',
        sku: 'MBP16-M3-512',
        name: 'MacBook Pro 16" M3',
        quantity: 2,
        unitPrice: 12999,
        totalPrice: 25998,
        imageUrl: '/images/products/mbp-16.jpg',
        addedAt: new Date()
      },
      // ... more items
    ],
    subtotal: 65000,
    tax: 9750,
    shipping: 0,
    total: 74750,
    currency: 'SAR',
    metadata: {
      department: 'Computer Science',
      project: 'AI Research Lab',
      priority: 'medium'
    },
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 30 * 60 * 1000)
  },
  // ... more orders with different statuses
];
```

### Mock Order Templates
```typescript
const mockOrderTemplates: OrderTemplate[] = [
  {
    id: 'template-001',
    name: 'Standard IT Equipment',
    description: 'Basic IT setup for new employees',
    items: [
      // Predefined items for quick order creation
    ],
    metadata: {
      department: 'IT',
      priority: 'medium'
    }
  }
];
```

## Acceptance Criteria

1. ✅ Multi-order management with active order selection
2. ✅ Order creation with metadata and templates
3. ✅ Order item management (add, update, remove)
4. ✅ Order status workflow with proper transitions
5. ✅ Order list with filtering and sorting
6. ✅ Order detail view with full information
7. ✅ Bulk operations for multiple orders
8. ✅ Order duplication and templating
9. ✅ Mobile-responsive order management
10. ✅ Real-time order updates
11. ✅ Order search and filtering
12. ✅ Order notes and metadata management
13. ✅ Order validation and error handling
14. ✅ Order persistence and state management

## Integration Points
- Orders will sync with Odoo sales orders
- Status updates will trigger notifications
- Order approval will integrate with workflow engine
- Order conversion will create purchase orders

## Visual Requirements
- Clear order status indicators
- Intuitive order switching interface
- Professional B2B order management design
- Responsive layout for all devices
- Loading states and error handling

## Notes
- Focus on B2B workflow requirements
- Support complex order scenarios
- Ensure data consistency across orders
- Optimize for frequent order switching
- Implement proper error recovery
