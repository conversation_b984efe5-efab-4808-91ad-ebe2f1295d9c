# Task 02: KAUST Theme System and Branding

## Objective
Implement a dynamic theme system with KAUST university branding, supporting light/dark modes and customer-specific theming architecture.

## Technical Requirements

### Theme Architecture

#### Theme Context Structure
```typescript
interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  customerTheme: CustomerTheme;
  setCustomerTheme: (theme: CustomerTheme) => void;
}

interface CustomerTheme {
  customerId: string;
  themeName: string;
  branding: {
    logoUrl: string;
    faviconUrl: string;
    companyName: string;
    tagline?: string;
  };
  colors: {
    light: ColorScheme;
    dark: ColorScheme;
  };
  typography: {
    primaryFont: string;
    secondaryFont: string;
    arabicFont: string;
  };
  layout: {
    headerStyle: 'default' | 'minimal' | 'extended';
    borderRadius: string;
  };
}

interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  muted: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}
```

### KAUST Brand Configuration

#### KAUST Theme Definition
Based on https://www.kaust.edu.sa/en/ branding:

```typescript
const kaustTheme: CustomerTheme = {
  customerId: "kaust",
  themeName: "KAUST University Theme",
  branding: {
    logoUrl: "/themes/kaust/logo.svg",
    faviconUrl: "/themes/kaust/favicon.ico",
    companyName: "King Abdullah University of Science and Technology",
    tagline: "Advancing Science and Technology"
  },
  colors: {
    light: {
      primary: "#0066CC",      // KAUST Blue
      secondary: "#004499",    // Dark Blue
      accent: "#FFB800",       // KAUST Gold
      background: "#FFFFFF",
      foreground: "#1A1A1A",
      muted: "#F5F5F5",
      border: "#E5E5E5",
      success: "#10B981",
      warning: "#F59E0B",
      error: "#EF4444"
    },
    dark: {
      primary: "#3B82F6",      // Lighter blue for dark mode
      secondary: "#1E40AF",    // Dark blue adjusted
      accent: "#FCD34D",       // Gold adjusted for dark
      background: "#0F172A",   // Dark background
      foreground: "#F8FAFC",   // Light text
      muted: "#1E293B",        // Dark muted
      border: "#334155",       // Dark border
      success: "#059669",
      warning: "#D97706",
      error: "#DC2626"
    }
  },
  typography: {
    primaryFont: "Inter, sans-serif",
    secondaryFont: "Roboto Mono, monospace",
    arabicFont: "Noto Sans Arabic, sans-serif"
  },
  layout: {
    headerStyle: "extended",
    borderRadius: "0.5rem"
  }
};
```

### CSS Custom Properties Implementation

#### Dynamic CSS Variables
```css
:root {
  /* Base theme variables */
  --color-primary: var(--customer-primary, #2563eb);
  --color-secondary: var(--customer-secondary, #64748b);
  --color-accent: var(--customer-accent, #f1f5f9);
  --color-background: var(--customer-background, #ffffff);
  --color-foreground: var(--customer-foreground, #171717);
  --color-muted: var(--customer-muted, #f8fafc);
  --color-border: var(--customer-border, #e2e8f0);
  --color-success: var(--customer-success, #10b981);
  --color-warning: var(--customer-warning, #f59e0b);
  --color-error: var(--customer-error, #ef4444);

  /* Typography */
  --font-primary: var(--customer-font-primary, 'Inter', sans-serif);
  --font-secondary: var(--customer-font-secondary, 'Roboto Mono', monospace);
  --font-arabic: var(--customer-font-arabic, 'Noto Sans Arabic', sans-serif);

  /* Layout */
  --border-radius: var(--customer-border-radius, 0.5rem);
  --header-height: 4rem;
  --sidebar-width: 16rem;
}

/* KAUST Theme Override */
.theme-kaust {
  --customer-primary: #0066CC;
  --customer-secondary: #004499;
  --customer-accent: #FFB800;
  --customer-font-primary: 'Inter', sans-serif;
  --customer-border-radius: 0.5rem;
}

.theme-kaust.dark {
  --customer-primary: #3B82F6;
  --customer-secondary: #1E40AF;
  --customer-accent: #FCD34D;
  --customer-background: #0F172A;
  --customer-foreground: #F8FAFC;
  --customer-muted: #1E293B;
  --customer-border: #334155;
}
```

### Theme Components

#### 1. Theme Provider Component
```typescript
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: 'light' | 'dark';
  customerId?: string;
}

// Component should:
// - Manage theme state (light/dark)
// - Load customer-specific theme configuration
// - Apply CSS custom properties dynamically
// - Persist theme preference in localStorage
// - Handle system theme detection
// - Provide theme context to children
```

#### 2. Theme Toggle Component
```typescript
interface ThemeToggleProps {
  variant?: 'button' | 'switch' | 'dropdown';
  showLabel?: boolean;
  className?: string;
}

// Component should:
// - Display current theme state
// - Toggle between light/dark modes
// - Show appropriate icons (sun/moon)
// - Smooth transition animations
// - Keyboard accessibility
// - Support different visual variants
```

#### 3. Customer Theme Loader
```typescript
interface CustomerThemeLoaderProps {
  customerId: string;
  onThemeLoaded?: (theme: CustomerTheme) => void;
}

// Component should:
// - Load customer theme configuration
// - Apply theme to document root
// - Handle loading states
// - Fallback to default theme
// - Cache theme configuration
// - Support theme hot-swapping
```

### Brand Assets Management

#### Logo Component
```typescript
interface LogoProps {
  variant?: 'full' | 'icon' | 'text';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  theme?: 'light' | 'dark';
  className?: string;
}

// Component should:
// - Display KAUST logo in different variants
// - Adapt to current theme (light/dark)
// - Support different sizes
// - Handle loading states
// - Fallback to text if image fails
// - Maintain aspect ratio
```

#### Favicon Management
```typescript
// Dynamic favicon system:
// - Load customer-specific favicon
// - Support different formats (ico, png, svg)
// - Update document head dynamically
// - Handle theme-aware favicons
// - Fallback to default favicon
```

### Theme Utilities

#### Theme Hook
```typescript
interface UseThemeReturn {
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  customerTheme: CustomerTheme;
  isLoading: boolean;
  systemTheme: 'light' | 'dark';
}

// Hook should provide:
// - Current theme state
// - Theme manipulation functions
// - Customer theme configuration
// - Loading states
// - System theme detection
// - Persistence handling
```

#### Color Utilities
```typescript
// Utility functions for:
// - Converting hex to HSL
// - Generating color variations
// - Calculating contrast ratios
// - Theme-aware color selection
// - Accessibility color validation
```

### Responsive Theme Behavior

#### Breakpoint-Aware Theming
```css
/* Mobile-first theme adjustments */
@media (max-width: 768px) {
  .theme-kaust {
    --header-height: 3.5rem;
    --sidebar-width: 100%;
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  .theme-kaust {
    --sidebar-width: 14rem;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .theme-kaust {
    --header-height: 4.5rem;
    --sidebar-width: 18rem;
  }
}
```

### Animation and Transitions

#### Theme Transition System
```css
/* Smooth theme transitions */
* {
  transition: 
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease,
    fill 0.3s ease,
    stroke 0.3s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
```

### Theme Configuration Files

#### Theme Registry
```typescript
// themes/index.ts
export const customerThemes: Record<string, CustomerTheme> = {
  'kaust': kaustTheme,
  'default': defaultTheme
};

export function getCustomerTheme(customerId: string): CustomerTheme {
  return customerThemes[customerId] || customerThemes['default'];
}

export function loadThemeAssets(theme: CustomerTheme): Promise<void> {
  // Load fonts, images, and other theme assets
}
```

## Mock Data Requirements

### Theme Configuration Mock
```typescript
const mockThemeConfigs = {
  kaust: {
    // Full KAUST theme configuration
    customerId: "kaust",
    // ... complete theme object
  },
  demo: {
    // Demo customer theme for testing
    customerId: "demo",
    // ... alternative theme configuration
  }
};
```

## Acceptance Criteria

1. ✅ Theme context provider implemented and working
2. ✅ KAUST brand colors applied correctly in light/dark modes
3. ✅ Theme toggle component with smooth transitions
4. ✅ CSS custom properties system for dynamic theming
5. ✅ Logo component with multiple variants and sizes
6. ✅ Favicon management system working
7. ✅ Theme persistence in localStorage
8. ✅ System theme detection and auto-switching
9. ✅ Responsive theme behavior across devices
10. ✅ Accessibility compliance for color contrast
11. ✅ Theme loading states and error handling
12. ✅ Customer theme registry system

## Integration Points
- Theme configuration will be loaded from Odoo backend in production
- Brand assets will be served from CDN
- Theme preferences will sync with user profile

## Visual Requirements
- Use KAUST official colors from their website
- Maintain brand consistency across all components
- Ensure proper contrast ratios for accessibility
- Support both English and Arabic typography
- Smooth animations for theme transitions

## Notes
- Focus on creating a flexible theme system for multiple customers
- Ensure theme changes don't cause layout shifts
- Optimize theme loading for performance
- Support theme customization without code changes
