# Task 08: Order State Management and Workflow

## Objective
Implement the B2B order state machine with proper transitions, validation, and workflow management for quotation-to-order lifecycle.

## Technical Requirements

### Order State Machine

#### State Definitions
```typescript
type OrderStatus = 
  | 'draft'           // User building quotation
  | 'submitted'       // Sent to vendor for review
  | 'under_review'    // Vendor evaluating quotation
  | 'revised'         // Updated due to negotiation
  | 'approved'        // Vendor accepted terms
  | 'rejected'        // Vendor declined quotation
  | 'expired'         // Validity period lapsed
  | 'converted_to_order' // Final order created
  | 'cancelled';      // Cancelled by user

interface OrderStatusConfig {
  status: OrderStatus;
  label: string;
  description: string;
  color: string;
  icon: string;
  allowedTransitions: OrderStatus[];
  userActions: string[];
  systemActions: string[];
  notifications: NotificationType[];
}
```

#### State Transition Rules
```typescript
const orderStateTransitions: Record<OrderStatus, OrderStatus[]> = {
  draft: ['submitted', 'cancelled'],
  submitted: ['under_review', 'cancelled'],
  under_review: ['approved', 'revised', 'rejected'],
  revised: ['under_review', 'cancelled'],
  approved: ['converted_to_order', 'expired'],
  rejected: [], // Terminal state
  expired: [], // Terminal state
  converted_to_order: [], // Terminal state
  cancelled: [] // Terminal state
};
```

### State Management Hook

#### Order Workflow Hook
```typescript
interface UseOrderWorkflowReturn {
  currentStatus: OrderStatus;
  allowedTransitions: OrderStatus[];
  canTransitionTo: (status: OrderStatus) => boolean;
  transitionTo: (status: OrderStatus, data?: TransitionData) => Promise<void>;
  getStatusConfig: (status: OrderStatus) => OrderStatusConfig;
  getStatusHistory: () => StatusHistoryEntry[];
  validateTransition: (from: OrderStatus, to: OrderStatus) => ValidationResult;
}

interface TransitionData {
  reason?: string;
  notes?: string;
  metadata?: Record<string, any>;
  notifyUsers?: string[];
}

interface StatusHistoryEntry {
  id: string;
  fromStatus: OrderStatus;
  toStatus: OrderStatus;
  timestamp: Date;
  userId: string;
  userName: string;
  reason?: string;
  notes?: string;
  automated: boolean;
}
```

### Status Display Components

#### Status Badge Component
```typescript
interface StatusBadgeProps {
  status: OrderStatus;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showLabel?: boolean;
  className?: string;
}
```

#### Status Badge Design
```
Draft:        [📝 Draft]
Submitted:    [📤 Submitted]
Under Review: [👁 Under Review]
Revised:      [✏️ Revised]
Approved:     [✅ Approved]
Rejected:     [❌ Rejected]
Expired:      [⏰ Expired]
Converted:    [🎯 Order Created]
Cancelled:    [🚫 Cancelled]
```

### Status Workflow Component

#### Workflow Visualization
```typescript
interface OrderWorkflowProps {
  orderId: string;
  currentStatus: OrderStatus;
  statusHistory: StatusHistoryEntry[];
  onStatusChange?: (status: OrderStatus, data: TransitionData) => void;
  readonly?: boolean;
}
```

#### Workflow Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Order Workflow: Q1 2024 Laptops                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ● Draft → ● Submitted → ● Under Review → ○ Approved → ○ Converted          │
│   ✓         ✓            ⏳                                                 │
│                                                                             │
│ Current Status: Under Review                                                │
│ Vendor is evaluating your quotation request                                │
│                                                                             │
│ Available Actions:                                                          │
│ [Cancel Request] [Add Notes] [Contact Vendor]                              │
│                                                                             │
│ Status History:                                                             │
│ • Under Review - 2 hours ago (Vendor: John Smith)                          │
│   "Reviewing technical specifications"                                      │
│ • Submitted - 1 day ago (You)                                              │
│   "Urgent request for AI lab setup"                                        │
│ • Created - 2 days ago (You)                                               │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Transition Actions

#### Status Transition Modal
```typescript
interface StatusTransitionModalProps {
  isOpen: boolean;
  onClose: () => void;
  fromStatus: OrderStatus;
  toStatus: OrderStatus;
  onConfirm: (data: TransitionData) => void;
  requiresReason?: boolean;
  requiresNotes?: boolean;
}
```

#### Transition Modal Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Submit Order for Review                                                 [×] │
├─────────────────────────────────────────────────────────────────────────────┤
│ You are about to submit this quotation to the vendor for review.           │
│                                                                             │
│ Order: Q1 2024 Laptops                                                     │
│ Items: 5 products                                                           │
│ Total: SAR 74,750                                                           │
│                                                                             │
│ Submission Notes (Optional):                                                │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ Urgent request for AI research lab setup. Please prioritize this       │ │
│ │ quotation as we need equipment by end of month.                        │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ☐ Notify department manager                                                 │
│ ☐ Request expedited review                                                  │
│                                                                             │
│                                           [Cancel] [Submit for Review]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Automated Workflows

#### Workflow Rules Engine
```typescript
interface WorkflowRule {
  id: string;
  name: string;
  trigger: WorkflowTrigger;
  conditions: WorkflowCondition[];
  actions: WorkflowAction[];
  enabled: boolean;
}

interface WorkflowTrigger {
  type: 'status_change' | 'time_based' | 'value_threshold' | 'user_action';
  config: Record<string, any>;
}

interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

interface WorkflowAction {
  type: 'send_notification' | 'update_status' | 'assign_user' | 'create_task';
  config: Record<string, any>;
}
```

#### Example Workflow Rules
```typescript
const workflowRules: WorkflowRule[] = [
  {
    id: 'auto-expire-approved',
    name: 'Auto-expire approved quotations',
    trigger: {
      type: 'time_based',
      config: { schedule: 'daily', time: '09:00' }
    },
    conditions: [
      { field: 'status', operator: 'equals', value: 'approved' },
      { field: 'validUntil', operator: 'less_than', value: 'now' }
    ],
    actions: [
      { type: 'update_status', config: { status: 'expired' } },
      { type: 'send_notification', config: { template: 'quotation_expired' } }
    ],
    enabled: true
  },
  {
    id: 'high-value-approval',
    name: 'High-value quotation approval',
    trigger: {
      type: 'status_change',
      config: { from: 'draft', to: 'submitted' }
    },
    conditions: [
      { field: 'total', operator: 'greater_than', value: 100000 }
    ],
    actions: [
      { type: 'send_notification', config: { 
        recipients: ['finance_manager'], 
        template: 'high_value_quotation' 
      }}
    ],
    enabled: true
  }
];
```

### Status Validation

#### Validation Rules
```typescript
interface StatusValidationRule {
  fromStatus: OrderStatus;
  toStatus: OrderStatus;
  validator: (order: Order) => ValidationResult;
  message: string;
}

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

const statusValidationRules: StatusValidationRule[] = [
  {
    fromStatus: 'draft',
    toStatus: 'submitted',
    validator: (order) => ({
      valid: order.items.length > 0 && order.total > 0,
      errors: order.items.length === 0 ? ['Order must have at least one item'] : [],
      warnings: []
    }),
    message: 'Order must have items before submission'
  },
  {
    fromStatus: 'approved',
    toStatus: 'converted_to_order',
    validator: (order) => ({
      valid: order.validUntil ? new Date() <= order.validUntil : true,
      errors: order.validUntil && new Date() > order.validUntil ? ['Quotation has expired'] : [],
      warnings: []
    }),
    message: 'Cannot convert expired quotation to order'
  }
];
```

### Bulk Status Operations

#### Bulk Status Update
```typescript
interface BulkStatusUpdateProps {
  selectedOrders: string[];
  targetStatus: OrderStatus;
  onUpdate: (orderIds: string[], status: OrderStatus, data: TransitionData) => void;
  onCancel: () => void;
}
```

#### Bulk Update Modal Design
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Bulk Status Update                                                      [×] │
├─────────────────────────────────────────────────────────────────────────────┤
│ Update 3 selected orders to "Submitted" status                             │
│                                                                             │
│ Orders to update:                                                           │
│ • Q1 2024 Laptops (5 items)                                                │
│ • Emergency IT Equipment (2 items)                                         │
│ • Office Supplies (8 items)                                                │
│                                                                             │
│ Bulk Notes:                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ Submitting all Q1 orders for vendor review                             │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ⚠️ This action cannot be undone                                            │
│                                                                             │
│                                           [Cancel] [Update All Orders]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Status Notifications

#### Notification System Integration
```typescript
interface StatusNotification {
  orderId: string;
  fromStatus: OrderStatus;
  toStatus: OrderStatus;
  timestamp: Date;
  triggeredBy: string;
  recipients: string[];
  template: string;
  data: Record<string, any>;
}

interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  channels: ('email' | 'sms' | 'push' | 'in_app')[];
  variables: string[];
}
```

### Mobile Workflow Interface

#### Mobile Status Management
```typescript
interface MobileOrderStatusProps {
  order: Order;
  onStatusChange: (status: OrderStatus, data: TransitionData) => void;
}
```

#### Mobile Status Design
- **Status Cards**: Swipeable status cards
- **Quick Actions**: Floating action buttons for common transitions
- **Status Timeline**: Vertical timeline view
- **Touch Gestures**: Swipe to change status
- **Simplified Forms**: Minimal input forms for mobile

## Mock Data Requirements

### Mock Status History
```typescript
const mockStatusHistory: StatusHistoryEntry[] = [
  {
    id: 'hist-001',
    fromStatus: 'submitted',
    toStatus: 'under_review',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    userId: 'vendor-001',
    userName: 'John Smith (Vendor)',
    reason: 'automatic',
    notes: 'Quotation received and assigned for review',
    automated: true
  },
  {
    id: 'hist-002',
    fromStatus: 'draft',
    toStatus: 'submitted',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    userId: 'user-001',
    userName: 'Ahmed Al-Rashid',
    reason: 'user_action',
    notes: 'Urgent request for AI lab setup',
    automated: false
  }
];
```

### Mock Workflow Rules
```typescript
const mockWorkflowRules: WorkflowRule[] = [
  // Auto-expiry rules
  // High-value approval rules
  // Notification rules
  // Escalation rules
];
```

## Acceptance Criteria

1. ✅ Complete order state machine implementation
2. ✅ Status transition validation and rules
3. ✅ Visual workflow component with timeline
4. ✅ Status history tracking and display
5. ✅ Transition confirmation modals
6. ✅ Automated workflow rules engine
7. ✅ Bulk status update operations
8. ✅ Status-based notifications
9. ✅ Mobile-responsive status management
10. ✅ Error handling and validation
11. ✅ Status badge components
12. ✅ Workflow analytics and reporting

## Integration Points
- Status changes will trigger Odoo workflow updates
- Notifications will integrate with email/SMS services
- Workflow rules will connect to business logic engine
- Status history will be audited and logged

## Visual Requirements
- Clear status indicators and progression
- Intuitive workflow visualization
- Professional B2B status management
- Responsive design for all devices
- Loading states and error feedback

## Notes
- Focus on B2B procurement workflow requirements
- Ensure proper state validation and error handling
- Support complex approval workflows
- Implement proper audit trails
- Optimize for workflow efficiency
