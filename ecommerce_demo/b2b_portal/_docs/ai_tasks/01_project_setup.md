# Task 01: Project Setup and Configuration

## Objective

Initialize a Next.js 15 project with TypeScript, Tailwind CSS, and all required dependencies for the KAUST B2B E-Commerce Portal.

## Technical Requirements

### Project Structure

```
ai_b2b/
├── src/
│   ├── app/                    # Next.js 15 App Router
│   ├── components/             # Reusable UI components
│   │   ├── ui/                # ShadCN UI components
│   │   ├── layout/            # Layout components
│   │   └── features/          # Feature-specific components
│   ├── lib/                   # Utilities and configurations
│   │   ├── redux/             # Redux store and slices
│   │   ├── api/               # API layer and mock data
│   │   └── utils/             # Helper functions
│   ├── contexts/              # React contexts
│   ├── hooks/                 # Custom React hooks
│   ├── types/                 # TypeScript type definitions
│   └── styles/                # Global styles
├── public/                    # Static assets
│   ├── images/               # Images and icons
│   └── themes/               # Theme-specific assets
└── docs/                     # Documentation
```

### Dependencies to Install

#### Core Framework (latest packages)

- `next` - Next.js framework
- `react` - React library
- `react-dom` - React DOM
- `typescript` - TypeScript support

#### UI and Styling (latest packages)

- `tailwindcss` - Utility-first CSS framework
- `@tailwindcss/typography` - Typography plugin
- `postcss` - CSS processing
- `autoprefixer` - CSS vendor prefixes
- `class-variance-authority` - Component variants
- `clsx` - Conditional classes
- `tailwind-merge` - Tailwind class merging

#### ShadCN UI Components (latest packages)

- `@radix-ui/react-*` (multiple packages for UI primitives)
- `lucide-react` - Icon library
- `cmdk` - Command palette component

#### State Management

- `@reduxjs/toolkit` - Redux state management
- `react-redux`- React Redux bindings

#### HTTP and API

- `axios` - HTTP client for API calls

### Configuration Files

#### 1. TypeScript Configuration (tsconfig.json)

{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/contexts/*": ["./src/contexts/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}

#### 2. Tailwind CSS Configuration (tailwind.config.js)

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // KAUST Brand Colors
        kaust: {
          blue: "#0066CC",
          darkblue: "#004499",
          gold: "#FFB800",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        arabic: ['Noto Sans Arabic', 'sans-serif'],
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
}
```

#### 3. Next.js Configuration (next.config.js)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com', 'placehold.co', 'randomuser.me'],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

### Environment Configuration

#### Environment Variables (.env.local)

```
# Application
NEXT_PUBLIC_APP_NAME="KAUST B2B Marketplace"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:3000/api"
NEXT_PUBLIC_MOCK_API_DELAY=500

# Theme Configuration
NEXT_PUBLIC_DEFAULT_THEME="light"
NEXT_PUBLIC_DEFAULT_LANGUAGE="en"

# External Services (Mock URLs for development)
NEXT_PUBLIC_NAFATH_AUTH_URL="https://mock-nafath.example.com"
NEXT_PUBLIC_ODOO_API_URL="https://mock-odoo.example.com"

# Feature Flags
NEXT_PUBLIC_ENABLE_DARK_MODE=true
NEXT_PUBLIC_ENABLE_RTL=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
```

### Initial File Structure Setup

#### 1. Global Styles (src/styles/globals.css)

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Arabic Font Support */
.font-arabic {
  font-family: 'Noto Sans Arabic', sans-serif;
}

/* KAUST Brand Colors */
.text-kaust-blue {
  color: #0066CC;
}

.bg-kaust-blue {
  background-color: #0066CC;
}

.text-kaust-gold {
  color: #FFB800;
}

.bg-kaust-gold {
  background-color: #FFB800;
}
```

### Package.json Scripts


## Acceptance Criteria

1. ✅ Next.js 15 project initialized with TypeScript
2. ✅ All required dependencies installed and configured
3. ✅ Tailwind CSS configured with KAUST brand colors
4. ✅ ShadCN UI components ready for use
5. ✅ Redux Toolkit store configured
6. ✅ Project structure follows specified organization
7. ✅ Environment variables configured for development
8. ✅ ESLint and TypeScript configurations working
9. ✅ Development server runs without errors
10. ✅ Build process completes successfully

## Mock Data Requirements

- No mock data required for this setup task
- Prepare structure for future mock API implementation

## Integration Points

- Environment variables prepared for future Odoo 18 integration
- API base URL configuration for backend services
- Authentication URL placeholder for Nafath integration

## Notes

- Focus on creating a solid foundation for rapid development
- Ensure all configurations support both English and Arabic languages
- Set up proper TypeScript paths for clean imports
- Configure Tailwind for both LTR and RTL layouts
