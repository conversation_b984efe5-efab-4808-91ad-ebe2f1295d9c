# POC 015 - Odoo Integration Guide (Simplified for Public Endpoints)

## Overview

This POC demonstrates how to integrate with **public Odoo endpoints** (no authentication required) in the KAUST B2B Marketplace project. This simplified approach is perfect for getting started with Odoo integration.

## 🎯 **4 Simple Steps**

1. **Create HTTP Client** - Basic axios setup for API calls
2. **Create Service Function** - Direct call to Odoo public endpoint
3. **Create Redux Async Thunk** - Handle API call with loading/error states
4. **Use in Component** - Dispatch action and display data

## Simple Integration Flow

```mermaid
graph LR
    A[Component] --> B[Redux Action]
    B --> C[Service Function]
    C --> D[Axios Call]
    D --> E[Public Odoo API]
    E --> F[JSON Response]
    F --> D
    D --> C
    C --> B
    B --> G[Redux Store]
    G --> A

    style A fill:#e1f5fe
    style G fill:#e8f5e8
    style E fill:#fff3e0
```

## Step-by-Step Implementation

### Step 1: Create Simple HTTP Client

**File: `src/lib/api/httpClient.ts`**

```typescript
import axios from 'axios';

// Simple HTTP client for public Odoo endpoints
export const odooClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_ODOO_URL || 'https://your-odoo-instance.com',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add simple logging
odooClient.interceptors.request.use((config) => {
  console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
  return config;
});

odooClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status}`);
    return response;
  },
  (error) => {
    console.error(`❌ API Error:`, error.response?.data || error.message);
    return Promise.reject(error);
  }
);
```

### Step 2: Create Service Function

**File: `src/lib/api/productService.ts`**

```typescript
import { odooClient } from './httpClient';

// Simple interface for Odoo product
export interface OdooProduct {
  id: number;
  name: string;
  default_code: string;
  list_price: number;
  categ_id: [number, string];
  image_1920?: string;
}

// Simple service function to get products
export async function getProductsFromOdoo(): Promise<OdooProduct[]> {
  try {
    // Call public Odoo endpoint
    const response = await odooClient.get('/api/products');

    // Return the data directly
    return response.data;
  } catch (error) {
    console.error('Failed to fetch products:', error);
    throw new Error('Failed to fetch products from Odoo');
  }
}

// Get single product
export async function getProductFromOdoo(productId: number): Promise<OdooProduct> {
  try {
    const response = await odooClient.get(`/api/products/${productId}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch product:', error);
    throw new Error('Failed to fetch product from Odoo');
  }
}
```

### Step 3: Create Redux Async Thunk

**File: `src/lib/redux/slices/products.ts`**

```typescript
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getProductsFromOdoo, OdooProduct } from '../../api/productService';

// Simple state interface
interface ProductsState {
  products: OdooProduct[];
  loading: boolean;
  error: string | null;
}

const initialState: ProductsState = {
  products: [],
  loading: false,
  error: null,
};

// Simple async thunk to fetch products
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async () => {
    // Call the service function
    const products = await getProductsFromOdoo();
    return products;
  }
);

// Simple Redux slice
export const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch products';
      });
  },
});

export const { clearError } = productsSlice.actions;
export default productsSlice.reducer;
```

### Step 4: Use in Component

**File: `src/app/(portal)/products/page.tsx`**

```typescript
'use client';

import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../lib/redux/hooks';
import { fetchProducts } from '../../../lib/redux/slices/products';

export default function ProductsPage() {
  const dispatch = useAppDispatch();
  const { products, loading, error } = useAppSelector((state) => state.products);

  // Fetch products when component mounts
  useEffect(() => {
    dispatch(fetchProducts());
  }, [dispatch]);

  // Show loading state
  if (loading) {
    return <div>Loading products from Odoo...</div>;
  }

  // Show error state
  if (error) {
    return (
      <div>
        <p>Error: {error}</p>
        <button onClick={() => dispatch(fetchProducts())}>
          Try Again
        </button>
      </div>
    );
  }

  // Show products
  return (
    <div>
      <h1>Products from Odoo</h1>
      <div className="products-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <h3>{product.name}</h3>
            <p>SKU: {product.default_code}</p>
            <p>Price: ${product.list_price}</p>
            <p>Category: {product.categ_id[1]}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Complete Example Summary

Here's what happens when you follow these 4 steps:

1. **HTTP Client** → Simple axios setup with logging
2. **Service Function** → Direct API call to `/api/products`
3. **Redux Thunk** → Handles loading/error states automatically
4. **Component** → Dispatches action and displays data

## Environment Setup

**File: `.env.local`**

```bash
# Simple Odoo configuration for public endpoints
NEXT_PUBLIC_ODOO_URL=https://your-odoo-instance.com
```

## Real Example API Call

When you call `dispatch(fetchProducts())`, this happens:

```text
1. Component dispatches fetchProducts()
2. Redux calls getProductsFromOdoo()
3. Service makes GET request to /api/products
4. Odoo returns JSON array of products
5. Redux updates state with products
6. Component re-renders with new data
```

## Quick Start Checklist

✅ **Step 1**: Create HTTP client with axios
✅ **Step 2**: Create service function for API call
✅ **Step 3**: Create Redux async thunk
✅ **Step 4**: Use in component with useEffect

## Common Issues and Solutions

### Issue 1: CORS Error
```bash
# Add to your Odoo configuration
Access-Control-Allow-Origin: http://localhost:3000
```

### Issue 2: Network Error
```typescript
// Add error handling in service
try {
  const response = await odooClient.get('/api/products');
  return response.data;
} catch (error) {
  console.error('API Error:', error);
  throw new Error('Failed to fetch products');
}
```

### Issue 3: Loading State Not Working
```typescript
// Make sure to handle all states in component
if (loading) return <div>Loading...</div>;
if (error) return <div>Error: {error}</div>;
if (products.length === 0) return <div>No products found</div>;
```

## Testing Your Integration

### Test the API Call
```typescript
// Test in browser console
fetch('https://your-odoo-instance.com/api/products')
  .then(response => response.json())
  .then(data => console.log(data));
```

### Test Redux Integration
```typescript
// In your component, add this to test
useEffect(() => {
  console.log('Products state:', { products, loading, error });
}, [products, loading, error]);
```

## Troubleshooting

### 1. **CORS Issues**
Add CORS headers to your Odoo server or use a proxy.

### 2. **Network Errors**
Check if the Odoo endpoint is accessible and returns valid JSON.

### 3. **Redux Not Updating**
Make sure your component is wrapped with Redux Provider.

## Next Steps

Once this basic integration works:

1. **Add Authentication** - Implement login/session management
2. **Add Filtering** - Implement search and category filters
3. **Add Caching** - Cache responses for better performance
4. **Add Error Handling** - Improve error messages and retry logic
5. **Add Testing** - Write tests for your integration

## Summary

This simplified approach gives you:
- ✅ **Quick Setup**: 4 simple steps to get started
- ✅ **No Authentication**: Works with public endpoints
- ✅ **Redux Integration**: Proper state management
- ✅ **Error Handling**: Basic error states
- ✅ **Real Example**: Working code you can copy

Perfect for getting started with Odoo integration!
