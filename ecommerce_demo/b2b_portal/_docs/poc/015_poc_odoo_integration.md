# POC 015 - Odoo Integration Guide

## Overview

This POC demonstrates how to integrate the KAUST B2B Marketplace with Odoo ERP backend, including API communication, Redux integration, and real-time data synchronization.

## Integration Architecture

```mermaid
graph TB
    subgraph "Frontend (Next.js)"
        A[React Components]
        B[Redux Store]
        C[API Service Layer]
        D[HTTP Client]
    end
    
    subgraph "Backend (Odoo)"
        E[Odoo Web API]
        F[Product Module]
        G[Sales Module]
        H[Partner Module]
        I[Database]
    end
    
    subgraph "Authentication"
        J[Nafath Integration]
        K[JWT Tokens]
        L[Session Management]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    F --> I
    G --> I
    H --> I
    
    J --> K
    K --> L
    L --> D
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style E fill:#fff3e0
    style I fill:#f3e5f5
```

## Step-by-Step Implementation

### Step 1: Odoo API Configuration

**File: `src/lib/api/odooConfig.ts`**

```typescript
export const ODOO_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_ODOO_URL || 'https://your-odoo-instance.com',
  database: process.env.NEXT_PUBLIC_ODOO_DB || 'your_database',
  apiVersion: 'v1',
  endpoints: {
    auth: '/web/session/authenticate',
    products: '/api/v1/products',
    orders: '/api/v1/sale.order',
    partners: '/api/v1/res.partner',
    categories: '/api/v1/product.category',
  },
  timeout: 30000,
  retryAttempts: 3,
};

export interface OdooResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface OdooProduct {
  id: number;
  name: string;
  default_code: string; // SKU
  list_price: number;
  categ_id: [number, string]; // [id, name]
  description_sale?: string;
  image_1920?: string; // Base64 encoded image
  qty_available: number;
  virtual_available: number;
  is_published: boolean;
  website_published: boolean;
  attribute_line_ids: number[];
  variant_ids: number[];
}
```

### Step 2: HTTP Client Setup

**File: `src/lib/api/httpClient.ts`**

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ODOO_CONFIG } from './odooConfig';

class OdooHttpClient {
  private client: AxiosInstance;
  private sessionId: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: ODOO_CONFIG.baseURL,
      timeout: ODOO_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - Add authentication
    this.client.interceptors.request.use(
      (config) => {
        // Add session ID to requests
        if (this.sessionId) {
          config.headers['X-Session-ID'] = this.sessionId;
        }

        // Add CSRF token if available
        const csrfToken = this.getCSRFToken();
        if (csrfToken) {
          config.headers['X-CSRFToken'] = csrfToken;
        }

        console.log(`🚀 Odoo API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor - Handle responses and errors
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ Odoo API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        console.error('❌ Response Error:', error.response?.data || error.message);

        // Handle authentication errors
        if (error.response?.status === 401) {
          this.sessionId = null;
          // Redirect to login or refresh token
          await this.refreshAuthentication();
        }

        // Handle rate limiting
        if (error.response?.status === 429) {
          await this.handleRateLimit(error);
        }

        return Promise.reject(error);
      }
    );
  }

  private getCSRFToken(): string | null {
    // Get CSRF token from cookies or meta tag
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || null;
  }

  private async refreshAuthentication(): Promise<void> {
    // Implement token refresh logic
    try {
      const refreshToken = localStorage.getItem('odoo_refresh_token');
      if (refreshToken) {
        await this.authenticate(refreshToken);
      }
    } catch (error) {
      console.error('Failed to refresh authentication:', error);
      // Redirect to login page
      window.location.href = '/login';
    }
  }

  private async handleRateLimit(error: any): Promise<void> {
    const retryAfter = error.response?.headers['retry-after'] || 1;
    console.log(`Rate limited. Retrying after ${retryAfter} seconds...`);
    await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
  }

  async authenticate(credentials: { username: string; password: string } | string): Promise<boolean> {
    try {
      const response = await this.client.post(ODOO_CONFIG.endpoints.auth, {
        jsonrpc: '2.0',
        method: 'call',
        params: {
          db: ODOO_CONFIG.database,
          login: typeof credentials === 'string' ? credentials : credentials.username,
          password: typeof credentials === 'string' ? '' : credentials.password,
        },
      });

      if (response.data.result && response.data.result.session_id) {
        this.sessionId = response.data.result.session_id;
        localStorage.setItem('odoo_session_id', this.sessionId);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Authentication failed:', error);
      return false;
    }
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const odooClient = new OdooHttpClient();
```

### Step 3: Product Service Implementation

**File: `src/lib/api/productService.ts`**

```typescript
import { odooClient } from './httpClient';
import { ODOO_CONFIG, OdooResponse, OdooProduct } from './odooConfig';
import { Product, ProductFilters } from '../../types';

export class ProductService {
  /**
   * Fetch products from Odoo with optional filters
   */
  async getProducts(filters?: ProductFilters): Promise<OdooResponse<Product[]>> {
    try {
      // Build Odoo domain filters
      const domain = this.buildOdooDomain(filters);
      
      const response = await odooClient.post<OdooResponse<OdooProduct[]>>(
        ODOO_CONFIG.endpoints.products,
        {
          jsonrpc: '2.0',
          method: 'call',
          params: {
            model: 'product.template',
            method: 'search_read',
            args: [domain],
            kwargs: {
              fields: [
                'id', 'name', 'default_code', 'list_price', 'categ_id',
                'description_sale', 'image_1920', 'qty_available',
                'virtual_available', 'is_published', 'attribute_line_ids'
              ],
              limit: filters?.limit || 50,
              offset: filters?.offset || 0,
              order: 'name asc',
            },
          },
        }
      );

      if (response.success && response.data) {
        const products = response.data.map(this.transformOdooProduct);
        return {
          success: true,
          data: products,
        };
      }

      return {
        success: false,
        error: response.error || 'Failed to fetch products',
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      return {
        success: false,
        error: 'Network error while fetching products',
      };
    }
  }

  /**
   * Get a single product by ID
   */
  async getProduct(productId: number): Promise<OdooResponse<Product>> {
    try {
      const response = await odooClient.post<OdooResponse<OdooProduct>>(
        ODOO_CONFIG.endpoints.products,
        {
          jsonrpc: '2.0',
          method: 'call',
          params: {
            model: 'product.template',
            method: 'read',
            args: [productId],
            kwargs: {
              fields: [
                'id', 'name', 'default_code', 'list_price', 'categ_id',
                'description_sale', 'image_1920', 'qty_available',
                'virtual_available', 'attribute_line_ids', 'variant_ids'
              ],
            },
          },
        }
      );

      if (response.success && response.data) {
        const product = this.transformOdooProduct(response.data);
        return {
          success: true,
          data: product,
        };
      }

      return {
        success: false,
        error: response.error || 'Product not found',
      };
    } catch (error) {
      console.error('Error fetching product:', error);
      return {
        success: false,
        error: 'Network error while fetching product',
      };
    }
  }

  /**
   * Search products by query
   */
  async searchProducts(query: string): Promise<OdooResponse<Product[]>> {
    const filters: ProductFilters = {
      searchQuery: query,
      limit: 20,
    };
    return this.getProducts(filters);
  }

  /**
   * Build Odoo domain filters from frontend filters
   */
  private buildOdooDomain(filters?: ProductFilters): any[] {
    const domain: any[] = [
      ['is_published', '=', true], // Only published products
      ['sale_ok', '=', true],      // Only products available for sale
    ];

    if (filters) {
      // Search query filter
      if (filters.searchQuery) {
        domain.push([
          '|', '|',
          ['name', 'ilike', filters.searchQuery],
          ['default_code', 'ilike', filters.searchQuery],
          ['description_sale', 'ilike', filters.searchQuery]
        ]);
      }

      // Category filter
      if (filters.categories && filters.categories.length > 0) {
        domain.push(['categ_id', 'in', filters.categories]);
      }

      // Price range filter
      if (filters.priceRange) {
        if (filters.priceRange.min > 0) {
          domain.push(['list_price', '>=', filters.priceRange.min]);
        }
        if (filters.priceRange.max < 999999) {
          domain.push(['list_price', '<=', filters.priceRange.max]);
        }
      }

      // Availability filter
      if (filters.inStock) {
        domain.push(['qty_available', '>', 0]);
      }
    }

    return domain;
  }

  /**
   * Transform Odoo product to frontend product format
   */
  private transformOdooProduct(odooProduct: OdooProduct): Product {
    return {
      id: odooProduct.id.toString(),
      name: odooProduct.name,
      sku: odooProduct.default_code || '',
      price: odooProduct.list_price,
      category: odooProduct.categ_id[1], // Category name
      categoryId: odooProduct.categ_id[0].toString(), // Category ID
      description: odooProduct.description_sale || '',
      image: odooProduct.image_1920 ? `data:image/png;base64,${odooProduct.image_1920}` : '/placeholder-product.jpg',
      inStock: odooProduct.qty_available > 0,
      stockQuantity: odooProduct.qty_available,
      isPublished: odooProduct.is_published,
      variants: [], // Will be populated separately if needed
      specifications: [], // Will be populated from attributes
    };
  }
}

export const productService = new ProductService();
```

### Step 4: Redux Integration with Odoo

**File: `src/lib/redux/slices/products.ts` (Updated)**

```typescript
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, ProductFilters } from '../../../types';
import { productService } from '../../api/productService';

interface ProductsState {
  products: Product[];
  filteredProducts: Product[];
  selectedProduct: Product | null;
  filters: ProductFilters;
  loading: boolean;
  error: string | null;
  lastFetch: number | null;
  cache: Record<string, Product>;
}

const initialState: ProductsState = {
  products: [],
  filteredProducts: [],
  selectedProduct: null,
  filters: {
    categories: [],
    subcategories: [],
    brands: [],
    priceRange: { min: 0, max: 10000 },
    searchQuery: '',
    inStock: false,
    limit: 50,
    offset: 0,
  },
  loading: false,
  error: null,
  lastFetch: null,
  cache: {},
};

// Async thunk for fetching products from Odoo
export const fetchProductsFromOdoo = createAsyncThunk(
  'products/fetchFromOdoo',
  async (filters?: ProductFilters, { rejectWithValue }) => {
    try {
      const response = await productService.getProducts(filters);
      
      if (response.success && response.data) {
        return {
          products: response.data,
          filters: filters || {},
          timestamp: Date.now(),
        };
      } else {
        return rejectWithValue(response.error || 'Failed to fetch products');
      }
    } catch (error) {
      return rejectWithValue('Network error while fetching products');
    }
  }
);

// Async thunk for fetching single product
export const fetchProductFromOdoo = createAsyncThunk(
  'products/fetchSingleFromOdoo',
  async (productId: number, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { products: ProductsState };
      
      // Check cache first
      const cachedProduct = state.products.cache[productId.toString()];
      if (cachedProduct) {
        return cachedProduct;
      }

      const response = await productService.getProduct(productId);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Product not found');
      }
    } catch (error) {
      return rejectWithValue('Network error while fetching product');
    }
  }
);

// Async thunk for searching products
export const searchProductsInOdoo = createAsyncThunk(
  'products/searchInOdoo',
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await productService.searchProducts(query);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Search failed');
      }
    } catch (error) {
      return rejectWithValue('Network error during search');
    }
  }
);

export const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<ProductFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    setSelectedProduct: (state, action: PayloadAction<Product | null>) => {
      state.selectedProduct = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch products cases
      .addCase(fetchProductsFromOdoo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductsFromOdoo.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.products;
        state.filteredProducts = action.payload.products;
        state.lastFetch = action.payload.timestamp;
        
        // Update cache
        action.payload.products.forEach(product => {
          state.cache[product.id] = product;
        });
      })
      .addCase(fetchProductsFromOdoo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch single product cases
      .addCase(fetchProductFromOdoo.fulfilled, (state, action) => {
        state.selectedProduct = action.payload;
        state.cache[action.payload.id] = action.payload;
      })
      .addCase(fetchProductFromOdoo.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Search products cases
      .addCase(searchProductsInOdoo.pending, (state) => {
        state.loading = true;
      })
      .addCase(searchProductsInOdoo.fulfilled, (state, action) => {
        state.loading = false;
        state.filteredProducts = action.payload;
      })
      .addCase(searchProductsInOdoo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setFilters, clearFilters, setSelectedProduct, clearError } = productsSlice.actions;
export default productsSlice.reducer;
```

### Step 5: Component Integration

**File: `src/app/(portal)/products/page.tsx`**

```typescript
'use client';

import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../../../lib/redux/hooks';
import { 
  fetchProductsFromOdoo, 
  searchProductsInOdoo, 
  setFilters 
} from '../../../lib/redux/slices/products';
import ProductCard from '../../../components/ProductCard';
import LoadingSpinner from '../../../components/LoadingSpinner';
import ErrorMessage from '../../../components/ErrorMessage';

export default function ProductsPage() {
  const dispatch = useAppDispatch();
  const { 
    filteredProducts, 
    loading, 
    error, 
    filters,
    lastFetch 
  } = useAppSelector((state) => state.products);
  
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Fetch products on mount or if cache is stale (older than 5 minutes)
    const shouldFetch = !lastFetch || (Date.now() - lastFetch > 5 * 60 * 1000);
    
    if (shouldFetch) {
      dispatch(fetchProductsFromOdoo(filters));
    }
  }, [dispatch, filters, lastFetch]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      dispatch(searchProductsInOdoo(query));
    } else {
      dispatch(fetchProductsFromOdoo(filters));
    }
  };

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    dispatch(setFilters(newFilters));
    dispatch(fetchProductsFromOdoo({ ...filters, ...newFilters }));
  };

  if (loading && filteredProducts.length === 0) {
    return <LoadingSpinner message="Loading products from Odoo..." />;
  }

  if (error) {
    return (
      <ErrorMessage 
        message={error} 
        onRetry={() => dispatch(fetchProductsFromOdoo(filters))}
      />
    );
  }

  return (
    <div className="products-page">
      <div className="search-filters">
        <input
          type="text"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          className="search-input"
        />
        
        <select 
          onChange={(e) => handleFilterChange({ 
            categories: e.target.value ? [e.target.value] : [] 
          })}
        >
          <option value="">All Categories</option>
          <option value="laptops">Laptops</option>
          <option value="phones">Phones</option>
        </select>
      </div>

      <div className="products-grid">
        {filteredProducts.map(product => (
          <ProductCard 
            key={product.id} 
            product={product}
            onAddToOrder={(productId) => {
              // Handle add to order
              console.log('Add to order:', productId);
            }}
          />
        ))}
      </div>

      {loading && (
        <div className="loading-overlay">
          <LoadingSpinner message="Updating products..." />
        </div>
      )}
    </div>
  );
}
```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant C as Component
    participant R as Redux Store
    participant S as Service Layer
    participant H as HTTP Client
    participant O as Odoo API
    
    C->>R: dispatch(fetchProductsFromOdoo())
    R->>S: productService.getProducts()
    S->>H: odooClient.post()
    H->>O: HTTP Request with auth
    O-->>H: Product data
    H-->>S: Transformed response
    S-->>R: Products array
    R-->>C: Updated state
    C->>C: Re-render with products
    
    Note over C,O: Real-time updates via WebSocket
    O->>H: Product update notification
    H->>R: dispatch(updateProduct())
    R->>C: State change notification
    C->>C: Re-render with updated data
```

## Error Handling and Retry Logic

**File: `src/lib/api/errorHandler.ts`**

```typescript
export class OdooErrorHandler {
  static async handleApiError(error: any, retryFn?: () => Promise<any>): Promise<any> {
    console.error('Odoo API Error:', error);

    // Network errors
    if (!error.response) {
      if (retryFn) {
        return this.retryWithBackoff(retryFn, 3);
      }
      throw new Error('Network connection failed');
    }

    // HTTP errors
    switch (error.response.status) {
      case 401:
        // Authentication error
        await this.handleAuthError();
        break;
      case 403:
        throw new Error('Access denied');
      case 404:
        throw new Error('Resource not found');
      case 429:
        // Rate limiting
        await this.handleRateLimit(error.response);
        if (retryFn) {
          return retryFn();
        }
        break;
      case 500:
        throw new Error('Server error');
      default:
        throw new Error(`API error: ${error.response.status}`);
    }
  }

  private static async retryWithBackoff(fn: () => Promise<any>, maxRetries: number): Promise<any> {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
  }

  private static async handleAuthError(): Promise<void> {
    // Clear stored tokens
    localStorage.removeItem('odoo_session_id');
    localStorage.removeItem('odoo_refresh_token');
    
    // Redirect to login
    window.location.href = '/login';
  }

  private static async handleRateLimit(response: any): Promise<void> {
    const retryAfter = response.headers['retry-after'] || 1;
    await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
  }
}
```

## Environment Configuration

**File: `.env.local`**

```bash
# Odoo Configuration
NEXT_PUBLIC_ODOO_URL=https://your-odoo-instance.com
NEXT_PUBLIC_ODOO_DB=your_database_name
ODOO_API_KEY=your_api_key
ODOO_USERNAME=api_user
ODOO_PASSWORD=api_password

# Development vs Production
NODE_ENV=development
NEXT_PUBLIC_API_MOCK=false
```

## Testing Odoo Integration

**File: `src/lib/api/__tests__/productService.test.ts`**

```typescript
import { productService } from '../productService';
import { odooClient } from '../httpClient';

// Mock the HTTP client
jest.mock('../httpClient');
const mockedOdooClient = odooClient as jest.Mocked<typeof odooClient>;

describe('ProductService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch products successfully', async () => {
    const mockResponse = {
      success: true,
      data: [
        {
          id: 1,
          name: 'Test Product',
          default_code: 'TEST001',
          list_price: 100,
          categ_id: [1, 'Electronics'],
        }
      ]
    };

    mockedOdooClient.post.mockResolvedValue(mockResponse);

    const result = await productService.getProducts();

    expect(result.success).toBe(true);
    expect(result.data).toHaveLength(1);
    expect(result.data![0].name).toBe('Test Product');
  });

  it('should handle API errors gracefully', async () => {
    mockedOdooClient.post.mockRejectedValue(new Error('Network error'));

    const result = await productService.getProducts();

    expect(result.success).toBe(false);
    expect(result.error).toBe('Network error while fetching products');
  });
});
```

## Performance Optimization

### 1. **Caching Strategy**
- Cache products in Redux store
- Implement cache invalidation
- Use browser storage for offline access

### 2. **Request Optimization**
- Batch API requests when possible
- Implement request deduplication
- Use pagination for large datasets

### 3. **Real-time Updates**
- WebSocket integration for live updates
- Optimistic updates for better UX
- Background sync for offline changes

## Conclusion

This Odoo integration provides:

1. **Robust API Communication**: Type-safe HTTP client with error handling
2. **Redux Integration**: Seamless state management with async operations
3. **Error Resilience**: Comprehensive error handling and retry logic
4. **Performance**: Caching and optimization strategies
5. **Testing**: Comprehensive test coverage for reliability

The integration ensures reliable communication between the Next.js frontend and Odoo backend while maintaining excellent user experience and developer productivity.
