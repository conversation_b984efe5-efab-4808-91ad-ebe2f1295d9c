# POC 011 - Redux Implementation Guide

## Overview

This POC demonstrates how to implement Redux Toolkit in the KAUST B2B Marketplace project with real examples from our codebase. Redux is used for centralized state management across the application.

## When to Apply Redux

### ✅ **Use Redux When:**
- **Complex State Sharing**: Multiple components need access to the same data
- **State Persistence**: Need to maintain state across page navigation
- **Async Operations**: Managing loading states, errors, and API calls
- **Undo/Redo Functionality**: Need to track state changes
- **Real-time Updates**: WebSocket updates need to sync across components
- **Large Applications**: Complex B2B workflows with multiple data entities

### ❌ **Don't Use Redux When:**
- **Simple Local State**: Component-specific state (form inputs, toggles)
- **Static Data**: Data that doesn't change (configuration, constants)
- **Small Applications**: Simple apps with minimal state sharing
- **Server State**: Use React Query/SWR for server state management

## Step-by-Step Implementation

### Step 1: Install Dependencies

```bash
# Install Redux Toolkit and React Redux
bun add @reduxjs/toolkit react-redux

# Install TypeScript types (if using TypeScript)
bun add -D @types/react-redux
```

### Step 2: Create Store Configuration

**File: `src/lib/redux/store.ts`**

```typescript
import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import ordersReducer from './slices/orders';
import productsReducer from './slices/products';
import wishlistReducer from './slices/wishlist';

// Define the store configuration
const createStore = () => {
  return configureStore({
    reducer: {
      orders: ordersReducer,
      products: productsReducer,
      wishlist: wishlistReducer,
    },
    // Enable Redux DevTools in development
    devTools: process.env.NODE_ENV !== 'production',
  });
};

// Create a store instance for the client side
let clientStore: ReturnType<typeof createStore> | undefined;

// Get or create the store
export const getStore = () => {
  // For SSR, always create a new store
  if (typeof window === 'undefined') {
    return createStore();
  }

  // Create the store once in the client
  if (!clientStore) {
    clientStore = createStore();
  }

  return clientStore;
};

// For convenience, also export a store instance
export const store = getStore();

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Define the AppThunk type for async actions
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
```

### Step 3: Create Redux Slice (Real Example - Orders)

**File: `src/lib/redux/slices/orders.ts`**

```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Order, OrderItem } from '../../../types';
import { v4 as uuidv4 } from 'uuid';

interface OrdersState {
  orders: Order[];
  activeOrderId: string | null;
}

const initialState: OrdersState = {
  orders: [],
  activeOrderId: null,
};

export const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    // Create a new order
    createOrder: (state, action: PayloadAction<{ name: string }>) => {
      const newOrder: Order = {
        id: uuidv4(),
        name: action.payload.name,
        items: [],
        state: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      state.orders.push(newOrder);

      // Set as active order if no active order exists
      if (!state.activeOrderId) {
        state.activeOrderId = newOrder.id;
      }
    },

    // Set active order
    setActiveOrder: (state, action: PayloadAction<string>) => {
      state.activeOrderId = action.payload;
    },

    // Add item to order
    addItemToOrder: (state, action: PayloadAction<{ orderId: string; item: OrderItem }>) => {
      const { orderId, item } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && order.state === 'draft') {
        // Check if item already exists
        const existingItemIndex = order.items.findIndex(i =>
          i.productId === item.productId &&
          i.selectedVariantId === item.selectedVariantId
        );

        if (existingItemIndex >= 0) {
          // Update quantity if item exists
          order.items[existingItemIndex].quantity += item.quantity;
        } else {
          // Add new item
          order.items.push(item);
        }

        order.updatedAt = new Date().toISOString();
      }
    },

    // Update item quantity
    updateItemQuantity: (state, action: PayloadAction<{ orderId: string; itemIndex: number; quantity: number }>) => {
      const { orderId, itemIndex, quantity } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && (order.state === 'draft' || order.state === 'submitted') && itemIndex >= 0 && itemIndex < order.items.length) {
        order.items[itemIndex].quantity = Math.max(1, quantity);
        order.updatedAt = new Date().toISOString();
      }
    },

    // Submit order
    submitOrder: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && order.state === 'draft' && order.items.length > 0) {
        order.state = 'submitted';
        order.updatedAt = new Date().toISOString();
      }
    },
  },
});

export const {
  createOrder,
  setActiveOrder,
  addItemToOrder,
  updateItemQuantity,
  submitOrder,
} = ordersSlice.actions;

export default ordersSlice.reducer;
```

### Step 4: Create Typed Hooks

**File: `src/lib/redux/hooks.ts`**

```typescript
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './store';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
```

### Step 5: Setup Redux Provider

**File: `src/app/layout.tsx`**

```typescript
'use client';

import { Provider } from 'react-redux';
import { store } from '../lib/redux/store';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <Provider store={store}>
          {children}
        </Provider>
      </body>
    </html>
  );
}
```

### Step 6: Use Redux in Components (Real Example)

**File: `src/components/OrderSelector.tsx`**

```typescript
'use client';

import { useState } from 'react';
import { useAppDispatch, useAppSelector } from '../lib/redux/hooks';
import { createOrder, setActiveOrder } from '../lib/redux/slices/orders';

export default function OrderSelector() {
  const dispatch = useAppDispatch();
  const { orders, activeOrderId } = useAppSelector((state) => state.orders);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [newOrderName, setNewOrderName] = useState('');

  const activeOrder = orders.find(o => o.id === activeOrderId);

  const handleOrderSelect = (orderId: string) => {
    dispatch(setActiveOrder(orderId));
  };

  const handleCreateOrder = () => {
    if (newOrderName.trim()) {
      dispatch(createOrder({ name: newOrderName.trim() }));
      setNewOrderName('');
      setIsCreatingOrder(false);
    }
  };

  return (
    <div className="order-selector">
      <h3>Active Order: {activeOrder?.name || 'No Order'}</h3>
      
      <select 
        value={activeOrderId || ''} 
        onChange={(e) => handleOrderSelect(e.target.value)}
      >
        <option value="">Select Order</option>
        {orders.map(order => (
          <option key={order.id} value={order.id}>
            {order.name} ({order.items.length} items)
          </option>
        ))}
      </select>

      {isCreatingOrder ? (
        <div>
          <input
            type="text"
            value={newOrderName}
            onChange={(e) => setNewOrderName(e.target.value)}
            placeholder="Order name"
          />
          <button onClick={handleCreateOrder}>Create</button>
          <button onClick={() => setIsCreatingOrder(false)}>Cancel</button>
        </div>
      ) : (
        <button onClick={() => setIsCreatingOrder(true)}>
          New Order
        </button>
      )}
    </div>
  );
}
```

### Step 7: Async Actions with Thunks (Real Example)

**File: `src/lib/redux/slices/products.ts`**

```typescript
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, FilterState } from '../../../types';
import { fetchProducts } from '../../api/odoo';

interface ProductsState {
  products: Product[];
  filteredProducts: Product[];
  filters: FilterState;
  loading: boolean;
  error: string | null;
}

const initialState: ProductsState = {
  products: [],
  filteredProducts: [],
  filters: {
    categories: [],
    subcategories: [],
    brands: [],
    priceRange: { min: 0, max: 10000 },
    searchQuery: '',
  },
  loading: false,
  error: null,
};

// Async thunk for fetching products
export const getProducts = createAsyncThunk(
  'products/getProducts',
  async (_, { rejectWithValue }) => {
    try {
      const products = await fetchProducts();
      return products;
    } catch (error) {
      return rejectWithValue('Failed to fetch products');
    }
  }
);

export const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.filters.searchQuery = action.payload;
      // Apply filters immediately
      applyFilters(state);
    },
    
    setCategoryFilters: (state, action: PayloadAction<string[]>) => {
      state.filters.categories = action.payload;
      applyFilters(state);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
        state.filteredProducts = action.payload;
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Helper function to apply filters
function applyFilters(state: ProductsState) {
  let filtered = state.products;

  // Apply search filter
  if (state.filters.searchQuery) {
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(state.filters.searchQuery.toLowerCase())
    );
  }

  // Apply category filter
  if (state.filters.categories.length > 0) {
    filtered = filtered.filter(product =>
      state.filters.categories.includes(product.category)
    );
  }

  state.filteredProducts = filtered;
}

export const { setSearchQuery, setCategoryFilters } = productsSlice.actions;
export default productsSlice.reducer;
```

### Step 8: Using Async Actions in Components

**File: `src/app/(portal)/products/page.tsx`**

```typescript
'use client';

import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../lib/redux/hooks';
import { getProducts, setSearchQuery } from '../../../lib/redux/slices/products';
import ProductCard from '../../../components/ProductCard';

export default function ProductsPage() {
  const dispatch = useAppDispatch();
  const { filteredProducts, loading, error } = useAppSelector((state) => state.products);

  useEffect(() => {
    // Fetch products when component mounts
    dispatch(getProducts());
  }, [dispatch]);

  const handleSearch = (query: string) => {
    dispatch(setSearchQuery(query));
  };

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <input
        type="text"
        placeholder="Search products..."
        onChange={(e) => handleSearch(e.target.value)}
      />
      
      <div className="products-grid">
        {filteredProducts.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}
```

## Redux DevTools Setup

### Install Browser Extension
1. Install Redux DevTools extension for Chrome/Firefox
2. The store configuration already includes DevTools support

### Using DevTools
- **Time Travel**: Navigate through state changes
- **Action Inspection**: See all dispatched actions
- **State Diff**: Compare state before/after actions
- **Performance**: Monitor action performance

## Best Practices

### 1. **Slice Organization**
```
src/lib/redux/slices/
├── orders.ts      # Order management
├── products.ts    # Product catalog
├── wishlist.ts    # User wishlist
└── auth.ts        # Authentication
```

### 2. **State Shape Design**
- Keep state normalized (avoid nested objects)
- Use IDs for relationships
- Separate loading/error states per slice

### 3. **Action Naming**
- Use descriptive action names: `addItemToOrder` not `addItem`
- Group related actions in same slice
- Use consistent naming patterns

### 4. **Performance Optimization**
- Use `useAppSelector` with specific selectors
- Memoize expensive calculations
- Avoid subscribing to entire state

### 5. **Error Handling**
```typescript
// In async thunks
export const fetchUserData = createAsyncThunk(
  'user/fetchData',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await api.getUser(userId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);
```

## Common Patterns in Our Project

### 1. **Entity Management Pattern**
```typescript
// Managing collections of entities (orders, products)
interface EntityState<T> {
  items: Record<string, T>;
  ids: string[];
  loading: boolean;
  error: string | null;
}
```

### 2. **Active Item Pattern**
```typescript
// Managing active/selected items
interface ActiveItemState {
  items: Item[];
  activeItemId: string | null;
}
```

### 3. **Filter Pattern**
```typescript
// Managing filters and search
interface FilterableState {
  allItems: Item[];
  filteredItems: Item[];
  filters: FilterState;
}
```

## Testing Redux

### Testing Reducers
```typescript
import { ordersSlice, createOrder } from './orders';

describe('orders reducer', () => {
  it('should create a new order', () => {
    const initialState = { orders: [], activeOrderId: null };
    const action = createOrder({ name: 'Test Order' });
    const newState = ordersSlice.reducer(initialState, action);
    
    expect(newState.orders).toHaveLength(1);
    expect(newState.orders[0].name).toBe('Test Order');
  });
});
```

### Testing Components with Redux
```typescript
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../lib/redux/store';
import OrderSelector from './OrderSelector';

const renderWithRedux = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

test('renders order selector', () => {
  renderWithRedux(<OrderSelector />);
  // Test component behavior
});
```

## Conclusion

Redux Toolkit provides a powerful and standardized way to manage complex state in our B2B marketplace. The key is to:

1. **Start Simple**: Begin with basic slices and gradually add complexity
2. **Follow Patterns**: Use consistent patterns across slices
3. **Type Everything**: Leverage TypeScript for better developer experience
4. **Test Thoroughly**: Test reducers and components separately
5. **Use DevTools**: Take advantage of Redux DevTools for debugging

This implementation provides a solid foundation for managing state in a complex B2B application with multiple entities, async operations, and real-time updates.
