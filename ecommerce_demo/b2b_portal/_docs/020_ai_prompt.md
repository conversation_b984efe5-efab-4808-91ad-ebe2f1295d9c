

### 🏢 **Prompt: Vendor-Side Use Case Diagram (Zenith Arabia)**

> Generate a **use case diagram** for the **vendor-side (Zenith Arabia)** of a B2B e-commerce system.
>
> ### 🎭 Actors:
>
> * **Vendor Merchandiser**: manages product catalogs and SKUs.
> * **Vendor Account Manager**: creates price lists, manages frame contracts, handles customer content.
> * **Vendor Admin**: sets system configurations and user roles.
>
> ### ✅ Use Cases:
>
> * Manage Product Catalog
> * Define Product Variants and SKUs
> * Create and Maintain Price Lists
> * Define and Monitor Frame Contracts
> * Link Price Lists to Frame Contracts
> * Manage Customer Profiles and CMS Content
> * View Reports on Customer Activities and Purchase Orders
> * Configure System Settings
> * Assign Roles and Permissions
>

---

### 🏫 **Prompt: Customer-Side Use Case Diagram (KAUST)**

> Generate a **use case diagram** for the **customer-side (KAUST)** of a B2B e-commerce system.
>
> ### 🎭 Actors:
>
> * **Customer Purchasing Representative**: browses catalog, creates and submits purchase orders (POs), negotiates terms.
> * **Customer Purchasing Manager**: approves POs, views contract details.
> * **Customer Admin/IT**: manages user access via Nafath Identity Verification.
>
> ### ✅ Use Cases:
>
> * Login via Nafath Verification
> * Browse Custom Catalog
> * Create and Edit Multiple Purchase Orders
> * Submit Purchase Orders
> * Negotiate Purchase Orders with Vendor
> * Track PO Status
> * Reorder from Past POs
> * Approve Purchase Orders
> * View Contract and Pricing Details
> * Manage Internal User Roles and Access
>

---

