# B2B Customer Portal E-commerce Architecture
## Technical Deep Dive: Multi-Tenant Customer Portal with Cart Management

---

## Executive Summary

This document provides a comprehensive technical architecture for the B2B customer portal, using e-commerce naming conventions (cart instead of quotation) while maintaining B2B functionality. The portal serves business customers like KAUST through a multi-tenant, multi-language, and highly customizable interface.

### Key Architectural Principles
- **Multi-Tenant Architecture**: Customer-specific branding and routing
- **E-commerce Conventions**: Cart-based workflow with B2B features
- **Role-Based Access**: Company users with different responsibilities
- **Multi-Language Support**: Arabic and English with RTL support
- **Theme Customization**: Customer-specific branding and colors
- **Multiple Cart Management**: Active cart switching and persistence

---

## Business Context & Requirements Analysis

### Customer Organization Structure
```mermaid
graph TB
    subgraph "KAUST Organization"
        A[Company Admin]
        B[Department Manager]
        C[Purchasing Representative]
        D[Finance Approver]
        E[End User/Requester]
    end
    
    subgraph "User Roles & Permissions"
        F[Create Users - Admin Only]
        G[Approve Purchases - Manager/Finance]
        H[Create Carts - All Users]
        I[Submit Orders - Rep/Manager]
        J[View Reports - Admin/Manager]
    end
    
    A --> F
    B --> G
    C --> H
    D --> G
    E --> H
    B --> I
    C --> I
    A --> J
    B --> J
```

### Multi-Cart Business Scenarios
1. **Department Separation**: Different carts for different departments
2. **Project-Based**: Separate carts for different projects
3. **Budget Cycles**: Monthly/quarterly budget-specific carts
4. **Approval Workflows**: Draft carts vs. submitted carts
5. **Negotiation Tracking**: Original cart vs. revised cart versions

---

## Frontend Architecture (Next.js)

### Multi-Tenant Routing Structure
```
customer-portal/
├── app/
│   ├── [customer-slug]/              # Dynamic customer routing
│   │   ├── layout.tsx               # Customer-specific layout
│   │   ├── page.tsx                 # Customer dashboard
│   │   ├── catalog/                 # Product catalog
│   │   │   ├── page.tsx            # Catalog listing
│   │   │   ├── [category]/         # Category pages
│   │   │   └── [product]/          # Product details
│   │   ├── cart/                    # Cart management
│   │   │   ├── page.tsx            # Active cart view
│   │   │   ├── list/               # All carts list
│   │   │   └── [cart-id]/          # Specific cart details
│   │   ├── orders/                  # Order management
│   │   │   ├── page.tsx            # Order history
│   │   │   └── [order-id]/         # Order details
│   │   ├── contracts/               # Contract management
│   │   │   ├── page.tsx            # Active contracts
│   │   │   └── [contract-id]/      # Contract details
│   │   ├── profile/                 # User & company profile
│   │   │   ├── page.tsx            # Profile overview
│   │   │   ├── company/            # Company settings
│   │   │   └── users/              # User management
│   │   └── analytics/               # Reports & analytics
│   ├── auth/                        # Authentication pages
│   │   ├── login/                  # Public login
│   │   ├── nafath/                 # Nafath integration
│   │   └── callback/               # Auth callbacks
│   └── api/                         # API routes
│       ├── auth/                   # Authentication APIs
│       ├── cart/                   # Cart management APIs
│       ├── catalog/                # Product APIs
│       └── customer/               # Customer APIs
├── components/
│   ├── layout/
│   │   ├── CustomerLayout.tsx      # Customer-specific layout
│   │   ├── Navigation.tsx          # Multi-language navigation
│   │   └── ThemeProvider.tsx       # Dynamic theming
│   ├── cart/
│   │   ├── CartManager.tsx         # Multiple cart management
│   │   ├── CartSwitcher.tsx        # Cart switching component
│   │   ├── CartItems.tsx           # Cart items display
│   │   └── CartNegotiation.tsx     # Negotiation interface
│   ├── catalog/
│   │   ├── ProductGrid.tsx         # Product listing
│   │   ├── ProductCard.tsx         # Product card component
│   │   ├── ProductFilter.tsx       # Advanced filtering
│   │   └── ProductSearch.tsx       # Search with autocomplete
│   ├── auth/
│   │   ├── PublicLogin.tsx         # Step 1: Public authentication
│   │   ├── NafathLogin.tsx         # Step 2: Nafath verification
│   │   └── UserContext.tsx         # User context provider
│   └── ui/
│       ├── ThemeCustomizer.tsx     # Customer theme components
│       ├── LanguageSwitcher.tsx    # Arabic/English switcher
│       └── RTLProvider.tsx         # RTL support
├── lib/
│   ├── auth/
│   │   ├── nafath.ts              # Nafath integration
│   │   ├── session.ts             # Session management
│   │   └── permissions.ts         # Role-based permissions
│   ├── cart/
│   │   ├── cartManager.ts         # Cart state management
│   │   ├── cartPersistence.ts     # Auto-save functionality
│   │   └── cartNegotiation.ts     # Negotiation logic
│   ├── theme/
│   │   ├── customerThemes.ts      # Customer-specific themes
│   │   ├── colorPalettes.ts       # Brand color management
│   │   └── rtlSupport.ts          # RTL layout support
│   └── i18n/
│       ├── config.ts              # Internationalization config
│       ├── ar.json                # Arabic translations
│       └── en.json                # English translations
├── stores/
│   ├── authStore.ts               # Authentication state
│   ├── cartStore.ts               # Cart management state
│   ├── customerStore.ts           # Customer context state
│   └── themeStore.ts              # Theme and language state
└── types/
    ├── auth.ts                    # Authentication types
    ├── cart.ts                    # Cart and order types
    ├── customer.ts                # Customer and user types
    └── theme.ts                   # Theme and branding types
```

### Multi-Language & RTL Support Architecture

#### Language Configuration
```typescript
// lib/i18n/config.ts
export const languages = {
  en: {
    code: 'en',
    name: 'English',
    direction: 'ltr',
    flag: '🇺🇸'
  },
  ar: {
    code: 'ar',
    name: 'العربية',
    direction: 'rtl',
    flag: '🇸🇦'
  }
}

export const defaultLanguage = 'en'
export const fallbackLanguage = 'en'
```

#### Theme Customization Architecture
```typescript
// types/theme.ts
export interface CustomerTheme {
  customerId: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  logoUrl: string
  faviconUrl: string
  fontFamily: string
  borderRadius: string
  customCSS?: string
  darkMode: {
    enabled: boolean
    primaryColor: string
    secondaryColor: string
    backgroundColor: string
  }
}

export interface ThemeConfig {
  light: CustomerTheme
  dark: CustomerTheme
  rtl: {
    enabled: boolean
    customizations: Record<string, string>
  }
}
```

---

## Authentication Architecture

### Two-Step Authentication Flow
```mermaid
sequenceDiagram
    participant User
    participant Portal
    participant PublicAuth
    participant Nafath
    participant Backend
    participant CustomerDB
    
    User->>Portal: Access /kaust portal
    Portal->>PublicAuth: Step 1: Public Login
    PublicAuth->>Portal: Basic session created
    
    Portal->>Nafath: Step 2: Nafath Verification
    Nafath->>Portal: National ID verified
    
    Portal->>Backend: Validate user & get B2B context
    Backend->>CustomerDB: Get customer permissions
    CustomerDB->>Backend: Return user roles & access
    Backend->>Portal: Full B2B session established
    
    Portal->>User: Access granted with customer context
```

### User Role Management
```typescript
// types/auth.ts
export interface UserRole {
  id: string
  name: string
  permissions: Permission[]
  level: 'admin' | 'manager' | 'user' | 'viewer'
}

export interface Permission {
  resource: 'cart' | 'order' | 'user' | 'contract' | 'analytics'
  actions: ('create' | 'read' | 'update' | 'delete' | 'approve')[]
  conditions?: {
    department?: string[]
    budgetLimit?: number
    approvalRequired?: boolean
  }
}

export interface B2BUser {
  id: string
  email: string
  nationalId: string
  nafathVerified: boolean
  customerId: string
  roles: UserRole[]
  department: string
  manager?: string
  preferences: {
    language: 'en' | 'ar'
    theme: 'light' | 'dark'
    notifications: NotificationSettings
  }
}
```

---

## Cart Management Architecture

### Multiple Cart System
```typescript
// types/cart.ts
export interface Cart {
  id: string
  name: string
  customerId: string
  userId: string
  status: CartStatus
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  expiresAt?: Date
  
  // Cart Content
  items: CartItem[]
  subtotal: number
  tax: number
  total: number
  currency: string
  
  // B2B Specific
  department: string
  project?: string
  budgetCode?: string
  approvalRequired: boolean
  contractId?: string
  
  // Negotiation
  negotiationThread: NegotiationMessage[]
  targetPrices: Record<string, number>
  vendorNotes?: string
  
  // Workflow
  submittedAt?: Date
  approvedAt?: Date
  rejectedAt?: Date
  convertedAt?: Date
}

export type CartStatus = 
  | 'draft'
  | 'submitted_for_review'
  | 'under_review'
  | 'revised'
  | 'approved'
  | 'rejected'
  | 'expired'
  | 'converted_to_order'

export interface CartItem {
  id: string
  productId: string
  variantId?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  targetPrice?: number
  specifications?: Record<string, any>
  notes?: string
  negotiationStatus?: 'pending' | 'accepted' | 'rejected'
}
```

### Cart State Management
```typescript
// stores/cartStore.ts
export interface CartStore {
  // State
  carts: Cart[]
  activeCartId: string | null
  loading: boolean
  error: string | null
  
  // Actions
  createCart: (name: string, options?: CartOptions) => Promise<Cart>
  switchCart: (cartId: string) => Promise<void>
  updateCart: (cartId: string, updates: Partial<Cart>) => Promise<void>
  deleteCart: (cartId: string) => Promise<void>
  
  // Cart Items
  addItem: (cartId: string, item: Omit<CartItem, 'id'>) => Promise<void>
  updateItem: (cartId: string, itemId: string, updates: Partial<CartItem>) => Promise<void>
  removeItem: (cartId: string, itemId: string) => Promise<void>
  bulkAddItems: (cartId: string, items: Omit<CartItem, 'id'>[]) => Promise<void>
  
  // Workflow
  submitCart: (cartId: string) => Promise<void>
  approveCart: (cartId: string) => Promise<void>
  rejectCart: (cartId: string, reason: string) => Promise<void>
  convertToOrder: (cartId: string) => Promise<string>
  
  // Negotiation
  addNegotiationMessage: (cartId: string, message: NegotiationMessage) => Promise<void>
  updateTargetPrice: (cartId: string, itemId: string, targetPrice: number) => Promise<void>
  
  // Persistence
  autoSave: (cartId: string) => Promise<void>
  loadCarts: () => Promise<void>
}
```

---

## Product Catalog Architecture

### Advanced Search & Filtering
```typescript
// types/catalog.ts
export interface ProductFilter {
  categories: string[]
  priceRange: {
    min: number
    max: number
    currency: string
  }
  availability: 'in_stock' | 'out_of_stock' | 'pre_order' | 'all'
  brands: string[]
  attributes: Record<string, string[]>
  contractOnly: boolean
  customerSpecific: boolean
}

export interface SearchQuery {
  query: string
  filters: ProductFilter
  sort: {
    field: 'name' | 'price' | 'popularity' | 'newest'
    direction: 'asc' | 'desc'
  }
  pagination: {
    page: number
    limit: number
  }
}

export interface Product {
  id: string
  sku: string
  name: string
  description: string
  category: ProductCategory
  brand: string
  images: ProductImage[]
  variants: ProductVariant[]
  attributes: ProductAttribute[]
  
  // B2B Pricing
  pricing: {
    basePrice: number
    customerPrice?: number
    contractPrice?: number
    currency: string
    priceBreaks: PriceBreak[]
  }
  
  // Availability
  availability: {
    status: 'in_stock' | 'out_of_stock' | 'pre_order'
    quantity: number
    leadTime: number
    location: string
  }
  
  // B2B Specific
  contractRequired: boolean
  minimumOrderQuantity: number
  maximumOrderQuantity?: number
  accessories: string[]
  relatedProducts: string[]
}
```

---

## Negotiation & Communication Architecture

### Real-time Negotiation System
The portal supports real-time negotiation between customers and vendors through:

#### Negotiation Thread Features
- **Item-level Discussions**: Separate threads for each cart item
- **Price Proposals**: Customers can propose target prices with justification
- **Vendor Responses**: Real-time vendor feedback and counter-proposals
- **Attachment Support**: Documents, specifications, and images
- **Status Tracking**: Visual indicators for negotiation progress

#### Communication Channels
- **In-app Messaging**: Real-time chat within the portal
- **Email Notifications**: Automatic email updates for important events
- **SMS Alerts**: Critical updates via SMS for urgent matters
- **Push Notifications**: Browser notifications for active users

### Target Price Management
- **Item-level Targeting**: Set target prices for individual cart items
- **Bulk Price Requests**: Request pricing for multiple items simultaneously
- **Historical Pricing**: View past pricing for similar orders
- **Market Comparison**: Compare prices with market benchmarks
- **Approval Workflows**: Manager approval for significant price deviations

---

## Customer Dashboard Architecture

### Multi-Role Dashboard Design
The dashboard adapts based on user roles and responsibilities:

#### Company Administrator Dashboard
- **User Management**: Create, approve, and manage company users
- **Spending Analytics**: Company-wide spending reports and trends
- **Contract Overview**: All active contracts and SLA compliance
- **Approval Workflows**: Pending approvals and workflow management
- **Security Settings**: Company security policies and access controls

#### Department Manager Dashboard
- **Department Analytics**: Department-specific spending and orders
- **Team Management**: Manage department users and permissions
- **Budget Tracking**: Monitor department budget utilization
- **Approval Queue**: Review and approve department purchases
- **Performance Metrics**: Department procurement performance

#### Purchasing Representative Dashboard
- **Active Carts**: Quick access to all active carts
- **Order Tracking**: Real-time order status and delivery tracking
- **Product Favorites**: Frequently ordered products and quick reorder
- **Negotiation Status**: Active negotiations and pending responses
- **Personal Analytics**: Individual purchasing history and patterns

#### End User Dashboard
- **Simple Cart Management**: Basic cart creation and item addition
- **Request Submission**: Submit purchase requests for approval
- **Order History**: View personal order history and status
- **Product Search**: Easy product discovery and selection
- **Notification Center**: Updates on submitted requests

### Analytics & Reporting Framework

#### Spending Analytics
- **Category Breakdown**: Spending by product categories
- **Trend Analysis**: Monthly, quarterly, and yearly trends
- **Budget Utilization**: Real-time budget tracking and alerts
- **Cost Savings**: Negotiation savings and contract benefits
- **Vendor Performance**: Supplier performance metrics

#### Order Analytics
- **Order Velocity**: Order frequency and patterns
- **Delivery Performance**: On-time delivery and SLA compliance
- **Product Usage**: Most ordered products and categories
- **Seasonal Trends**: Seasonal ordering patterns
- **Approval Efficiency**: Time-to-approval metrics

#### Contract Analytics
- **Utilization Rates**: Contract usage and remaining capacity
- **SLA Compliance**: Service level agreement performance
- **Renewal Alerts**: Contract expiration and renewal notifications
- **Cost Comparison**: Contract vs. market pricing analysis
- **Performance Scoring**: Vendor performance against SLAs

---

## Multi-Language & Localization Architecture

### Arabic & English Support
The portal provides comprehensive bilingual support:

#### Language Features
- **Dynamic Language Switching**: Instant language change without page reload
- **RTL Layout Support**: Proper right-to-left layout for Arabic
- **Cultural Adaptations**: Date formats, number formats, and currency display
- **Content Localization**: All UI elements, messages, and content translated
- **Mixed Content**: Support for mixed Arabic-English content

#### RTL Design Considerations
- **Layout Mirroring**: Automatic layout direction changes
- **Icon Adjustments**: Directional icons flip appropriately
- **Text Alignment**: Proper text alignment for each language
- **Form Layouts**: RTL-optimized form designs
- **Navigation Flow**: Intuitive navigation in both directions

### Theme Customization Architecture

#### Customer-Specific Branding
Each customer organization can customize:

#### Visual Branding
- **Logo Integration**: Company logo in header and throughout portal
- **Color Schemes**: Primary, secondary, and accent colors
- **Typography**: Custom font families and sizing
- **Layout Preferences**: Grid layouts, spacing, and component arrangements
- **Custom CSS**: Advanced customizations through CSS overrides

#### Content Customization
- **Welcome Messages**: Personalized welcome content
- **Announcements**: Customer-specific announcements and news
- **Help Content**: Customized help documentation and FAQs
- **Terms & Conditions**: Customer-specific terms and policies
- **Contact Information**: Dedicated support contacts and information

#### Dark/Light Theme Support
- **Automatic Detection**: System preference detection
- **Manual Toggle**: User preference override
- **Consistent Branding**: Brand colors work in both themes
- **Accessibility**: High contrast and readability in both modes
- **Component Adaptation**: All components support both themes

---

## User Management & Permissions Architecture

### Role-Based Access Control (RBAC)

#### Permission Matrix
The system implements granular permissions across resources:

#### Resource-Level Permissions
- **Cart Management**: Create, view, edit, delete, submit carts
- **Order Management**: View orders, track shipments, cancel orders
- **User Management**: Invite users, assign roles, deactivate accounts
- **Contract Access**: View contracts, access pricing, monitor SLAs
- **Analytics Access**: View reports, export data, access insights

#### Conditional Permissions
- **Department Restrictions**: Access limited to specific departments
- **Budget Limits**: Spending limits based on user role
- **Approval Requirements**: Automatic approval workflows for large orders
- **Time-based Access**: Temporary permissions for specific periods
- **Location-based**: Access restrictions based on user location

### User Lifecycle Management

#### User Onboarding Process
1. **Invitation**: Admin sends invitation with role assignment
2. **Registration**: User completes profile with Nafath verification
3. **Approval**: Admin approves new user account
4. **Training**: Guided tour and feature introduction
5. **Activation**: Full access granted with monitoring period

#### User Maintenance
- **Regular Reviews**: Periodic access reviews and updates
- **Role Changes**: Promotion, demotion, or lateral role changes
- **Deactivation**: Temporary or permanent account deactivation
- **Data Retention**: User data handling after deactivation
- **Audit Trails**: Complete user activity logging

---

## Performance & Optimization Architecture

### Frontend Performance Strategy

#### Loading Optimization
- **Code Splitting**: Route-based and component-based splitting
- **Lazy Loading**: On-demand loading of heavy components
- **Image Optimization**: WebP format, responsive images, lazy loading
- **Bundle Analysis**: Regular bundle size monitoring and optimization
- **CDN Integration**: Global content delivery for static assets

#### Caching Strategy
- **Browser Caching**: Aggressive caching of static resources
- **API Response Caching**: Smart caching of API responses
- **State Persistence**: Local storage for cart and user preferences
- **Service Worker**: Offline functionality and background sync
- **Memory Management**: Efficient memory usage and cleanup

### Backend Integration Optimization

#### API Efficiency
- **Request Batching**: Combine multiple API calls
- **Response Compression**: Gzip compression for all responses
- **Pagination**: Efficient pagination for large datasets
- **Field Selection**: Request only needed data fields
- **Caching Headers**: Proper cache control headers

#### Real-time Features
- **WebSocket Connections**: Efficient real-time communication
- **Event Streaming**: Server-sent events for updates
- **Connection Pooling**: Efficient connection management
- **Heartbeat Monitoring**: Connection health monitoring
- **Graceful Degradation**: Fallback for connection issues

---

## Security & Compliance Architecture

### Data Protection Framework

#### Privacy by Design
- **Data Minimization**: Collect only necessary data
- **Purpose Limitation**: Use data only for stated purposes
- **Storage Limitation**: Automatic data retention policies
- **Consent Management**: Granular consent tracking
- **Right to Erasure**: Complete data deletion capabilities

#### Security Measures
- **Encryption at Rest**: All sensitive data encrypted
- **Encryption in Transit**: TLS 1.3 for all communications
- **Input Validation**: Comprehensive input sanitization
- **Output Encoding**: XSS prevention through encoding
- **CSRF Protection**: Cross-site request forgery prevention

### Audit & Compliance

#### Audit Trail System
- **User Actions**: Complete user activity logging
- **Data Changes**: Track all data modifications
- **Access Logs**: Monitor system access patterns
- **Security Events**: Log security-related events
- **Compliance Reports**: Automated compliance reporting

#### Regulatory Compliance
- **GDPR Compliance**: European data protection compliance
- **Saudi Data Laws**: Local data protection requirements
- **Industry Standards**: Relevant industry compliance standards
- **Regular Audits**: Scheduled security and compliance audits
- **Incident Response**: Comprehensive incident response procedures

---

## Integration Architecture

### Odoo Backend Integration

#### API Design Patterns
- **RESTful APIs**: Standard REST patterns for all endpoints
- **GraphQL Support**: Flexible data querying capabilities
- **Webhook Integration**: Real-time event notifications
- **Batch Operations**: Efficient bulk data operations
- **Error Handling**: Comprehensive error response patterns

#### Data Synchronization
- **Real-time Sync**: Immediate data synchronization
- **Conflict Resolution**: Automatic conflict resolution strategies
- **Offline Support**: Offline capability with sync on reconnection
- **Data Validation**: Server-side validation for all operations
- **Transaction Management**: Atomic operations for data integrity

### Third-Party Integrations

#### Nafath Authentication
- **Seamless Integration**: Smooth authentication flow
- **Error Handling**: Graceful handling of authentication failures
- **Session Management**: Secure session handling post-authentication
- **Fallback Options**: Alternative authentication methods
- **Compliance**: Full compliance with Nafath requirements

#### External Services
- **Payment Gateways**: Integration with payment providers
- **Shipping Services**: Real-time shipping and tracking
- **Analytics Platforms**: Business intelligence integration
- **Communication Services**: Email, SMS, and notification services
- **Document Management**: PDF generation and document storage

---

## Deployment & DevOps Architecture

### Infrastructure Strategy

#### Containerization
- **Docker Containers**: Consistent deployment environments
- **Kubernetes Orchestration**: Scalable container management
- **Microservices Ready**: Architecture supports microservices transition
- **Health Monitoring**: Container health checks and monitoring
- **Auto-scaling**: Automatic scaling based on demand

#### CI/CD Pipeline
- **Automated Testing**: Comprehensive test suite execution
- **Code Quality**: Static analysis and quality gates
- **Security Scanning**: Automated security vulnerability scanning
- **Deployment Automation**: Zero-downtime deployment strategies
- **Rollback Capabilities**: Quick rollback for failed deployments

### Monitoring & Observability

#### Application Monitoring
- **Performance Metrics**: Real-time performance monitoring
- **Error Tracking**: Comprehensive error logging and alerting
- **User Analytics**: User behavior and engagement tracking
- **Business Metrics**: Key business indicator monitoring
- **Custom Dashboards**: Tailored monitoring dashboards

#### Infrastructure Monitoring
- **Server Metrics**: CPU, memory, disk, and network monitoring
- **Database Performance**: Query performance and optimization
- **Network Monitoring**: Latency and connectivity monitoring
- **Security Monitoring**: Intrusion detection and prevention
- **Capacity Planning**: Resource usage trends and planning

---

## Conclusion

This comprehensive B2B customer portal architecture provides:

### Technical Excellence
- **Scalable Architecture**: Supports growth and increasing complexity
- **Performance Optimized**: Fast, responsive user experience
- **Security First**: Comprehensive security and compliance measures
- **Modern Technology**: Latest frameworks and best practices
- **Maintainable Code**: Clean, well-documented, and testable code

### Business Value
- **User-Centric Design**: Intuitive interface for all user types
- **Operational Efficiency**: Streamlined procurement processes
- **Cost Optimization**: Negotiation tools and spending analytics
- **Compliance Ready**: Meets regulatory and industry requirements
- **Future-Proof**: Extensible architecture for future enhancements

### Implementation Success Factors
- **Phased Approach**: Incremental delivery and validation
- **User Feedback**: Continuous user input and iteration
- **Performance Monitoring**: Ongoing optimization and improvement
- **Security Updates**: Regular security patches and updates
- **Training & Support**: Comprehensive user training and support

This architecture ensures the portal delivers exceptional value to B2B customers like KAUST while providing Zenith Arabia with a powerful platform for customer engagement and business growth.
```
