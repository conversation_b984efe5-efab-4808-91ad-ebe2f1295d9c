# F010 - Dynamic Customer Theme System with Glassmorphism

## Overview

A comprehensive dynamic theme system that supports customer-specific branding with both normal and glassmorphism styles. The system allows for easy customization per customer while maintaining light/dark mode support.

## Features

### 🎨 **Customer Branding**
- **Dynamic Theme Loading**: Themes loaded based on environment configuration
- **KAUST Theme**: Official KAUST university branding with authentic colors
- **Scalable Architecture**: Easy to add new customer themes
- **Brand Consistency**: Logo, colors, typography, and layout customization

### 🌓 **Light/Dark Mode**
- **Seamless Toggle**: Smooth transitions between light and dark modes
- **System Preference**: Automatic detection of user's system preference
- **Persistent Settings**: Theme preferences saved in localStorage
- **Backward Compatibility**: Maintains existing theme toggle functionality

### ✨ **Glassmorphism Effects**
- **Modern UI**: Semi-transparent, frosted glass-like panels
- **Depth & Layering**: Background blur and saturation effects
- **Configurable**: Enable/disable per customer and theme mode
- **Performance Optimized**: CSS-based effects with hardware acceleration

### 🔧 **Developer Experience**
- **TypeScript Support**: Fully typed theme system
- **CSS Variables**: Dynamic CSS custom properties
- **Tailwind Integration**: Extended Tailwind config with theme colors
- **Component Library**: Pre-built glassmorphism components

## Architecture

### Theme Configuration Structure

```typescript
interface CustomerTheme {
  customerId: string;
  themeName: string;
  logoUrl: string;
  faviconUrl: string;
  colors: {
    light: ColorPalette;
    dark: ColorPalette;
  };
  typography: TypographyConfig;
  layout: LayoutConfig;
  glassmorphism: {
    light: GlassmorphismConfig;
    dark: GlassmorphismConfig;
  };
  customCSS?: string;
}
```

### Environment Configuration

```bash
# .env.local
NEXT_PUBLIC_CUSTOMER_ID="kaust"
NEXT_PUBLIC_THEME_STYLE="glassmorphism"
NEXT_PUBLIC_ENABLE_GLASSMORPHISM="true"
```

## Implementation

### 1. Theme Registry

Central theme management system:

```typescript
// src/lib/theme/themeRegistry.ts
export const themeRegistry = new ThemeRegistry();

// Register themes
themeRegistry.registerTheme(kaustTheme);
themeRegistry.registerTheme(defaultTheme);

// Get active theme
const activeTheme = getActiveTheme();
```

### 2. Enhanced Theme Context

```typescript
// src/contexts/ThemeContext.tsx
const { 
  mode,           // 'light' | 'dark'
  style,          // 'normal' | 'glassmorphism'
  customerTheme,  // Current customer theme
  toggleMode,     // Toggle light/dark
  setStyle,       // Set theme style
  isGlassmorphism // Boolean helper
} = useTheme();
```

### 3. Glassmorphism Components

Pre-built components with glassmorphism effects:

```typescript
import { GlassCard, GlassButton, GlassModal } from '@/components/ui/GlassCard';

// Automatically adapts based on theme settings
<GlassCard variant="elevated">
  <GlassButton variant="primary">
    Click me
  </GlassButton>
</GlassCard>
```

## KAUST Theme Specification

### Brand Colors

Based on official KAUST website analysis:

```typescript
const kaustColors = {
  light: {
    primary: '#0066CC',      // KAUST Blue
    secondary: '#004499',    // Dark Blue
    accent: '#FFB800',       // KAUST Gold
    // ... complete color palette
  },
  dark: {
    primary: '#3B82F6',      // Adjusted for dark mode
    secondary: '#1E40AF',    // Dark blue adjusted
    accent: '#FCD34D',       // Gold adjusted
    // ... complete dark palette
  }
};
```

### Typography

```typescript
const kaustTypography = {
  fontPrimary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
  fontSecondary: "'Roboto Mono', 'SF Mono', Monaco, monospace",
  fontArabic: "'Noto Sans Arabic', 'Tajawal', 'Cairo', sans-serif",
};
```

### Glassmorphism Configuration

```typescript
const kaustGlass = {
  light: {
    enabled: true,
    blur: '16px',
    opacity: '0.8',
    borderOpacity: '0.2',
    shadowIntensity: '0.1',
    backdropSaturation: '1.8',
  },
  dark: {
    enabled: true,
    blur: '20px',
    opacity: '0.7',
    borderOpacity: '0.15',
    shadowIntensity: '0.2',
    backdropSaturation: '1.5',
  }
};
```

## Usage Examples

### Basic Theme Usage

```typescript
// Get theme colors
const colors = useThemeColors();
const primaryColor = colors.primary;

// Check glassmorphism status
const { enabled, config } = useGlassmorphism();

// Get typography settings
const typography = useThemeTypography();
```

### Component Styling

```typescript
// Using theme classes
<div className="bg-primary text-white">
  Primary colored background
</div>

// Using CSS variables
<div style={{ backgroundColor: 'var(--color-primary)' }}>
  Dynamic primary color
</div>

// Glassmorphism component
<GlassCard className="p-6">
  <h2 className="text-primary">Glass Card</h2>
  <p className="text-foreground-muted">
    Automatically applies glassmorphism if enabled
  </p>
</GlassCard>
```

### Theme Toggle Components

```typescript
// Simple toggle button
<ThemeToggle />

// Switch variant
<ThemeToggle variant="switch" showLabel />

// Dropdown with style options
<ThemeToggle variant="dropdown" showLabel />
```

## CSS Variables

The system generates dynamic CSS variables:

```css
:root {
  /* Colors */
  --color-primary: #0066CC;
  --color-primary-hover: #0052A3;
  --color-accent: #FFB800;
  
  /* Typography */
  --font-primary: 'Inter', sans-serif;
  --font-arabic: 'Noto Sans Arabic', sans-serif;
  
  /* Glassmorphism */
  --glass-blur: 16px;
  --glass-opacity: 0.8;
  --glass-border-opacity: 0.2;
}
```

## Tailwind Integration

Extended Tailwind configuration:

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--color-primary)',
          hover: 'var(--color-primary-hover)',
        },
        theme: {
          background: 'var(--color-background)',
          foreground: 'var(--color-foreground)',
        }
      }
    }
  }
};
```

## Performance Considerations

### CSS Optimization
- **Hardware Acceleration**: Uses `backdrop-filter` for optimal performance
- **Minimal Repaints**: CSS variables prevent style recalculation
- **Smooth Transitions**: 300ms transitions for theme changes

### Bundle Size
- **Tree Shaking**: Only used theme components are included
- **CSS Variables**: Reduces CSS bundle size
- **Lazy Loading**: Theme assets loaded on demand

## Browser Support

### Glassmorphism Support
- **Modern Browsers**: Full support in Chrome 76+, Firefox 103+, Safari 14+
- **Fallback**: Graceful degradation to normal styles
- **Progressive Enhancement**: Enhanced experience for supported browsers

## Accessibility

### WCAG Compliance
- **Color Contrast**: Automatic contrast ratio validation
- **Focus Indicators**: Visible focus states for all interactive elements
- **Screen Readers**: Proper ARIA labels and semantic markup
- **Reduced Motion**: Respects `prefers-reduced-motion` setting

## Testing

### Theme Testing
```typescript
// Test theme switching
const { toggleMode, setStyle } = useTheme();

// Test glassmorphism
const { enabled } = useGlassmorphism();
expect(enabled).toBe(true);

// Test color accessibility
expect(isAccessible('#0066CC', '#FFFFFF')).toBe(true);
```

## Migration Guide

### From Old Theme System

1. **Update Imports**:
   ```typescript
   // Old
   import { useTheme } from '../contexts/ThemeContext';
   const { theme, toggleTheme } = useTheme();
   
   // New (backward compatible)
   import { useTheme } from '../contexts/ThemeContext';
   const { mode, toggleMode, theme, toggleTheme } = useTheme();
   ```

2. **Update CSS Classes**:
   ```css
   /* Old */
   .bg-blue-600
   
   /* New */
   .bg-primary
   ```

3. **Environment Configuration**:
   ```bash
   # Add to .env.local
   NEXT_PUBLIC_CUSTOMER_ID="kaust"
   NEXT_PUBLIC_THEME_STYLE="glassmorphism"
   ```

## Future Enhancements

### Planned Features
- **Theme Builder**: Visual theme customization interface
- **Animation Presets**: Pre-defined animation sets per theme
- **Component Variants**: More glassmorphism component variations
- **Theme Analytics**: Usage tracking and optimization insights

### Customer Expansion
- **Multi-tenant Support**: Runtime theme switching
- **Brand Guidelines**: Automated brand compliance checking
- **Asset Management**: CDN-based theme asset delivery
- **A/B Testing**: Theme variant testing capabilities

## Conclusion

The dynamic theme system provides a robust, scalable solution for customer branding with modern glassmorphism effects. It maintains backward compatibility while offering advanced customization capabilities for enterprise B2B applications.

The KAUST theme demonstrates the system's capabilities with authentic university branding, and the architecture supports easy addition of new customer themes as the platform scales.
