{"name": "KUST_portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.6.1", "autoprefixer": "^10.4.21", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.363.0", "negotiator": "^1.0.0", "next": "^15.2.4", "next-i18next": "^15.4.2", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-redux": "^9.2.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.1", "uuid": "^9.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^3.3.0", "typescript": "^5"}}