/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Legacy colors for backward compatibility
        background: 'var(--background)',
        foreground: 'var(--foreground)',

        // New theme system colors
        primary: {
          DEFAULT: 'var(--color-primary)',
          hover: 'var(--color-primary-hover)',
          active: 'var(--color-primary-active)',
        },
        secondary: {
          DEFAULT: 'var(--color-secondary)',
          hover: 'var(--color-secondary-hover)',
        },
        accent: {
          DEFAULT: 'var(--color-accent)',
          hover: 'var(--color-accent-hover)',
        },
        theme: {
          background: 'var(--color-background)',
          'background-secondary': 'var(--color-background-secondary)',
          'background-tertiary': 'var(--color-background-tertiary)',
          foreground: 'var(--color-foreground)',
          'foreground-secondary': 'var(--color-foreground-secondary)',
          'foreground-muted': 'var(--color-foreground-muted)',
          border: 'var(--color-border)',
          'border-hover': 'var(--color-border-hover)',
          success: 'var(--color-success)',
          warning: 'var(--color-warning)',
          error: 'var(--color-error)',
          info: 'var(--color-info)',
        },
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)', 'var(--font-primary)'],
        mono: ['var(--font-geist-mono)', 'var(--font-secondary)'],
        primary: ['var(--font-primary)'],
        secondary: ['var(--font-secondary)'],
        arabic: ['var(--font-arabic)'],
      },
      borderRadius: {
        'theme-sm': 'var(--radius-sm)',
        'theme-md': 'var(--radius-md)',
        'theme-lg': 'var(--radius-lg)',
        'theme-xl': 'var(--radius-xl)',
      },
      spacing: {
        'theme-xs': 'var(--spacing-xs)',
        'theme-sm': 'var(--spacing-sm)',
        'theme-md': 'var(--spacing-md)',
        'theme-lg': 'var(--spacing-lg)',
        'theme-xl': 'var(--spacing-xl)',
      },
      boxShadow: {
        'theme-sm': 'var(--shadow-sm)',
        'theme-md': 'var(--shadow-md)',
        'theme-lg': 'var(--shadow-lg)',
        'theme-xl': 'var(--shadow-xl)',
      },
      backdropBlur: {
        'glass': 'var(--glass-blur)',
      },
      screens: {
        'xs': '480px',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.rtl': {
          direction: 'rtl',
          textAlign: 'right',
        },
        '.ltr': {
          direction: 'ltr',
          textAlign: 'left',
        },
      }
      addUtilities(newUtilities)
    },
  ],
}