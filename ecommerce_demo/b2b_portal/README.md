# KUST Marketplace Customer Portal

A clean, modular Next.js 15 project for a KUST Marketplace Customer Portal with multi-basket state management, Odoo ERP integration, and reusable UI components.

## Features

- **Multi-basket State Management**: Create and manage multiple baskets with Redux
- **Odoo ERP Integration**: Mock API ready to connect to Odoo 18 via HTTP controllers
- **Dynamic Product Data**: Mock data for electronics products and services
- **Zero Backend Dependency**: UI-ready for any API
- **Reusable Components**: Built with ShadCN UI + Tailwind CSS

## Project Structure

```bash
├── public/                        # Static assets (product images, icons)
├── src/
│   ├── app/                       # Next.js 15 app router
│   │   ├── (portal)/              # Customer portal routes
│   │   │   ├── layout.tsx         # Shared portal layout
│   │   │   ├── page.tsx           # Main marketplace grid view
│   │   │   ├── search/            # Smart search autocomplete
│   │   │   ├── baskets/           # Multi-basket management
│   │   │   └── orders/            # RFQ/order confirmation
│   ├── components/                # Reusable UI
│   │   ├── ProductCard.tsx        # Grid item (image, price, variants)
│   │   ├── BasketSelector.tsx     # Switch between baskets (Redux state)
│   │   ├── FiltersSidebar.tsx     # Category/price/variant filters
│   │   └── SearchAutocomplete.tsx # Typeahead search (debounced)
│   ├── lib/
│   │   ├── api/                   # Odoo API mock (fetch/axios)
│   │   │   ├── odoo.ts            # HTTP controller helpers (POST to Odoo 18)
│   │   │   └── mockData.ts        # Dynamic JSON generator (electronics)
│   │   ├── redux/                 # State management
│   │   │   ├── slices/
│   │   │   │   ├── baskets.ts     # Multi-basket state (confirmed/draft)
│   │   │   │   └── products.ts    # Catalog filters/search state
│   │   │   └── store.ts           # Redux store
│   ├── styles/                    # Tailwind + ShadCN overrides
│   └── types/                     # TS interfaces (Product, Basket, etc.)
```

## Getting Started

1. Install dependencies:

```bash
npm install
# or
yarn install
# or
bun install
```

1. Run the development server:

```bash
npm run dev
# or
yarn dev
# or
bun dev
```

1. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Connecting to Odoo

To connect this portal to a real Odoo instance, update the `NEXT_PUBLIC_ODOO_API_URL` environment variable and modify the API functions in `src/lib/api/odoo.ts`.

Example Odoo HTTP controller:

```python
from odoo import http
from odoo.http import request
import json

class KUSTPortalController(http.Controller):
    @http.route('/api/confirm_rfq', type='json', auth='public', methods=['POST'], csrf=False)
    def confirm_rfq(self, **post):
        data = json.loads(request.httprequest.data)
        basket_id = data.get('basket_id')
        items = data.get('items', [])

        # Create sale order
        sale_order = request.env['sale.order'].sudo().create({
            'partner_id': request.env.user.partner_id.id,
            'client_order_ref': basket_id,
        })

        # Add order lines
        for item in items:
            request.env['sale.order.line'].sudo().create({
                'order_id': sale_order.id,
                'product_id': item['productId'],
                'product_uom_qty': item['quantity'],
            })

        return {
            'success': True,
            'data': {
                'rfq_id': sale_order.name,
                'message': 'Request for Quotation created successfully',
            }
        }
```

## Customizing the UI

The UI is built with Tailwind CSS and can be customized by modifying the `tailwind.config.js` file. ShadCN UI components can be added as needed.

## License

This project is licensed under the MIT License.
