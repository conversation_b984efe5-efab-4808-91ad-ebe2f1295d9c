# Customer-Specific Branding System Guide

## Overview

The B2B Marketplace implements a comprehensive customer-specific branding system that allows different organizations to have their own branded color schemes and visual identity. This white-label solution enables easy deployment for multiple customers while maintaining a consistent user experience.

## Architecture

### 1. Environment-Based Configuration

The branding system is controlled through environment variables in `.env.local`:

```bash
# Customer Branding Configuration
NEXT_PUBLIC_CUSTOMER_BRAND="kaust"  # Options: "kaust", "default", or future brands
```

### 2. Brand Configuration System

**Location:** `/src/config/brandConfig.ts`

This file contains:
- **BrandColors Interface**: Defines the complete color palette structure
- **BrandConfig Interface**: Defines brand configuration including name, colors, and optional logo
- **Brand Registry**: Object containing all available brand configurations
- **getBrandConfig()**: Function that reads environment variable and returns appropriate brand
- **getCSSVariables()**: Helper function that converts brand colors to CSS custom properties

### 3. Theme Context Integration

**Location:** `/src/contexts/ThemeContext.tsx`

The ThemeProvider:
- Loads brand configuration based on environment variable
- Applies brand colors as CSS custom properties to document root
- Maintains light/dark mode functionality independent of brand colors
- Provides brand configuration to all components via React Context

## Brand Configuration Structure

### Color Palette

Each brand defines a comprehensive color palette:

```typescript
interface BrandColors {
  primary: {
    50: string;   // Very light
    100: string;  // Light
    // ... through to
    900: string;  // Very dark
    950: string;  // Darkest
  };
  secondary: { /* Same structure */ };
  success: { 50, 500, 600, 700 };
  warning: { 50, 500, 600, 700 };
  error: { 50, 500, 600, 700 };
  neutral: { /* Full 50-950 range */ };
}
```

### KAUST Brand Colors

The KAUST brand configuration is based on the official KAUST logo colors:
- **Primary**: KAUST Yellow (#FFD600) with full shade range - main brand color from logo
- **Secondary**: KAUST Green (#B2DF28) with full shade range - vibrant green from logo
- **Tertiary**: KAUST Teal (#26C6DA) with full shade range - teal accent from logo
- **Semantic Colors**: Success (green), Warning (orange #FFA040 from logo), Error (red)
- **Neutral Colors**: Comprehensive gray scale for text and backgrounds

The color palette reflects the harmonious and energetic nature of the KAUST logo with its four overlapping organic oval shapes in yellow, orange, green, and teal.

### Default Brand Colors

The default brand provides:
- **Primary**: Standard blue (#3b82f6) with full shade range
- **Secondary**: Neutral gray with full shade range
- **Semantic Colors**: Standard success, warning, error colors
- **Neutral Colors**: Standard gray scale

## Implementation Details

### 1. CSS Custom Properties

Brand colors are applied as CSS custom properties:

```css
:root {
  --color-primary-500: #1e40af;  /* KAUST Blue */
  --color-secondary-500: #0f766e; /* KAUST Teal */
  /* ... all other color shades */
}
```

### 2. Tailwind Configuration

**Location:** `/tailwind.config.js`

Tailwind is configured to use the CSS custom properties:

```javascript
colors: {
  primary: {
    50: 'var(--color-primary-50)',
    500: 'var(--color-primary-500)',
    DEFAULT: 'var(--color-primary-500)',
    // ... all shades
  },
  // ... other color categories
}
```

### 3. Component Usage

Components can use brand colors through Tailwind classes:

```jsx
// Primary brand colors
<button className="bg-primary-500 hover:bg-primary-600 text-white">
  Primary Button
</button>

// Secondary brand colors
<div className="text-secondary-500 border-secondary-200">
  Secondary styled content
</div>

// Semantic colors
<div className="bg-success-50 text-success-700 border-success-200">
  Success message
</div>
```

## Adding New Customer Brands

### Step 1: Define Brand Configuration

Add new brand to `/src/config/brandConfig.ts`:

```typescript
const newCustomerBrand: BrandConfig = {
  name: 'newcustomer',
  displayName: 'New Customer',
  colors: {
    primary: {
      50: '#f0f9ff',
      // ... define all shades
      500: '#0ea5e9',  // Main brand color
      // ... continue through 950
    },
    // ... define secondary, success, warning, error, neutral
  },
};

// Add to brand registry
export const brandConfigs: Record<string, BrandConfig> = {
  kaust: kaustBrand,
  default: defaultBrand,
  newcustomer: newCustomerBrand,  // Add here
};
```

### Step 2: Environment Configuration

Update `.env.local` for the new customer:

```bash
NEXT_PUBLIC_CUSTOMER_BRAND="newcustomer"
```

### Step 3: Test and Validate

1. Restart the development server
2. Visit `/brand-demo` to see the new brand colors
3. Test all components and pages
4. Verify light/dark mode compatibility

## Features

### ✅ **Implemented Features**

1. **Environment-Based Brand Switching**: Controlled via `NEXT_PUBLIC_CUSTOMER_BRAND`
2. **Comprehensive Color System**: Full shade ranges for all color categories
3. **KAUST Brand Configuration**: Official KAUST colors and branding
4. **Default Fallback Brand**: Generic branding for non-specific deployments
5. **Light/Dark Mode Compatibility**: Brand colors work in both themes
6. **Tailwind Integration**: Full integration with Tailwind CSS classes
7. **CSS Custom Properties**: Dynamic color application via CSS variables
8. **Multi-Tenant Architecture**: Easy addition of new customer brands
9. **Homepage Integration**: Brand-aware homepage with dynamic content
10. **Brand Demo Page**: Visual demonstration of brand color system

### 🎯 **Key Benefits**

1. **White-Label Ready**: Easy deployment for different customers
2. **Consistent UX**: Same interface with customer-specific branding
3. **Maintainable**: Centralized brand configuration
4. **Scalable**: Easy addition of new customer brands
5. **Performance**: No runtime brand switching overhead
6. **SEO Friendly**: Brand determined at build time
7. **Developer Friendly**: Clear APIs and documentation

## Usage Examples

### Accessing Brand Configuration in Components

```typescript
import { useTheme } from '../contexts/ThemeContext';

function MyComponent() {
  const { brandConfig } = useTheme();
  
  return (
    <div>
      <h1>{brandConfig.displayName} Marketplace</h1>
      <p>Welcome to {brandConfig.name} portal</p>
    </div>
  );
}
```

### Using Brand Colors in Styles

```jsx
// Tailwind classes
<div className="bg-primary-500 text-white">Primary background</div>
<div className="text-primary-600 border-primary-200">Primary text</div>

// CSS custom properties
<div style={{ backgroundColor: 'var(--color-primary-500)' }}>
  Custom styling
</div>
```

## Testing

### Brand Demo Page

Visit `/brand-demo` to see:
- Current brand configuration
- All color palettes with hex values
- Component examples using brand colors
- Instructions for switching brands

### Environment Testing

1. Set `NEXT_PUBLIC_CUSTOMER_BRAND="kaust"` - See KAUST branding
2. Set `NEXT_PUBLIC_CUSTOMER_BRAND="default"` - See default branding
3. Set invalid value - Falls back to KAUST branding with console warning

## Deployment Considerations

### Production Deployment

1. Set appropriate `NEXT_PUBLIC_CUSTOMER_BRAND` in production environment
2. Build application with customer-specific branding
3. Brand colors are applied at build time for optimal performance

### Multi-Customer Deployments

For serving multiple customers:
1. Create separate deployments with different environment variables
2. Use subdomain or path-based routing to different branded instances
3. Consider using a deployment pipeline that builds for each customer

## Future Enhancements

### Potential Additions

1. **Logo Configuration**: Add customer logos to brand configuration
2. **Font Configuration**: Customer-specific font families
3. **Layout Variations**: Different layout styles per customer
4. **Advanced Theming**: More granular theme customization options
5. **Runtime Brand Switching**: Dynamic brand switching for demo purposes

This branding system provides a solid foundation for a white-label B2B marketplace that can be easily customized for different customers while maintaining code consistency and performance.
