# Task: Order Management Components

## Objective
Implement the order management components for creating, editing, tracking, and managing orders throughout their lifecycle.

## Requirements

1. Create order/basket selection and management components
2. Implement order state machine with transitions
3. Create order detail view with line items
4. Implement order history and tracking
5. Create order communication/chatter functionality

## Implementation Steps

### 1. Order Selection Components

- Create Order Selector (`src/components/OrderSelector.tsx`):
  ```typescript
  // Dropdown to select active order
  // Create new order button
  // Quick view of order status
  ```

- Create Basket Selector (`src/components/BasketSelector.tsx`):
  ```typescript
  // Display current basket items
  // Show total price
  // Quick actions (view, checkout)
  ```

### 2. Order State Machine

- Implement Order State Machine (`src/lib/redux/slices/ordersSlice.ts`):
  ```typescript
  // Define order states: draft, submitted, under_review, revised, approved, rejected, expired, ordered
  // Implement state transitions
  // Validation for state changes
  ```

- Create Order State Visualization (`src/components/OrderStateVisualizer.tsx`):
  ```typescript
  // Visual representation of order state
  // Show current state and possible transitions
  // Progress indicator
  ```

### 3. Order Detail Components

- Create Order Detail Page (`src/app/(portal)/orders/[id]/page.tsx`):
  ```typescript
  // Fetch order by ID
  // Display order details
  // Show order history
  ```

- Create Order Line Items (`src/components/OrderLineItems.tsx`):
  ```typescript
  // Table of order items
  // Quantity and price
  // Edit/remove functionality
  // Subtotal calculation
  ```

- Create Order Summary (`src/components/OrderSummary.tsx`):
  ```typescript
  // Order totals
  // Taxes and discounts
  // Payment terms
  // Delivery information
  ```

### 4. Order Actions

- Create Order Actions (`src/components/OrderActions.tsx`):
  ```typescript
  // Submit order button
  // Cancel order button
  // Edit order button
  // State transition buttons based on current state
  ```

- Create Order Confirmation Modal (`src/components/OrderConfirmationModal.tsx`):
  ```typescript
  // Confirm order submission
  // Show order summary
  // Terms acceptance
  ```

### 5. Order History and Tracking

- Create Orders List Page (`src/app/(portal)/orders/page.tsx`):
  ```typescript
  // Table of all orders
  // Filtering by state
  // Sorting options
  // Search functionality
  ```

- Create Order History (`src/components/OrderHistory.tsx`):
  ```typescript
  // Timeline of order state changes
  // User actions history
  // Date and time stamps
  ```

### 6. Order Communication

- Create Order Chatter (`src/components/OrderChatter.tsx`):
  ```typescript
  // Message thread for order
  // File attachments
  // User mentions
  // Timestamp for messages
  ```

- Create Message Notification (`src/components/MessageNotification.tsx`):
  ```typescript
  // Notification for new messages
  // Unread count
  // Quick access to messages
  ```

## Deliverables

1. Order selection and basket components
2. Order state machine implementation
3. Order detail view with line items
4. Order history and tracking functionality
5. Order communication/chatter component

## Dependencies

- Task 001: Project Setup
- Task 002: Core Architecture
- Task 003: Base UI Components
- Task 005: Internationalization
- Task 006: Mock API Integration

## Estimated Time
12 hours