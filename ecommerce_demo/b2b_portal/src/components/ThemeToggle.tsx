'use client';

import { useTheme } from '../contexts/ThemeContext';
import { cn } from '../lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'switch';
  showLabel?: boolean;
  className?: string;
}

export default function ThemeToggle({
  variant = 'button',
  showLabel = false,
  className
}: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();

  if (variant === 'switch') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        {showLabel && (
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {theme === 'light' ? 'Light' : 'Dark'}
          </span>
        )}
        <button
          onClick={toggleTheme}
          className={cn(
            'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            {
              'bg-blue-600': theme === 'dark',
              'bg-gray-300': theme === 'light',
            }
          )}
          aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
        >
          <span
            className={cn(
              'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
              {
                'translate-x-6': theme === 'dark',
                'translate-x-1': theme === 'light',
              }
            )}
          />
        </button>
      </div>
    );
  }



  // Default button variant
  return (
    <button
      onClick={toggleTheme}
      className={cn(
        'p-2 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500',
        'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800',
        className
      )}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
    >
      {theme === 'light' ? (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
        </svg>
      ) : (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
        </svg>
      )}
      {showLabel && (
        <span className="ml-2 text-sm">
          {theme === 'light' ? 'Light' : 'Dark'}
        </span>
      )}
    </button>
  );
}
