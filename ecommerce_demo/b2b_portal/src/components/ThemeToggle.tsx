'use client';

import { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { cn } from '../lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'switch' | 'dropdown';
  showLabel?: boolean;
  className?: string;
}

export default function ThemeToggle({
  variant = 'button',
  showLabel = false,
  className
}: ThemeToggleProps) {
  const { mode, style, toggleMode, setStyle, isGlassmorphism } = useTheme();
  const [showStyleMenu, setShowStyleMenu] = useState(false);

  if (variant === 'switch') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        {showLabel && (
          <span className="text-sm text-theme-foreground-muted">
            {mode === 'light' ? 'Light' : 'Dark'}
          </span>
        )}
        <button
          onClick={toggleMode}
          className={cn(
            'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
            {
              'bg-primary': mode === 'dark',
              'bg-gray-300': mode === 'light',
              'glass': isGlassmorphism,
            }
          )}
          aria-label={`Switch to ${mode === 'light' ? 'dark' : 'light'} theme`}
        >
          <span
            className={cn(
              'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
              {
                'translate-x-6': mode === 'dark',
                'translate-x-1': mode === 'light',
              }
            )}
          />
        </button>
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={cn('relative', className)}>
        <button
          onClick={() => setShowStyleMenu(!showStyleMenu)}
          className={cn(
            'flex items-center space-x-2 p-2 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary',
            'text-theme-foreground-secondary hover:text-theme-foreground',
            {
              'glass': isGlassmorphism,
              'bg-theme-background-secondary hover:bg-theme-background-tertiary': !isGlassmorphism,
            }
          )}
          aria-label="Theme options"
        >
          {mode === 'light' ? (
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          )}
          {showLabel && (
            <span className="text-sm">
              {mode === 'light' ? 'Light' : 'Dark'} • {style === 'glassmorphism' ? 'Glass' : 'Normal'}
            </span>
          )}
          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>

        {showStyleMenu && (
          <div className={cn(
            'absolute right-0 mt-2 w-48 rounded-md shadow-lg z-50',
            {
              'glass border border-white/20': isGlassmorphism,
              'bg-white border border-gray-200': !isGlassmorphism,
            }
          )}>
            <div className="py-1">
              <button
                onClick={() => {
                  toggleMode();
                  setShowStyleMenu(false);
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-theme-foreground hover:bg-theme-background-secondary"
              >
                {mode === 'light' ? (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                    Switch to Dark
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                    </svg>
                    Switch to Light
                  </>
                )}
              </button>
              <hr className="border-theme-border" />
              <button
                onClick={() => {
                  setStyle(style === 'glassmorphism' ? 'normal' : 'glassmorphism');
                  setShowStyleMenu(false);
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-theme-foreground hover:bg-theme-background-secondary"
              >
                {style === 'glassmorphism' ? (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                    </svg>
                    Normal Style
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" clipRule="evenodd" />
                    </svg>
                    Glassmorphism
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default button variant
  return (
    <button
      onClick={toggleMode}
      className={cn(
        'p-2 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary',
        'text-theme-foreground-secondary hover:text-theme-foreground',
        {
          'glass': isGlassmorphism,
          'hover:bg-theme-background-secondary': !isGlassmorphism,
        },
        className
      )}
      aria-label={`Switch to ${mode === 'light' ? 'dark' : 'light'} theme`}
    >
      {mode === 'light' ? (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
        </svg>
      ) : (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
        </svg>
      )}
      {showLabel && (
        <span className="ml-2 text-sm">
          {mode === 'light' ? 'Light' : 'Dark'}
        </span>
      )}
    </button>
  );
}
