'use client';

import { useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../lib/redux/store';
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

export default function WishlistNotification() {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const wishlistItems = useSelector((state: RootState) => state.wishlist.items);

  return (
    <div className="relative">
      <button
        className="p-1.5 rounded-full text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors duration-200"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Wishlist"
      >
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
        {wishlistItems.length > 0 && (
          <span className="absolute -top-2 -right-2 bg-blue-600 dark:bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center transition-colors duration-200">
            {wishlistItems.length}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 transition-colors duration-200">
          <div className="p-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center transition-colors duration-200">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">{t('wishlist.yourWishlist')}</h3>
            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 transition-colors duration-200">{wishlistItems.length} {t('wishlist.items')}</span>
          </div>

          <div className="max-h-60 overflow-y-auto">
            {wishlistItems.length > 0 ? (
              <div className="py-1">
                {wishlistItems.map(item => (
                  <Link
                    key={item.id}
                    href={`/products/${item.id}`}
                    className="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 flex-shrink-0 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden">
                        {item.images && item.images.length > 0 && (
                          <img
                            src={item.images[0]}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                      <div className="ml-3 flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate transition-colors duration-200">
                          {item.name}
                        </p>
                        <p className="text-xs text-blue-600 dark:text-blue-400 transition-colors duration-200">
                          ${item.price.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="p-3 text-center text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
                {t('wishlist.noItems')}
              </div>
            )}
          </div>

          <div className="p-2 border-t border-gray-200 dark:border-gray-700 transition-colors duration-200">
            <Link
              href="/wishlist"
              className="block w-full text-center px-4 py-2 bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white text-sm font-medium rounded transition-colors duration-200"
              onClick={() => setIsOpen(false)}
            >
              {t('wishlist.viewAll')}
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
