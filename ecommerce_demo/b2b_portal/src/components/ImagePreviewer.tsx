'use client';

import { useState, useRef, useEffect, KeyboardEvent, MouseEvent } from 'react';
import Image from 'next/image';

interface ImagePreviewerProps {
  images: string[];
  alt: string;
  mainImageWidth?: number;
  mainImageHeight?: number;
  thumbnailWidth?: number;
  thumbnailHeight?: number;
}

export default function ImagePreviewer({
  images,
  alt,
  mainImageWidth = 800,
  mainImageHeight = 800,
  thumbnailWidth = 70,
  thumbnailHeight = 70,
}: ImagePreviewerProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [zoomVisible, setZoomVisible] = useState(false);

  // State to track if component is mounted (client-side only)
  const [isMounted, setIsMounted] = useState(false);

  // Only render on client side to avoid hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const mainImageRef = useRef<HTMLDivElement>(null);
  const zoomLensRef = useRef<HTMLDivElement>(null);

  // Handle image load complete
  const handleImageLoad = () => {
    setIsLoading(false);
  };

  // Select a thumbnail
  const selectThumbnail = (index: number) => {
    setSelectedImageIndex(index);
  };

  // Handle keyboard navigation for thumbnails
  const handleThumbnailKeyDown = (e: KeyboardEvent<HTMLButtonElement>, index: number) => {
    if (e.key === 'Enter' || e.key === ' ') {
      selectThumbnail(index);
    }
  };

  // Handle mouse enter/leave for zoom
  const handleMouseEnter = () => {
    setIsHovering(true);
    setZoomVisible(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
    setZoomVisible(false);
  };

  // Calculate zoom position based on cursor position
  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!mainImageRef.current || !isHovering) return;

    const { left, top, width, height } = mainImageRef.current.getBoundingClientRect();

    // Calculate position as percentage
    const x = Math.max(0, Math.min(1, (e.clientX - left) / width));
    const y = Math.max(0, Math.min(1, (e.clientY - top) / height));

    setHoverPosition({ x, y });

    // Update lens position if it exists
    if (zoomLensRef.current) {
      const lensWidth = zoomLensRef.current.offsetWidth;
      const lensHeight = zoomLensRef.current.offsetHeight;

      const lensLeft = e.clientX - left - lensWidth / 2;
      const lensTop = e.clientY - top - lensHeight / 2;

      zoomLensRef.current.style.left = `${Math.max(0, Math.min(width - lensWidth, lensLeft))}px`;
      zoomLensRef.current.style.top = `${Math.max(0, Math.min(height - lensHeight, lensTop))}px`;
    }
  };

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);

  // Calculate the zoom lens size based on zoom level
  const zoomLevel = 2.5;
  const lensSize = {
    width: Math.round(mainImageWidth / zoomLevel),
    height: Math.round(mainImageHeight / zoomLevel)
  };

  // Calculate thumbnail size based on main image size
  const adjustedThumbnailWidth = isMobile ? thumbnailWidth * 0.8 : thumbnailWidth;
  const adjustedThumbnailHeight = isMobile ? thumbnailHeight * 0.8 : thumbnailHeight;

  // Check if device is mobile on mount and when window resizes
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // If not mounted yet, return a placeholder to avoid hydration issues
  if (!isMounted) {
    return (
      <div className="w-full">
        <div className="aspect-w-1 aspect-h-1 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <div className="animate-pulse bg-gray-200 dark:bg-gray-700 w-full h-full"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row gap-1 md:gap-2 w-full">
        {/* Thumbnails - Horizontal on mobile, vertical on desktop */}
        <div
          className="flex flex-row md:flex-col gap-1 overflow-x-auto md:overflow-y-auto order-2 md:order-1 pb-1 md:pb-0 flex-shrink-0"
          role="tablist"
          aria-label="Product images"
          style={{
            maxHeight: `${mainImageHeight}px`,
            minWidth: `${adjustedThumbnailWidth + 16}px`
          }}
        >
          {images.map((image, index) => (
            <button
              key={index}
              type="button"
              role="tab"
              aria-selected={selectedImageIndex === index}
              aria-label={`${alt || 'Product'} - Image ${index + 1}`}
              onClick={() => selectThumbnail(index)}
              onKeyDown={(e) => handleThumbnailKeyDown(e, index)}
              className={`
                relative flex-shrink-0 rounded-md overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-500
                ${selectedImageIndex === index ? 'ring-2 ring-blue-500' : 'ring-1 ring-gray-200'}
                transition-all duration-200 hover:ring-2 hover:ring-blue-300
              `}
              style={{
                width: `${adjustedThumbnailWidth}px`,
                height: `${adjustedThumbnailHeight}px`,
                minWidth: `${adjustedThumbnailWidth}px`
              }}
            >
              <Image
                src={image}
                alt={`${alt || 'Product'} - Thumbnail ${index + 1}`}
                fill
                sizes={`${thumbnailWidth}px`}
                className="object-cover"
              />
            </button>
          ))}
        </div>

        {/* Main image container */}
        <div className="relative flex-grow order-1 md:order-2">
          {/* Loading indicator */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}

          {/* Main image with hover zoom */}
          <div
            ref={mainImageRef}
            className="relative rounded-lg overflow-hidden bg-white cursor-crosshair w-full"
            style={{
              maxWidth: '100%',
              height: 'auto',
              aspectRatio: '1/1',
              maxHeight: `${mainImageHeight}px`
            }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onMouseMove={handleMouseMove}
          >
            <Image
              src={images[selectedImageIndex]}
              alt={`${alt || 'Product'} - Image ${selectedImageIndex + 1}`}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 600px"
              priority
              quality={90}
              className={`
                object-contain transition-opacity duration-300
                ${isLoading ? 'opacity-0' : 'opacity-100'}
              `}
              onLoad={handleImageLoad}
            />

            {/* Zoom lens (only visible when hovering) */}
            {isHovering && (
              <div
                ref={zoomLensRef}
                className="absolute border-2 border-gray-300 bg-white/10 pointer-events-none z-10"
                style={{
                  width: `${lensSize.width}px`,
                  height: `${lensSize.height}px`,
                }}
              />
            )}
          </div>

          {/* Zoom popup (Amazon style) - Only on desktop */}
          {zoomVisible && !isMobile && (
            <div
              className="absolute left-full ml-1 top-0 rounded-lg overflow-hidden border border-gray-200 shadow-lg z-20 hidden xl:block"
              style={{
                width: `${mainImageWidth * 0.9}px`,
                height: `${mainImageHeight * 0.9}px`,
                backgroundImage: `url(${images[selectedImageIndex]})`,
                backgroundPosition: `${hoverPosition.x * 100}% ${hoverPosition.y * 100}%`,
                backgroundSize: `${zoomLevel * 100}%`,
                backgroundRepeat: 'no-repeat'
              }}
            >
              {/* Checkerboard pattern for transparent images */}
              <div
                className="absolute inset-0 opacity-10"
                style={{
                  backgroundImage: 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)',
                  backgroundSize: '20px 20px',
                  backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                }}
              />
            </div>
          )}

          {/* Image caption/info - Only on desktop */}
          <div className="mt-1 text-xs text-gray-500 text-center hidden xl:block">
            Hover over image to zoom
          </div>
        </div>
      </div>
    </div>
  );
}
