'use client';

import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { createOrder } from '../lib/redux/slices/orders';
import { useLanguage } from '../contexts/LanguageContext';
import Link from 'next/link';

export default function BasketSelector() {
  const dispatch = useDispatch();
  const { orders, activeOrderId } = useSelector((state: RootState) => state.orders);
  const { t } = useLanguage();

  // Filter to only show open orders (draft and submitted)
  const openOrders = orders.filter(order => order.state === 'draft' || order.state === 'submitted');

  // Group orders by state
  const draftOrders = openOrders.filter(order => order.state === 'draft');
  const submittedOrders = openOrders.filter(order => order.state === 'submitted');

  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [newOrderName, setNewOrderName] = useState('');

  const handleCreateOrder = () => {
    if (newOrderName.trim()) {
      dispatch(createOrder({ name: newOrderName.trim() }));
      setNewOrderName('');
      setIsCreatingOrder(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 transition-colors duration-200">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white transition-colors duration-200">{t('orders.yourOrders')}</h2>
        <Link
          href="/orders"
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200"
        >
          {t('orders.viewAll')}
        </Link>
      </div>

      {openOrders.length > 0 ? (
        <div className="space-y-4">
          {/* Draft Orders Section */}
          {draftOrders.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 transition-colors duration-200">{t('orders.draftOrders')}</h3>
              <div className="space-y-2 max-h-32 overflow-y-auto pr-1 scrollbar-thin">
                {draftOrders.map(order => (
                  <Link
                    key={order.id}
                    href={`/orders/${order.id}`}
                    className={`
                      block p-3 rounded-md transition-colors duration-200
                      ${activeOrderId === order.id
                        ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800'
                        : 'bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 border border-transparent'}
                    `}
                  >
                    <div className="flex justify-between items-center">
                      <div className="min-w-0 flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white truncate transition-colors duration-200">{order.name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
                          {order.items.length} {order.items.length === 1 ? t('orders.item') : t('orders.items')}
                        </p>
                      </div>
                      <div>
                        <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300 transition-colors duration-200">
                          {t('orders.draft')}
                        </span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Submitted Orders Section */}
          {submittedOrders.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 transition-colors duration-200">{t('orders.submittedOrders')}</h3>
              <div className="space-y-2 max-h-32 overflow-y-auto pr-1 scrollbar-thin">
                {submittedOrders.map(order => (
                  <Link
                    key={order.id}
                    href={`/orders/${order.id}`}
                    className={`
                      block p-3 rounded-md transition-colors duration-200
                      ${activeOrderId === order.id
                        ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800'
                        : 'bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 border border-transparent'}
                    `}
                  >
                    <div className="flex justify-between items-center">
                      <div className="min-w-0 flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white truncate transition-colors duration-200">{order.name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
                          {order.items.length} {order.items.length === 1 ? t('orders.item') : t('orders.items')}
                        </p>
                      </div>
                      <div>
                        <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 transition-colors duration-200">
                          {t('orders.submitted')}
                        </span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <p className="text-gray-500 dark:text-gray-400 text-sm transition-colors duration-200">{t('orders.noOpenOrders')}</p>
      )}

      {isCreatingOrder ? (
        <div className="mt-4">
          <div className="flex">
            <input
              type="text"
              value={newOrderName}
              onChange={(e) => setNewOrderName(e.target.value)}
              placeholder={t('orders.orderNamePlaceholder')}
              className="flex-1 border-gray-300 dark:border-gray-600 rounded-l-md focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
            />
            <button
              onClick={handleCreateOrder}
              disabled={!newOrderName.trim()}
              className="bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-r-md hover:bg-blue-700 dark:hover:bg-blue-800 disabled:bg-gray-400 disabled:dark:bg-gray-600 transition-colors duration-200"
            >
              {t('general.create')}
            </button>
          </div>
          <button
            onClick={() => setIsCreatingOrder(false)}
            className="mt-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200"
          >
            {t('general.cancel')}
          </button>
        </div>
      ) : (
        <button
          onClick={() => setIsCreatingOrder(true)}
          className="mt-4 w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors duration-200"
        >
          <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          {t('orders.createNewOrder')}
        </button>
      )}
    </div>
  );
}
