'use client';

import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { setSearchQuery } from '../lib/redux/slices/products';
import { searchProducts } from '../lib/api/odoo';
import { Product } from '../types';
import { useRouter } from 'next/navigation';

export default function SearchAutocomplete() {
  const dispatch = useDispatch();
  const router = useRouter();

  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const searchRef = useRef<HTMLDivElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Debounced search
  useEffect(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (query.trim().length > 2) {
      setIsLoading(true);

      debounceTimerRef.current = setTimeout(async () => {
        try {
          const searchResults = await searchProducts(query);
          setResults(searchResults);
        } catch (error) {
          console.error('Error searching products:', error);
          setResults([]);
        } finally {
          setIsLoading(false);
        }
      }, 300);
    } else {
      setResults([]);
      setIsLoading(false);
    }

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [query]);

  const handleSearch = () => {
    if (query.trim()) {
      dispatch(setSearchQuery(query));
      setShowResults(false);
      router.push(`/products?q=${encodeURIComponent(query)}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleResultClick = (product: Product) => {
    setQuery(product.name);
    setShowResults(false);
    router.push(`/products/${product.id}`);
  };

  return (
    <div ref={searchRef} className="relative w-full max-w-lg">
      <div className="flex rounded-md shadow-sm border border-gray-300 hover:border-blue-300 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500 transition-all">
        <input
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setShowResults(true);
          }}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowResults(true)}
          placeholder="Search products..."
          style={{ caretColor: '#2563eb' }}
          className="flex-1 min-w-0 block w-full px-3 py-2 rounded-l-md border-0 focus:ring-0 text-sm font-medium text-gray-900 placeholder-gray-600"
        />
        <button
          type="button"
          onClick={handleSearch}
          className="inline-flex items-center px-4 py-2 border-0 rounded-r-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {isLoading ? (
            <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          )}
        </button>
      </div>

      {showResults && query.trim().length > 2 && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md overflow-hidden border border-gray-200">
          {isLoading ? (
            <div className="p-4 text-center text-gray-700 font-medium">
              Searching...
            </div>
          ) : results.length > 0 ? (
            <ul className="max-h-60 overflow-y-auto">
              {results.map(product => (
                <li
                  key={product.id}
                  onClick={() => handleResultClick(product)}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md overflow-hidden relative">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="object-contain w-full h-full"
                      />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{product.name}</p>
                      <p className="text-sm font-medium text-gray-700">${product.price.toFixed(2)}</p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-4 text-center text-gray-700 font-medium">
              No results found
            </div>
          )}
        </div>
      )}
    </div>
  );
}
