'use client';

import { useParams } from 'next/navigation';
import { mockProducts } from '../lib/api/mockData';
import { laptopProducts } from '../lib/api/laptopMockData';
import ProductDetail from './ProductDetail';

export default function ProductDetailWrapper() {
  // Get the ID from the URL using useParams hook
  const params = useParams();
  const id = params.id as string;

  // Get product directly from mock data
  const productId = Number(id);
  const product = mockProducts.find(p => p.id === productId) ||
                 laptopProducts.find(p => p.id === productId) ||
                 null;

  if (!product) {
    return <div className="p-4 text-center">Product not found</div>;
  }

  return <ProductDetail product={product} />;
}
