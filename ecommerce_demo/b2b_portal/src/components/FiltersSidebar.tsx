'use client';

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../lib/redux/store';
import {
  setCategoryFilters,
  setSubcategoryFilters,
  setBrandFilters,
  setPriceRange,
  clearFilters
} from '../lib/redux/slices/products';
import {
  availableCategories,
  availableSubcategories,
  subcategories
} from '../lib/api/mockData';
import { mockProducts } from '../lib/api/mockData';

export default function FiltersSidebar() {
  const dispatch = useDispatch();
  const { filters } = useSelector((state: RootState) => state.products);

  // Use a fixed list of brands to avoid hydration errors
  const availableBrands = [
    'Apple', 'Dell', 'HP', 'Lenovo', 'Microsoft',
    'Samsung', 'Asus', 'Acer', 'LG', 'Sony'
  ];

  // Sample categories for better UX
  const sampleCategories = [
    'Laptops', 'Desktops', 'Tablets', 'Smartphones', 'Monitors',
    'Keyboards', 'Mice', 'Headphones', 'Printers', 'Accessories'
  ];

  // Local state for UI
  const [selectedCategories, setSelectedCategories] = useState<string[]>(filters.categories);
  const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>(filters.subcategories);
  const [selectedBrands, setSelectedBrands] = useState<string[]>(filters.brands);
  const [priceRange, setPriceRangeLocal] = useState(filters.priceRange);
  const [isExpanded, setIsExpanded] = useState(true);
  const [categoryExpanded, setCategoryExpanded] = useState(true);
  const [subcategoryExpanded, setSubcategoryExpanded] = useState(true);
  const [brandsExpanded, setBrandsExpanded] = useState(true);
  const [priceExpanded, setPriceExpanded] = useState(true);

  // State to track which subcategories to show based on selected main categories
  const [visibleSubcategories, setVisibleSubcategories] = useState<string[]>([]);

  // Apply filters when local state changes
  useEffect(() => {
    dispatch(setCategoryFilters(selectedCategories));

    // Update visible subcategories based on selected main categories
    if (selectedCategories.length === 0) {
      setVisibleSubcategories(availableSubcategories);
    } else {
      const newVisibleSubcategories = selectedCategories.flatMap(category =>
        subcategories[category] || []
      );
      setVisibleSubcategories([...new Set(newVisibleSubcategories)]);
    }
  }, [selectedCategories, dispatch]);

  useEffect(() => {
    dispatch(setSubcategoryFilters(selectedSubcategories));
  }, [selectedSubcategories, dispatch]);

  useEffect(() => {
    dispatch(setBrandFilters(selectedBrands));
  }, [selectedBrands, dispatch]);

  useEffect(() => {
    dispatch(setPriceRange(priceRange));
  }, [priceRange, dispatch]);

  // Toggle category selection
  const toggleCategory = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter(c => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };

  // Toggle subcategory selection
  const toggleSubcategory = (subcategory: string) => {
    if (selectedSubcategories.includes(subcategory)) {
      setSelectedSubcategories(selectedSubcategories.filter(s => s !== subcategory));
    } else {
      setSelectedSubcategories([...selectedSubcategories, subcategory]);
    }
  };

  // Toggle brand selection
  const toggleBrand = (brand: string) => {
    if (selectedBrands.includes(brand)) {
      setSelectedBrands(selectedBrands.filter(b => b !== brand));
    } else {
      setSelectedBrands([...selectedBrands, brand]);
    }
  };

  // Toggle section expansion
  const toggleSection = (section: 'category' | 'subcategory' | 'brands' | 'price') => {
    switch (section) {
      case 'category':
        setCategoryExpanded(!categoryExpanded);
        break;
      case 'subcategory':
        setSubcategoryExpanded(!subcategoryExpanded);
        break;
      case 'brands':
        setBrandsExpanded(!brandsExpanded);
        break;
      case 'price':
        setPriceExpanded(!priceExpanded);
        break;
    }
  };

  // Handle price range change
  const handleMinPriceChange = (value: number) => {
    setPriceRangeLocal({
      ...priceRange,
      min: value,
    });
  };

  const handleMaxPriceChange = (value: number) => {
    setPriceRangeLocal({
      ...priceRange,
      max: value,
    });
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSelectedCategories([]);
    setSelectedSubcategories([]);
    setSelectedBrands([]);
    setPriceRangeLocal({ min: 0, max: 5000 });
    dispatch(clearFilters());
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-200">
      <div
        className="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center cursor-pointer transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h2 className="text-base font-semibold text-gray-800 dark:text-white transition-colors duration-200">Filters</h2>
        <svg
          className={`h-5 w-5 transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {isExpanded && (
        <div className="p-3 space-y-4">
          {/* Main Categories */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-3">
            <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleSection('category')}>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">Product Type</h3>
              <svg
                className={`h-4 w-4 transform transition-transform duration-200 ${categoryExpanded ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {categoryExpanded && (
              <div className="space-y-1 mt-2 max-h-40 overflow-y-auto pr-1 scrollbar-thin">
                {sampleCategories.map(category => (
                  <div key={category} className="flex items-center">
                    <input
                      id={`category-${category}`}
                      type="checkbox"
                      checked={selectedCategories.includes(category)}
                      onChange={() => toggleCategory(category)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded transition-colors duration-200"
                    />
                    <label htmlFor={`category-${category}`} className="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">
                      {category}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Subcategories */}
          {visibleSubcategories.length > 0 && (
            <div className="border-b border-gray-200 dark:border-gray-700 py-3">
              <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleSection('subcategory')}>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">Subcategories</h3>
                <svg
                  className={`h-4 w-4 transform transition-transform duration-200 ${subcategoryExpanded ? 'rotate-180' : ''}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              {subcategoryExpanded && (
                <div className="space-y-1 mt-2 max-h-40 overflow-y-auto pr-1 scrollbar-thin">
                  {visibleSubcategories.map(subcategory => (
                    <div key={subcategory} className="flex items-center">
                      <input
                        id={`subcategory-${subcategory}`}
                        type="checkbox"
                        checked={selectedSubcategories.includes(subcategory)}
                        onChange={() => toggleSubcategory(subcategory)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded transition-colors duration-200"
                      />
                      <label htmlFor={`subcategory-${subcategory}`} className="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">
                        {subcategory}
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Brands */}
          <div className="border-b border-gray-200 dark:border-gray-700 py-3">
            <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleSection('brands')}>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">Brands</h3>
              <svg
                className={`h-4 w-4 transform transition-transform duration-200 ${brandsExpanded ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {brandsExpanded && (
              <div className="space-y-1 mt-2 max-h-40 overflow-y-auto pr-1 scrollbar-thin">
                {availableBrands.map(brand => (
                  <div key={brand} className="flex items-center">
                    <input
                      id={`brand-${brand}`}
                      type="checkbox"
                      checked={selectedBrands.includes(brand)}
                      onChange={() => toggleBrand(brand)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded transition-colors duration-200"
                    />
                    <label htmlFor={`brand-${brand}`} className="ml-2 text-sm text-gray-700 dark:text-gray-300 transition-colors duration-200">
                      {brand}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Price Range */}
          <div className="border-b border-gray-200 dark:border-gray-700 py-3">
            <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleSection('price')}>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">Price Range</h3>
              <svg
                className={`h-4 w-4 transform transition-transform duration-200 ${priceExpanded ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            {priceExpanded && (
              <div className="space-y-3 mt-2">
                <div>
                  <label htmlFor="min-price" className="block text-sm text-gray-600 dark:text-gray-400">Min Price</label>
                  <div className="flex items-center">
                    <span className="text-gray-500 dark:text-gray-400 mr-2">$</span>
                    <input
                      id="min-price"
                      type="number"
                      min="0"
                      max={priceRange.max}
                      value={priceRange.min}
                      onChange={(e) => handleMinPriceChange(Number(e.target.value))}
                      className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="max-price" className="block text-sm text-gray-600 dark:text-gray-400">Max Price</label>
                  <div className="flex items-center">
                    <span className="text-gray-500 dark:text-gray-400 mr-2">$</span>
                    <input
                      id="max-price"
                      type="number"
                      min={priceRange.min}
                      value={priceRange.max}
                      onChange={(e) => handleMaxPriceChange(Number(e.target.value))}
                      className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Clear Filters */}
          <div className="pt-3">
            <button
              onClick={handleClearFilters}
              className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              Clear All Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
