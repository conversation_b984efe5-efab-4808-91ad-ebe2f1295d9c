'use client';

import { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import Image from 'next/image';

export default function LanguageToggle() {
  const { language, setLanguage, t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleLanguage = (newLanguage: 'en' | 'ar') => {
    setLanguage(newLanguage);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-md text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white focus:outline-none"
        aria-label="Change language"
      >
        <div className="flex items-center">
          <div className="w-6 h-4 relative rounded overflow-hidden border border-gray-300 dark:border-gray-600">
            <Image
              src={language === 'en' ? '/flags/us.svg' : '/flags/sa.svg'}
              alt={language === 'en' ? 'American Flag' : 'Saudi Arabia Flag'}
              fill
              className="object-cover"
            />
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 ml-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700">
          <div className="py-1">
            <button
              onClick={() => toggleLanguage('en')}
              className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                language === 'en'
                  ? 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <div className="w-6 h-4 relative rounded overflow-hidden border border-gray-300 dark:border-gray-600 mr-2">
                <Image
                  src="/flags/us.svg"
                  alt="American Flag"
                  fill
                  className="object-cover"
                />
              </div>
              {t('general.english')}
            </button>
            <button
              onClick={() => toggleLanguage('ar')}
              className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                language === 'ar'
                  ? 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <div className="w-6 h-4 relative rounded overflow-hidden border border-gray-300 dark:border-gray-600 mr-2">
                <Image
                  src="/flags/sa.svg"
                  alt="Saudi Arabia Flag"
                  fill
                  className="object-cover"
                />
              </div>
              {t('general.arabic')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
