'use client';

import { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { setActiveOrder, createOrder, ensureDefaultOrder, updateOrderName } from '../lib/redux/slices/orders';
import Link from 'next/link';

export default function HeaderOrder() {
  const dispatch = useDispatch();
  const { orders, activeOrderId } = useSelector((state: RootState) => state.orders);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [newOrderName, setNewOrderName] = useState('');
  const [editingOrderId, setEditingOrderId] = useState<string | null>(null);
  const [editOrderName, setEditOrderName] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const activeOrder = orders.find(o => o.id === activeOrderId);
  const totalItems = activeOrder?.items.reduce((sum, item) => sum + item.quantity, 0) || 0;

  // Ensure a default order exists
  useEffect(() => {
    dispatch(ensureDefaultOrder());
  }, [dispatch]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleOrderSelect = (orderId: string) => {
    dispatch(setActiveOrder(orderId));
    setIsDropdownOpen(false);
  };

  const handleCreateOrder = () => {
    if (newOrderName.trim()) {
      dispatch(createOrder({ name: newOrderName.trim() }));
      setNewOrderName('');
      setIsCreatingOrder(false);
    }
  };

  const handleEditOrder = (orderId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const order = orders.find(o => o.id === orderId);
    if (order) {
      setEditingOrderId(orderId);
      setEditOrderName(order.name);
    }
  };

  const handleSaveOrderName = () => {
    if (editingOrderId && editOrderName.trim()) {
      dispatch(updateOrderName({ orderId: editingOrderId, name: editOrderName.trim() }));
      setEditingOrderId(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingOrderId(null);
  };

  // Get open orders (draft and submitted)
  const openOrders = orders.filter(order => order.state === 'draft' || order.state === 'submitted');

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
      >
        <div className="relative mr-1">
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          {totalItems > 0 && (
            <span className="absolute -top-2 -right-2 bg-blue-600 dark:bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center transition-colors duration-200">
              {totalItems}
            </span>
          )}
        </div>
        <span className="hidden md:inline transition-colors duration-200">{activeOrder?.name || 'No Order'}</span>
        <svg className={`ml-1 h-4 w-4 transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 transition-colors duration-200">
          <div className="p-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center transition-colors duration-200">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">Your Orders</h3>
            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 transition-colors duration-200">{openOrders.length} Open</span>
          </div>

          <div className="max-h-60 overflow-y-auto">
            {openOrders.length > 0 ? (
              <div className="py-1">
                {openOrders.map(order => (
                  <div key={order.id} className="border-b border-gray-100 last:border-b-0">
                    {editingOrderId === order.id ? (
                      <div className="p-3">
                        <div className="flex items-center space-x-1">
                          <input
                            type="text"
                            value={editOrderName}
                            onChange={(e) => setEditOrderName(e.target.value)}
                            className="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 text-gray-900 font-medium placeholder-gray-700"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleSaveOrderName();
                              if (e.key === 'Escape') handleCancelEdit();
                            }}
                            autoFocus
                          />
                          <button
                            onClick={handleSaveOrderName}
                            className="text-green-600 hover:text-green-800 p-1"
                          >
                            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="text-red-600 hover:text-red-800 p-1"
                          >
                            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleOrderSelect(order.id)}
                        className={`w-full text-left px-4 py-2 text-sm ${activeOrderId === order.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-1 flex-1 min-w-0">
                            <span className="font-medium truncate max-w-[120px] inline-block">{order.name}</span>
                            {/* Show edit button for draft orders and status badge for submitted orders */}
                            {order.state === 'draft' ? (
                              <div
                                onClick={(e) => {
                                  e.stopPropagation(); // Prevent triggering the parent button's onClick
                                  handleEditOrder(order.id, e);
                                }}
                                className="text-gray-400 hover:text-gray-600 p-1 cursor-pointer"
                                role="button"
                                tabIndex={0}
                                aria-label="Edit order name"
                              >
                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                              </div>
                            ) : order.state === 'submitted' && (
                              <span className="ml-2 px-1.5 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                                Submitted
                              </span>
                            )}
                          </div>
                          <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800 flex-shrink-0">
                            {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                          </span>
                        </div>
                      </button>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-2 px-4 text-sm text-gray-500">
                No open orders yet. Create your first order.
              </div>
            )}
          </div>

          <div className="p-2 border-t">
            {isCreatingOrder ? (
              <div className="space-y-2">
                <input
                  type="text"
                  value={newOrderName}
                  onChange={(e) => setNewOrderName(e.target.value)}
                  placeholder="Enter order name..."
                  className="w-full px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 font-medium text-gray-900 placeholder-gray-700"
                  autoFocus
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleCreateOrder}
                    disabled={!newOrderName.trim()}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium transition-colors duration-200"
                  >
                    Create
                  </button>
                  <button
                    onClick={() => setIsCreatingOrder(false)}
                    className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={() => setIsCreatingOrder(true)}
                  className="flex-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 font-medium transition-colors duration-200"
                >
                  New Order
                </button>
                <Link
                  href="/orders"
                  onClick={() => setIsDropdownOpen(false)}
                  className="flex-1 px-3 py-1 text-xs border border-blue-300 rounded bg-blue-50 text-blue-700 hover:bg-blue-100 text-center font-medium transition-colors duration-200"
                >
                  View All
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
