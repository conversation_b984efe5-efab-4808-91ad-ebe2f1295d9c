'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { useLanguage } from '../contexts/LanguageContext';
import { v4 as uuidv4 } from 'uuid';

// In a real app, we would have a proper notification system
// For now, we'll use a simple custom event to simulate notifications
const notifyNewMessage = (orderId: string, orderName: string, message: string, sender: any) => {
  const event = new CustomEvent('newMessage', {
    detail: {
      orderId,
      orderName,
      message,
      sender
    }
  });
  window.dispatchEvent(event);
};

// Define message types
type MessageType = 'text' | 'system' | 'attachment';

// Define message interface
interface Message {
  id: string;
  type: MessageType;
  content: string;
  sender: {
    id: string;
    name: string;
    avatar: string;
    isCurrentUser: boolean;
  };
  timestamp: string;
  attachments?: {
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
}

interface OrderChatterProps {
  orderId: string;
}

export default function OrderChatter({ orderId }: OrderChatterProps) {
  const { t } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isAttaching, setIsAttaching] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get current user from Redux store (mock data for now)
  const currentUser = {
    id: 'user-1',
    name: 'Sarah Johnson',
    avatar: 'https://randomuser.me/api/portraits/women/23.jpg',
  };

  // Mock data for other participants
  const otherParticipants = [
    {
      id: 'user-2',
      name: 'Michael Chen',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      role: 'Account Manager'
    }
  ];

  // Load initial messages (mock data)
  useEffect(() => {
    // In a real app, you would fetch messages from an API
    const initialMessages: Message[] = [
      {
        id: uuidv4(),
        type: 'system',
        content: 'Order created',
        sender: {
          id: 'system',
          name: 'System',
          avatar: '',
          isCurrentUser: false
        },
        timestamp: new Date(Date.now() - ******** * 3).toISOString() // 3 days ago
      },
      {
        id: uuidv4(),
        type: 'text',
        content: 'Hi Sarah, I noticed you\'ve added several laptops to this order. Would you like me to check if there are any volume discounts available?',
        sender: {
          id: otherParticipants[0].id,
          name: otherParticipants[0].name,
          avatar: otherParticipants[0].avatar,
          isCurrentUser: false
        },
        timestamp: new Date(Date.now() - ******** * 2).toISOString() // 2 days ago
      },
      {
        id: uuidv4(),
        type: 'text',
        content: 'Yes, that would be great! We\'re looking to upgrade our entire IT department.',
        sender: {
          id: currentUser.id,
          name: currentUser.name,
          avatar: currentUser.avatar,
          isCurrentUser: true
        },
        timestamp: new Date(Date.now() - ******** * 2 + 3600000).toISOString() // 2 days ago + 1 hour
      },
      {
        id: uuidv4(),
        type: 'text',
        content: 'I\'ve checked with our supplier and we can offer a 15% discount if you order 10 or more laptops. Would you like me to apply this to your order?',
        sender: {
          id: otherParticipants[0].id,
          name: otherParticipants[0].name,
          avatar: otherParticipants[0].avatar,
          isCurrentUser: false
        },
        timestamp: new Date(Date.now() - ********).toISOString() // 1 day ago
      },
      {
        id: uuidv4(),
        type: 'attachment',
        content: 'Here\'s the quote document with the details.',
        sender: {
          id: otherParticipants[0].id,
          name: otherParticipants[0].name,
          avatar: otherParticipants[0].avatar,
          isCurrentUser: false
        },
        timestamp: new Date(Date.now() - ******** + 1800000).toISOString(), // 1 day ago + 30 minutes
        attachments: [
          {
            name: 'Volume_Discount_Quote.pdf',
            url: '#',
            type: 'application/pdf',
            size: 2457600 // 2.4 MB
          }
        ]
      }
    ];

    setMessages(initialMessages);
  }, [orderId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'long' });
    } else {
      return date.toLocaleDateString();
    }
  };

  // Handle sending a new message
  const handleSendMessage = () => {
    if (newMessage.trim() === '') return;

    const newMsg: Message = {
      id: uuidv4(),
      type: 'text',
      content: newMessage.trim(),
      sender: {
        id: currentUser.id,
        name: currentUser.name,
        avatar: currentUser.avatar,
        isCurrentUser: true
      },
      timestamp: new Date().toISOString()
    };

    setMessages([...messages, newMsg]);
    setNewMessage('');
  };

  // Handle file attachment
  const handleAttachment = () => {
    fileInputRef.current?.click();
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // In a real app, you would upload the file to a server
    // For now, we'll just create a mock attachment message
    const file = files[0];

    const newMsg: Message = {
      id: uuidv4(),
      type: 'attachment',
      content: `Attached: ${file.name}`,
      sender: {
        id: currentUser.id,
        name: currentUser.name,
        avatar: currentUser.avatar,
        isCurrentUser: true
      },
      timestamp: new Date().toISOString(),
      attachments: [
        {
          name: file.name,
          url: '#',
          type: file.type,
          size: file.size
        }
      ]
    };

    setMessages([...messages, newMsg]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Group messages by date
  const groupedMessages: { date: string; messages: Message[] }[] = [];
  let currentDate = '';

  messages.forEach(message => {
    const messageDate = new Date(message.timestamp).toLocaleDateString();

    if (messageDate !== currentDate) {
      currentDate = messageDate;
      groupedMessages.push({
        date: messageDate,
        messages: [message]
      });
    } else {
      groupedMessages[groupedMessages.length - 1].messages.push(message);
    }
  });

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-colors duration-200">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{t('orders.discussion')}</h2>
        <div className="flex items-center">
          <div className="flex -space-x-2">
            {[currentUser, ...otherParticipants].map(user => (
              <div key={user.id} className="relative h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800">
                <Image
                  src={user.avatar}
                  alt={user.name}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            {otherParticipants.length + 1} {t('orders.participants')}
          </span>
        </div>
      </div>

      {/* Messages */}
      <div className="p-4 h-96 overflow-y-auto bg-gray-50 dark:bg-gray-900">
        {groupedMessages.map((group, groupIndex) => (
          <div key={groupIndex} className="mb-6">
            <div className="flex justify-center mb-4">
              <div className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-full text-xs text-gray-700 dark:text-gray-300">
                {new Date(group.date).toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })}
              </div>
            </div>

            {group.messages.map(message => (
              <div key={message.id} className={`mb-4 ${message.sender.isCurrentUser ? 'flex flex-col items-end' : 'flex flex-col items-start'}`}>
                {message.type === 'system' ? (
                  <div className="flex justify-center w-full mb-4">
                    <div className="px-3 py-1 bg-blue-100 dark:bg-blue-900 rounded-full text-xs text-blue-700 dark:text-blue-300">
                      {message.content}
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center mb-1">
                      {!message.sender.isCurrentUser && (
                        <div className="relative h-6 w-6 rounded-full overflow-hidden mr-2">
                          <Image
                            src={message.sender.avatar}
                            alt={message.sender.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <span className="text-xs text-gray-700 dark:text-gray-300 mr-2">
                        {message.sender.isCurrentUser ? 'You' : message.sender.name}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatTimestamp(message.timestamp)}
                      </span>
                    </div>

                    <div className={`max-w-[80%] rounded-lg p-3 ${
                      message.sender.isCurrentUser
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
                    }`}>
                      <p className="text-sm">{message.content}</p>

                      {message.attachments && message.attachments.length > 0 && (
                        <div className={`mt-2 p-2 rounded ${
                          message.sender.isCurrentUser
                            ? 'bg-blue-700'
                            : 'bg-gray-300 dark:bg-gray-600'
                        }`}>
                          {message.attachments.map((attachment, index) => (
                            <div key={index} className="flex items-center">
                              <svg className="h-5 w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{attachment.name}</p>
                                <p className="text-xs opacity-80">{formatFileSize(attachment.size)}</p>
                              </div>
                              <a
                                href={attachment.url}
                                className="ml-2 p-1 rounded-full hover:bg-opacity-20 hover:bg-black"
                                download
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                              </a>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-end">
          <div className="flex-1 relative">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder={t('orders.typeMessage')}
              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg py-2 px-3 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-700 dark:placeholder-gray-300 bg-white dark:bg-gray-700 resize-none transition-colors duration-200"
              rows={2}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
          </div>
          <div className="ml-3 flex">
            <button
              onClick={handleAttachment}
              className="p-2 rounded-full text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileChange}
              />
            </button>
            <button
              onClick={handleSendMessage}
              disabled={newMessage.trim() === ''}
              className="ml-2 p-2 rounded-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
