'use client';

import { ReactNode, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import SearchAutocomplete from './SearchAutocomplete';
import HeaderOrder from './HeaderOrder';
import ThemeToggle from './ThemeToggle';
import LanguageToggle from './LanguageToggle';
import WishlistNotification from './WishlistNotification';
import { useLanguage } from '../contexts/LanguageContext';
import { useGlassmorphism } from '../contexts/ThemeContext';
import { cn } from '../lib/utils';

interface PortalLayoutContentProps {
  children: ReactNode;
}

export default function PortalLayoutContent({ children }: PortalLayoutContentProps) {
  const { t, dir } = useLanguage();
  const { enabled: isGlass } = useGlassmorphism();
  const [mobileSearchVisible, setMobileSearchVisible] = useState(false);
  const pathname = usePathname();

  // Function to check if a path is active
  const isActive = (path: string) => {
    if (path === '/') return pathname === '/';
    return pathname.startsWith(path);
  };

  return (
    <div className={cn(
      'min-h-screen transition-colors duration-200',
      'bg-theme-background',
      dir === 'rtl' ? 'rtl' : 'ltr'
    )}>
      {/* Fixed Header */}
      <header className={cn(
        'fixed top-0 left-0 right-0 shadow-md transition-all duration-200 z-50',
        {
          'glass-nav': isGlass,
          'bg-theme-background border-b border-theme-border': !isGlass,
        }
      )}>
        <div className="w-full max-w-[1600px] mx-auto px-2 sm:px-3 lg:px-4">
          {/* Top header with logo, search, and action buttons */}
          <div className="flex justify-between items-center py-3 sm:py-4">
            <div className="flex items-center">
              <Link href="/" className="flex items-center group">
                <div className="w-20 h-10 rounded-lg bg-gradient-to-br from-blue-600 to-indigo-600 dark:from-blue-500 dark:to-indigo-500 flex items-center justify-center text-white font-bold text-lg mr-2 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-105">
                  KAUST
                </div>
                <span className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 group-hover:from-blue-500 group-hover:to-indigo-500 transition-all duration-300">
                  Marketplace
                </span>
              </Link>
            </div>

            <div className="w-full max-w-lg mx-4 hidden md:block">
              <SearchAutocomplete />
            </div>

            <div className="flex items-center space-x-1 sm:space-x-2">
              <HeaderOrder />
              {/* Mobile search button */}
              <button
                className={`md:hidden p-1.5 rounded-full transition-all duration-200 ${mobileSearchVisible ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400' : 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50'}`}
                onClick={() => setMobileSearchVisible(!mobileSearchVisible)}
                aria-label="Toggle search"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
              {/* Wishlist */}
              <div className="relative group">
                <WishlistNotification />
                <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 group-hover:w-full group-hover:left-0 transition-all duration-300"></span>
              </div>
              <div className="relative group">
                <LanguageToggle />
                <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 group-hover:w-full group-hover:left-0 transition-all duration-300"></span>
              </div>
              <div className="relative group">
                <ThemeToggle />
                <span className="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 group-hover:w-full group-hover:left-0 transition-all duration-300"></span>
              </div>
            </div>
          </div>

          {/* Mobile search bar - only visible when toggled */}
          <div className={`overflow-hidden transition-all duration-300 md:hidden ${mobileSearchVisible ? 'max-h-16 opacity-100 py-2 pb-4' : 'max-h-0 opacity-0'}`}>
            <SearchAutocomplete />
          </div>

          {/* Navigation menu on a new line */}
          <nav className="flex items-center justify-center space-x-2 sm:space-x-6 py-2 border-t border-gray-200 dark:border-gray-700 transition-colors duration-200 overflow-x-auto scrollbar-thin">
            <div className="relative">
              <Link
                href="/products"
                className={cn(
                  'inline-block px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-200 group',
                  isActive('/products')
                    ? 'text-primary bg-primary/10'
                    : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-background-secondary'
                )}
              >
                <span className="relative z-10">{t('nav.products')}</span>
              </Link>
              {isActive('/products') ? (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary rounded-full transform scale-x-100 origin-left transition-transform duration-200"></span>
              ) : (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary rounded-full transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-200"></span>
              )}
            </div>
            <div className="relative">
              <Link
                href="/orders"
                className={cn(
                  'inline-block px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-200 group',
                  isActive('/orders')
                    ? 'text-primary bg-primary/10'
                    : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-background-secondary'
                )}
              >
                <span className="relative z-10">{t('nav.orders')}</span>
              </Link>
              {isActive('/orders') ? (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary rounded-full transform scale-x-100 origin-left transition-transform duration-200"></span>
              ) : (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary rounded-full transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-200"></span>
              )}
            </div>
            <div className="relative">
              <Link
                href="/account"
                className={cn(
                  'inline-block px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-200 group',
                  isActive('/account')
                    ? 'text-primary bg-primary/10'
                    : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-background-secondary'
                )}
              >
                <span className="relative z-10">{t('nav.account')}</span>
              </Link>
              {isActive('/account') ? (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary rounded-full transform scale-x-100 origin-left transition-transform duration-200"></span>
              ) : (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary rounded-full transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-200"></span>
              )}
            </div>
          </nav>
        </div>
      </header>

      {/* Header spacer to prevent content from being hidden under fixed header */}
      <div className={`${mobileSearchVisible ? 'h-36 sm:h-40' : 'h-28 sm:h-32'} transition-all duration-200`}></div>

      {/* Main content */}
      <main className="w-full max-w-[1600px] mx-auto px-0 sm:px-2 py-3 text-theme-foreground transition-colors duration-200">
        {children}
      </main>

      {/* Footer */}
      <footer className={cn(
        'border-t py-8 transition-colors duration-200',
        {
          'glass-nav border-theme-border/30': isGlass,
          'bg-theme-background border-theme-border': !isGlass,
        }
      )}>
        <div className="w-full max-w-[1600px] mx-auto px-2 sm:px-3 lg:px-4">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex justify-center md:order-2">
              <span className="text-sm text-theme-foreground-muted transition-colors duration-200">
                &copy; 2023 KAUST Marketplace. All rights reserved.
              </span>
            </div>
            <div className="mt-8 md:mt-0 md:order-1">
              <p className="text-center text-sm text-theme-foreground-muted transition-colors duration-200">
                Powered by Next.js 15 and Redux
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
