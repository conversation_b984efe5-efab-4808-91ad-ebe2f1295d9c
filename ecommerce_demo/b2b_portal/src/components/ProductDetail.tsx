'use client';

import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { addItemToOrder, createOrder } from '../lib/redux/slices/orders';
import { addToWishlist, removeFromWishlist } from '../lib/redux/slices/wishlist';
import { Product, OrderItem, ProductVariant } from '../types';
import Link from 'next/link';
import ImagePreviewer from './ImagePreviewer';
import { useLanguage } from '../contexts/LanguageContext';

interface ProductDetailProps {
  product: Product;
}

export default function ProductDetail({ product }: ProductDetailProps) {
  const dispatch = useDispatch();
  // We don't need activeBasketId anymore as we're using activeOrderId
  const { orders } = useSelector((state: RootState) => state.orders);
  const { items: wishlistItems } = useSelector((state: RootState) => state.wishlist);
  const isInWishlist = wishlistItems.some(item => Number(item.id) === Number(product.id));
  const { t } = useLanguage();

  const dropdownRef = useRef<HTMLDivElement>(null);

  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | undefined>(
    product?.variants.length ? product.variants[0] : undefined
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [newOrderName, setNewOrderName] = useState('');

  // Get draft orders for the dropdown
  const draftOrders = orders.filter(order => order.state === 'draft');
  const activeOrderId = orders.find(order => order.state === 'draft')?.id || null;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleAddToBasket = (orderId: string = activeOrderId || '') => {
    if (!orderId) {
      alert(t('products.selectOrderFirst'));
      return;
    }

    const orderItem: OrderItem = {
      productId: product.id,
      quantity,
      selectedVariantId: selectedVariant?.id,
      selectedServiceOptions: [], // Empty array since we removed service options
    };

    dispatch(addItemToOrder({
      orderId: orderId,
      item: orderItem,
    }));

    // Reset selections
    setQuantity(1);
    if (product.variants.length > 0) {
      setSelectedVariant(product.variants[0]);
    }

    // Close dropdown
    setIsDropdownOpen(false);

    // Show success message
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  };

  const handleCreateOrder = () => {
    if (newOrderName.trim()) {
      // Store the name before clearing it
      const orderNameToCreate = newOrderName.trim();

      // Create a new order with just the name
      dispatch(createOrder({
        name: orderNameToCreate
      }));

      setNewOrderName('');
      setIsCreatingOrder(false);

      // The order ID will be generated in the reducer
      // We'll use the active order ID after a short delay
      setTimeout(() => {
        // Find the newly created order by name
        const newOrder = orders.find(o => o.name === orderNameToCreate);
        if (newOrder) {
          handleAddToBasket(newOrder.id);
        } else {
          // If we can't find the order by name, use the most recently created order
          const mostRecentOrder = [...orders].sort((a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          )[0];
          if (mostRecentOrder) {
            handleAddToBasket(mostRecentOrder.id);
          }
        }
      }, 100);
    }
  };

  // Update product images when variant changes
  useEffect(() => {
    // The ImagePreviewer component handles image selection internally
    // We just need to make sure the product has the correct images
  }, [selectedVariant]);

  if (!product) {
    return <div className="p-4 text-center">Product not found</div>;
  }

  return (
    <div className="w-full px-1 sm:px-2 py-3 sm:py-4">
      <div className="mb-3">
        <Link href="/products" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Products
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="flex flex-col lg:flex-row">
          {/* Product Image - Smaller and more responsive */}
          <div className="w-full lg:w-2/5 p-2 sm:p-3 md:p-4">
            {/* Use the new ImagePreviewer component with a key to prevent hydration issues */}
            <ImagePreviewer
              key={`product-${product.id}`}
              images={product.images || [product.image]}
              alt={product.name}
              mainImageWidth={600}
              mainImageHeight={600}
              thumbnailWidth={60}
              thumbnailHeight={60}
            />

            {/* Variant color swatches */}
            {product.variants.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-4 w-full">
                <div className="w-full text-sm font-medium text-gray-700 mb-1">Color Options:</div>
                {product.variants.map((variant) => (
                  <button
                    key={variant.id}
                    onClick={() => setSelectedVariant(variant)}
                    className={`relative w-12 h-12 rounded-full overflow-hidden border-2 ${selectedVariant?.id === variant.id ? 'border-blue-500' : 'border-gray-200'}`}
                    title={variant.name}
                  >
                    {variant.color && (
                      <div
                        className="absolute inset-0"
                        style={{ backgroundColor: variant.color }}
                      />
                    )}
                  </button>
                ))}
              </div>
            )}

            {/* Brand and model if available */}
            {(product.brand || product.model) && (
              <div className="mt-4 text-sm text-gray-600">
                {product.brand && <span className="font-medium">{product.brand}</span>}
                {product.brand && product.model && <span> • </span>}
                {product.model && <span>{product.model}</span>}
              </div>
            )}
          </div>

          {/* Product Details - Larger to fit more details */}
          <div className="w-full lg:w-3/5 p-2 sm:p-3 md:p-4 lg:border-l border-gray-200 dark:border-gray-700">
            <div className="flex flex-col mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
              <div className="flex justify-between items-center mt-1">
                <div className="text-sm text-gray-600">
                  {product.sku && <span>SKU: {product.sku}</span>}
                </div>
              </div>
            </div>
            <p className="text-gray-700 mb-4">{product.description}</p>

            <div className="flex items-center mb-4">
              {product.discountPercentage ? (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <span className="text-3xl font-bold text-green-600">${product.discountedPrice?.toFixed(2)}</span>
                    {selectedVariant?.price !== undefined && selectedVariant.price !== 0 && (
                      <span className="ml-2 text-sm text-gray-700 font-medium">
                        {selectedVariant.price > 0 ? `+$${selectedVariant.price.toFixed(2)}` : `-$${Math.abs(selectedVariant.price).toFixed(2)}`}
                      </span>
                    )}
                    <span className="ml-3 px-2 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-md">
                      {product.discountPercentage}% OFF
                    </span>
                  </div>
                  <div className="mt-1">
                    <span className="text-lg line-through text-gray-500">
                      ${product.price.toFixed(2)}
                    </span>
                    <span className="ml-2 text-sm text-gray-700">
                      You save: ${(product.price - (product.discountedPrice || 0)).toFixed(2)}
                    </span>
                  </div>
                </div>
              ) : (
                <div>
                  <span className="text-3xl font-bold text-blue-600">${product.price.toFixed(2)}</span>
                  {selectedVariant?.price !== undefined && selectedVariant.price !== 0 && (
                    <span className="ml-2 text-sm text-gray-700 font-medium">
                      {selectedVariant.price > 0 ? `+$${selectedVariant.price.toFixed(2)}` : `-$${Math.abs(selectedVariant.price).toFixed(2)}`}
                    </span>
                  )}
                </div>
              )}
            </div>

            <div className="mt-4 flex flex-wrap gap-2">
              {product.categories.map(category => (
                <span
                  key={category}
                  className="inline-block bg-gray-100 text-gray-900 font-medium text-xs px-2 py-1 rounded"
                >
                  {category}
                </span>
              ))}
            </div>

            {/* Product attributes */}
            {product.attributes && product.attributes.length > 0 && (
              <div className="mt-6 border-t pt-6">
                <h3 className="text-sm font-medium text-gray-900 mb-4">Specifications</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {product.attributes.map((attr, index) => (
                    <div key={index} className="flex">
                      <span className="text-sm font-medium text-gray-700 w-24 flex-shrink-0">{attr.name}:</span>
                      <span className="text-sm text-gray-900 font-medium">{attr.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="mt-6 space-y-6">
              {/* Variants - Simplified to max 4 options */}
              {product.variants.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Options</label>
                  <div className="flex flex-wrap gap-2">
                    {product.variants.slice(0, 4).map(variant => (
                      <button
                        key={variant.id}
                        onClick={() => setSelectedVariant(variant)}
                        className={`px-4 py-2 border rounded-md ${selectedVariant?.id === variant.id ? 'border-blue-600 bg-blue-50 text-blue-700 font-medium' : 'border-gray-300 text-gray-700 hover:border-blue-300'}`}
                      >
                        <div className="flex items-center">
                          {variant.color && (
                            <div
                              className="w-4 h-4 rounded-full mr-2 flex-shrink-0 border border-gray-300"
                              style={{ backgroundColor: variant.color }}
                            />
                          )}
                          <span>{variant.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Service Options removed */}

              {/* Quantity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                <div className="flex rounded-md shadow-sm">
                  <button
                    type="button"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="relative inline-flex items-center px-3 py-2 rounded-l-md border border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100"
                  >
                    <span className="sr-only">Decrease</span>
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                    </svg>
                  </button>
                  <input
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full text-center border-blue-300 text-gray-800"
                  />
                  <button
                    type="button"
                    onClick={() => setQuantity(quantity + 1)}
                    className="relative inline-flex items-center px-3 py-2 rounded-r-md border border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100"
                  >
                    <span className="sr-only">Increase</span>
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Add to Order Section */}
              <div className="flex gap-2">
                {/* Order Selection Dropdown */}
                <div className="relative flex-grow" ref={dropdownRef}>
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    disabled={draftOrders.length === 0 && !activeOrderId}
                    className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white py-3 px-4 rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed text-sm flex justify-between items-center transition-colors duration-200"
                  >
                    <div className="flex items-center overflow-hidden max-w-[calc(100%-20px)]">
                      <span className="truncate">
                        {activeOrderId ? (
                          <>
                            {t('products.addToOrder')}:{' '}
                            <span className="font-medium truncate max-w-[120px] inline-block align-bottom">
                              {orders.find(o => o.id === activeOrderId)?.name || 'Order'}
                            </span>
                          </>
                        ) : (
                          t('products.addToOrder')
                        )}
                      </span>
                    </div>
                    <svg className={`ml-1 h-4 w-4 flex-shrink-0 transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {isDropdownOpen && (
                    <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                      {/* Quick add to active order */}
                      {activeOrderId && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddToBasket(activeOrderId);
                          }}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 font-medium border-b border-gray-100 dark:border-gray-700 flex justify-between items-center transition-colors duration-200"
                        >
                          <span className="flex items-center overflow-hidden">
                            <span className="truncate">{t('orders.addToActiveOrder')}: </span>
                            <span className="font-medium truncate max-w-[100px] inline-block ml-1">{orders.find(o => o.id === activeOrderId)?.name || 'active order'}</span>
                          </span>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Active</span>
                        </button>
                      )}

                      {/* Draft orders list */}
                      {draftOrders.length > 0 ? (
                        <div className="max-h-60 overflow-y-auto">
                          {draftOrders.map(order => (
                            <button
                              key={order.id}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddToBasket(order.id);
                              }}
                              className={`w-full text-left px-3 py-2 text-sm ${order.id === activeOrderId ? 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'} transition-colors duration-200`}
                            >
                              <div className="flex justify-between items-center">
                                <span className="font-medium truncate max-w-[200px]">{order.name}</span>
                                <span className="text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 transition-colors duration-200 flex-shrink-0">
                                  {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                                </span>
                              </div>
                            </button>
                          ))}
                        </div>
                      ) : !activeOrderId && (
                        <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
                          No draft orders available
                        </div>
                      )}

                      {/* Create new order */}
                      {isCreatingOrder ? (
                        <div className="p-2 border-t border-gray-200 dark:border-gray-700">
                          <input
                            type="text"
                            value={newOrderName}
                            onChange={(e) => setNewOrderName(e.target.value)}
                            placeholder="Enter order name..."
                            className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 font-medium text-gray-900 dark:text-white placeholder-gray-700 dark:placeholder-gray-300 bg-white dark:bg-gray-700 transition-colors duration-200"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="flex space-x-2 mt-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCreateOrder();
                              }}
                              className="flex-1 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white text-xs py-1 px-2 rounded transition-colors duration-200"
                            >
                              {t('general.save')}
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsCreatingOrder(false);
                                setNewOrderName('');
                              }}
                              className="flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 text-xs py-1 px-2 rounded transition-colors duration-200"
                            >
                              {t('general.cancel')}
                            </button>
                          </div>
                        </div>
                      ) : (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsCreatingOrder(true);
                          }}
                          className="w-full text-left px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900 border-t border-gray-200 dark:border-gray-700 font-medium transition-colors duration-200"
                        >
                          + {t('orders.createNewOrder')}
                        </button>
                      )}
                    </div>
                  )}

                  {/* Success message */}
                  {showSuccessMessage && (
                    <div className="absolute top-0 left-0 right-0 -mt-8 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs py-1 px-2 rounded text-center transition-colors duration-200">
                      Added to order!
                    </div>
                  )}
                </div>

                {/* Wishlist Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isInWishlist) {
                      dispatch(removeFromWishlist(String(product.id)));
                    } else {
                      dispatch(addToWishlist(product));
                    }
                  }}
                  className={`flex-shrink-0 py-1.5 px-3 rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center ${isInWishlist ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50' : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'}`}
                  title={isInWishlist ? t('wishlist.removeFromWishlist') : t('wishlist.addToWishlist')}
                  aria-label={isInWishlist ? t('wishlist.removeFromWishlist') : t('wishlist.addToWishlist')}
                >
                  <svg className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill={isInWishlist ? 'currentColor' : 'none'} stroke="currentColor" strokeWidth={isInWishlist ? '0' : '2'}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  <span className="text-sm">{isInWishlist ? t('wishlist.removeFromWishlist') : t('wishlist.addToWishlist')}</span>
                </button>

                {/* Quick Add Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToBasket();
                  }}
                  disabled={!activeOrderId}
                  className="flex-shrink-0 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white py-1.5 px-3 rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed text-sm flex items-center justify-center transition-colors duration-200"
                  title="Add to cart"
                  aria-label="Add to cart"
                >
                  <svg className="h-4 w-4" viewBox="0 0 576 512" fill="currentColor">
                    <path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM252 160c0 11 9 20 20 20h44v44c0 11 9 20 20 20s20-9 20-20V180h44c11 0 20-9 20-20s-9-20-20-20H356V96c0-11-9-20-20-20s-20 9-20 20v44H272c-11 0-20 9-20 20z"/>
                  </svg>
                </button>
              </div>

              {/* Stock Information */}
              <div className="text-sm text-gray-700 font-medium">
                {product.stock > 10 ? (
                  <span className="text-green-700">In Stock</span>
                ) : product.stock > 0 ? (
                  <span className="text-yellow-700">Low Stock: Only {product.stock} left</span>
                ) : (
                  <span className="text-red-700">Out of Stock</span>
                )}
              </div>

              {/* Return to Products */}
              <Link
                href="/products"
                className="inline-flex items-center justify-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-white hover:bg-blue-50"
              >
                <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Products
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
