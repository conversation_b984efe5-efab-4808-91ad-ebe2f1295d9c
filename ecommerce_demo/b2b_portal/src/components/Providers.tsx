'use client';

import { ReactNode } from 'react';
import { Provider } from 'react-redux';
import { getStore } from '../lib/redux/store';
import { ThemeProvider } from '../contexts/ThemeContext';
import { LanguageProvider } from '../contexts/LanguageContext';

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  // Get a store instance
  const store = getStore();

  return (
    <Provider store={store}>
      <ThemeProvider>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </ThemeProvider>
    </Provider>
  );
}
