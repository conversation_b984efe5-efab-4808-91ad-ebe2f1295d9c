'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '../contexts/LanguageContext';
import Image from 'next/image';

// Define message notification type
interface MessageNotification {
  id: string;
  orderId: string;
  orderName: string;
  sender: {
    name: string;
    avatar: string;
  };
  message: string;
  timestamp: string;
  read: boolean;
}

export default function MessageNotification() {
  const router = useRouter();
  const { t } = useLanguage();
  const [notifications, setNotifications] = useState<MessageNotification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Load mock notifications
  useEffect(() => {
    // In a real app, you would fetch notifications from an API
    const mockNotifications: MessageNotification[] = [
      {
        id: 'notif-1',
        orderId: 'order-1',
        orderName: 'Q2 Hardware Refresh',
        sender: {
          name: 'Michael Chen',
          avatar: 'https://randomuser.me/api/portraits/men/45.jpg'
        },
        message: 'I\'ve checked with our supplier and we can offer a 15% discount if you order 10 or more laptops.',
        timestamp: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        read: false
      },
      {
        id: 'notif-2',
        orderId: 'order-2',
        orderName: 'Office Supplies',
        sender: {
          name: 'Emily Rodriguez',
          avatar: 'https://randomuser.me/api/portraits/women/32.jpg'
        },
        message: 'Your order has been confirmed and will be shipped tomorrow.',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        read: false
      },
      {
        id: 'notif-3',
        orderId: 'order-3',
        orderName: 'Software Licenses',
        sender: {
          name: 'Alex Johnson',
          avatar: 'https://randomuser.me/api/portraits/men/22.jpg'
        },
        message: 'We\'ve updated the quote with the additional licenses you requested.',
        timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        read: true
      }
    ];
    
    setNotifications(mockNotifications);
    
    // Calculate unread count
    const unread = mockNotifications.filter(notif => !notif.read).length;
    setUnreadCount(unread);
  }, []);
  
  // Format timestamp
  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);
    
    if (diffMins < 60) {
      return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };
  
  // Handle notification click
  const handleNotificationClick = (notification: MessageNotification) => {
    // Mark as read
    setNotifications(notifications.map(notif => 
      notif.id === notification.id ? { ...notif, read: true } : notif
    ));
    
    // Update unread count
    setUnreadCount(prev => Math.max(0, prev - 1));
    
    // Close dropdown
    setIsOpen(false);
    
    // Navigate to order detail page
    router.push(`/orders/${notification.orderId}`);
  };
  
  // Mark all as read
  const markAllAsRead = () => {
    setNotifications(notifications.map(notif => ({ ...notif, read: true })));
    setUnreadCount(0);
  };
  
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-md text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white focus:outline-none relative"
        aria-label="Message notifications"
      >
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 -mt-1 -mr-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white">
            {unreadCount}
          </span>
        )}
      </button>
      
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              {t('notifications.messages')}
            </h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                {t('notifications.markAllAsRead')}
              </button>
            )}
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {notifications.length > 0 ? (
              <div>
                {notifications.map(notification => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200 ${
                      !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 relative h-10 w-10 rounded-full overflow-hidden">
                        <Image
                          src={notification.sender.avatar}
                          alt={notification.sender.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="ml-3 flex-1 min-w-0">
                        <div className="flex justify-between items-start">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {notification.sender.name}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-2">
                            {formatTimestamp(notification.timestamp)}
                          </p>
                        </div>
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-0.5">
                          {notification.orderName}
                        </p>
                        <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 line-clamp-2">
                          {notification.message}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                {t('notifications.noMessages')}
              </div>
            )}
          </div>
          
          <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-center">
            <button
              onClick={() => router.push('/orders')}
              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
            >
              {t('notifications.viewAllOrders')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
