'use client';

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useLanguage } from '../contexts/LanguageContext';

export type AlertType = 'success' | 'error' | 'warning' | 'info';

interface AlertProps {
  type: AlertType;
  message: string;
  duration?: number;
  onClose?: () => void;
}

export default function Alert({ type, message, duration = 5000, onClose }: AlertProps) {
  const { t } = useLanguage();
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onClose) onClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);
  
  const handleClose = () => {
    setIsVisible(false);
    if (onClose) onClose();
  };
  
  const getAlertStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/30 border-green-500 text-green-800 dark:text-green-300';
      case 'error':
        return 'bg-red-50 dark:bg-red-900/30 border-red-500 text-red-800 dark:text-red-300';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/30 border-yellow-500 text-yellow-800 dark:text-yellow-300';
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/30 border-blue-500 text-blue-800 dark:text-blue-300';
      default:
        return 'bg-gray-50 dark:bg-gray-900/30 border-gray-500 text-gray-800 dark:text-gray-300';
    }
  };
  
  const getIconByType = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="h-5 w-5 text-green-500 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="h-5 w-5 text-red-500 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="h-5 w-5 text-yellow-500 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="h-5 w-5 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };
  
  if (!isVisible) return null;
  
  // Use createPortal to render the alert at the top level of the DOM
  return createPortal(
    <div className="fixed inset-x-0 top-4 flex items-center justify-center z-50 px-4 pointer-events-none">
      <div className={`max-w-md w-full rounded-lg shadow-lg border-l-4 overflow-hidden pointer-events-auto transition-all duration-300 ${getAlertStyles()}`}>
        <div className="p-4 flex items-start">
          <div className="flex-shrink-0">
            {getIconByType()}
          </div>
          <div className="ml-3 w-0 flex-1 pt-0.5">
            <p className="text-sm font-medium">{message}</p>
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              onClick={handleClose}
              className="inline-flex text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <span className="sr-only">{t('general.close')}</span>
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
}

// Alert Manager for global alerts
export const AlertManager = {
  alerts: [] as { id: string; props: AlertProps }[],
  listeners: [] as ((alerts: { id: string; props: AlertProps }[]) => void)[],
  
  subscribe(listener: (alerts: { id: string; props: AlertProps }[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  },
  
  notify(props: Omit<AlertProps, 'onClose'>) {
    const id = Math.random().toString(36).substring(2, 9);
    const alert = {
      id,
      props: {
        ...props,
        onClose: () => this.remove(id)
      }
    };
    
    this.alerts.push(alert);
    this.emitChange();
    
    return id;
  },
  
  remove(id: string) {
    this.alerts = this.alerts.filter(alert => alert.id !== id);
    this.emitChange();
  },
  
  emitChange() {
    this.listeners.forEach(listener => listener([...this.alerts]));
  }
};

export function AlertContainer() {
  const [alerts, setAlerts] = useState<{ id: string; props: AlertProps }[]>([]);
  
  useEffect(() => {
    const unsubscribe = AlertManager.subscribe(setAlerts);
    return unsubscribe;
  }, []);
  
  return (
    <>
      {alerts.map(({ id, props }) => (
        <Alert key={id} {...props} />
      ))}
    </>
  );
}

// Helper functions for common alert types
export const showSuccessAlert = (message: string, duration = 5000) => 
  AlertManager.notify({ type: 'success', message, duration });

export const showErrorAlert = (message: string, duration = 5000) => 
  AlertManager.notify({ type: 'error', message, duration });

export const showWarningAlert = (message: string, duration = 5000) => 
  AlertManager.notify({ type: 'warning', message, duration });

export const showInfoAlert = (message: string, duration = 5000) => 
  AlertManager.notify({ type: 'info', message, duration });
