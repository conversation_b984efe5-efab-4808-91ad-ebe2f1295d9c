'use client';

import React from 'react';
import { cn } from '../../lib/utils';
import { useGlassmorphism } from '../../contexts/ThemeContext';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'subtle';
  interactive?: boolean;
  as?: keyof JSX.IntrinsicElements;
}

export function GlassCard({ 
  children, 
  className, 
  variant = 'default',
  interactive = false,
  as: Component = 'div'
}: GlassCardProps) {
  const { enabled } = useGlassmorphism();

  const baseClasses = cn(
    'rounded-lg border transition-all duration-300',
    {
      // Glassmorphism styles
      'glass-card': enabled,
      'backdrop-blur-md bg-white/80 border-white/20 shadow-lg': enabled,
      
      // Normal styles (fallback)
      'bg-white border-gray-200 shadow-sm': !enabled,
      
      // Variants for glassmorphism
      'shadow-xl backdrop-blur-lg bg-white/90': enabled && variant === 'elevated',
      'backdrop-blur-sm bg-white/60': enabled && variant === 'subtle',
      
      // Interactive states
      'hover:shadow-xl hover:bg-white/90 cursor-pointer': enabled && interactive,
      'hover:shadow-md hover:bg-gray-50': !enabled && interactive,
    },
    className
  );

  return (
    <Component className={baseClasses}>
      {children}
    </Component>
  );
}

// Glass Button Component
interface GlassButtonProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export function GlassButton({
  children,
  className,
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  type = 'button',
}: GlassButtonProps) {
  const { enabled } = useGlassmorphism();

  const baseClasses = cn(
    'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2',
    {
      // Size variants
      'px-3 py-1.5 text-sm': size === 'sm',
      'px-4 py-2 text-base': size === 'md',
      'px-6 py-3 text-lg': size === 'lg',
      
      // Glassmorphism styles
      'glass-button': enabled,
      'backdrop-blur-md border border-white/20': enabled,
      
      // Color variants for glassmorphism
      'bg-blue-500/20 text-blue-700 hover:bg-blue-500/30 focus:ring-blue-500': 
        enabled && variant === 'primary',
      'bg-gray-500/20 text-gray-700 hover:bg-gray-500/30 focus:ring-gray-500': 
        enabled && variant === 'secondary',
      'bg-yellow-500/20 text-yellow-700 hover:bg-yellow-500/30 focus:ring-yellow-500': 
        enabled && variant === 'accent',
      
      // Normal styles (fallback)
      'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500': 
        !enabled && variant === 'primary',
      'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500': 
        !enabled && variant === 'secondary',
      'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500': 
        !enabled && variant === 'accent',
      
      // Disabled state
      'opacity-50 cursor-not-allowed': disabled,
    },
    className
  );

  return (
    <button
      type={type}
      className={baseClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

// Glass Modal/Dialog Component
interface GlassModalProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  overlayClassName?: string;
}

export function GlassModal({
  children,
  isOpen,
  onClose,
  className,
  overlayClassName,
}: GlassModalProps) {
  const { enabled } = useGlassmorphism();

  if (!isOpen) return null;

  return (
    <div 
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center p-4',
        {
          'backdrop-blur-sm bg-black/20': enabled,
          'bg-black/50': !enabled,
        },
        overlayClassName
      )}
      onClick={onClose}
    >
      <div
        className={cn(
          'relative max-w-lg w-full max-h-[90vh] overflow-auto rounded-xl',
          {
            'glass-card backdrop-blur-xl bg-white/90 border-white/30 shadow-2xl': enabled,
            'bg-white border border-gray-200 shadow-xl': !enabled,
          },
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
}

// Glass Navigation Component
interface GlassNavProps {
  children: React.ReactNode;
  className?: string;
  fixed?: boolean;
}

export function GlassNav({ children, className, fixed = false }: GlassNavProps) {
  const { enabled } = useGlassmorphism();

  return (
    <nav
      className={cn(
        'w-full border-b transition-all duration-300',
        {
          'glass backdrop-blur-xl bg-white/80 border-white/20': enabled,
          'bg-white border-gray-200': !enabled,
          'fixed top-0 z-40': fixed,
        },
        className
      )}
    >
      {children}
    </nav>
  );
}

// Glass Sidebar Component
interface GlassSidebarProps {
  children: React.ReactNode;
  className?: string;
  isOpen?: boolean;
}

export function GlassSidebar({ children, className, isOpen = true }: GlassSidebarProps) {
  const { enabled } = useGlassmorphism();

  return (
    <aside
      className={cn(
        'h-full border-r transition-all duration-300',
        {
          'glass backdrop-blur-xl bg-white/80 border-white/20': enabled,
          'bg-white border-gray-200': !enabled,
          'translate-x-0': isOpen,
          '-translate-x-full': !isOpen,
        },
        className
      )}
    >
      {children}
    </aside>
  );
}

// Glass Input Component
interface GlassInputProps {
  className?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  disabled?: boolean;
}

export function GlassInput({
  className,
  placeholder,
  value,
  onChange,
  type = 'text',
  disabled = false,
}: GlassInputProps) {
  const { enabled } = useGlassmorphism();

  return (
    <input
      type={type}
      className={cn(
        'w-full rounded-lg px-4 py-2 transition-all duration-300 focus:outline-none focus:ring-2',
        {
          'backdrop-blur-md bg-white/60 border border-white/30 placeholder-gray-600 focus:bg-white/80 focus:ring-blue-500/50': enabled,
          'bg-white border border-gray-300 placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500': !enabled,
          'opacity-50 cursor-not-allowed': disabled,
        },
        className
      )}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
    />
  );
}
