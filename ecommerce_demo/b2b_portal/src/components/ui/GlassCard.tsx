'use client';

import React from 'react';
import { cn } from '../../lib/utils';
import { useGlassmorphism } from '../../contexts/ThemeContext';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'subtle';
  interactive?: boolean;
  as?: keyof JSX.IntrinsicElements;
}

export function GlassCard({
  children,
  className,
  variant = 'default',
  interactive = false,
  as: Component = 'div'
}: GlassCardProps) {
  const { enabled } = useGlassmorphism();

  const baseClasses = cn(
    'rounded-lg border transition-all duration-300',
    {
      // Glassmorphism styles
      'glass-card': enabled,

      // Normal styles (fallback)
      'bg-theme-background border-theme-border shadow-theme-sm': !enabled,
      'dark:bg-theme-background dark:border-theme-border': !enabled,

      // Variants for glassmorphism
      'shadow-theme-xl': enabled && variant === 'elevated',
      'shadow-theme-sm': enabled && variant === 'subtle',

      // Interactive states
      'hover:shadow-theme-xl cursor-pointer': enabled && interactive,
      'hover:shadow-theme-md hover:bg-theme-background-secondary': !enabled && interactive,
    },
    className
  );

  return (
    <Component className={baseClasses}>
      {children}
    </Component>
  );
}

// Glass Button Component
interface GlassButtonProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export function GlassButton({
  children,
  className,
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  type = 'button',
}: GlassButtonProps) {
  const { enabled } = useGlassmorphism();

  const baseClasses = cn(
    'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2',
    {
      // Size variants
      'px-3 py-1.5 text-sm': size === 'sm',
      'px-4 py-2 text-base': size === 'md',
      'px-6 py-3 text-lg': size === 'lg',

      // Glassmorphism styles
      'glass-button border': enabled,

      // Color variants for glassmorphism
      'text-primary hover:text-primary-hover focus:ring-primary/50':
        enabled && variant === 'primary',
      'text-secondary hover:text-theme-foreground focus:ring-secondary/50':
        enabled && variant === 'secondary',
      'text-accent hover:text-accent-hover focus:ring-accent/50':
        enabled && variant === 'accent',

      // Normal styles (fallback)
      'bg-primary text-white hover:bg-primary-hover focus:ring-primary border-primary':
        !enabled && variant === 'primary',
      'bg-secondary text-white hover:bg-secondary-hover focus:ring-secondary border-secondary':
        !enabled && variant === 'secondary',
      'bg-accent text-white hover:bg-accent-hover focus:ring-accent border-accent':
        !enabled && variant === 'accent',

      // Disabled state
      'opacity-50 cursor-not-allowed': disabled,
    },
    className
  );

  return (
    <button
      type={type}
      className={baseClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

// Glass Modal/Dialog Component
interface GlassModalProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  overlayClassName?: string;
}

export function GlassModal({
  children,
  isOpen,
  onClose,
  className,
  overlayClassName,
}: GlassModalProps) {
  const { enabled } = useGlassmorphism();

  if (!isOpen) return null;

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center p-4',
        {
          'glass-modal-overlay': enabled,
          'bg-black/50': !enabled,
        },
        overlayClassName
      )}
      onClick={onClose}
    >
      <div
        className={cn(
          'relative max-w-lg w-full max-h-[90vh] overflow-auto rounded-xl',
          {
            'glass-card shadow-theme-xl': enabled,
            'bg-theme-background border border-theme-border shadow-theme-xl': !enabled,
          },
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
}

// Glass Navigation Component
interface GlassNavProps {
  children: React.ReactNode;
  className?: string;
  fixed?: boolean;
}

export function GlassNav({ children, className, fixed = false }: GlassNavProps) {
  const { enabled } = useGlassmorphism();

  return (
    <nav
      className={cn(
        'w-full border-b transition-all duration-300',
        {
          'glass backdrop-blur-xl bg-white/80 border-white/20': enabled,
          'bg-white border-gray-200': !enabled,
          'fixed top-0 z-40': fixed,
        },
        className
      )}
    >
      {children}
    </nav>
  );
}

// Glass Sidebar Component
interface GlassSidebarProps {
  children: React.ReactNode;
  className?: string;
  isOpen?: boolean;
}

export function GlassSidebar({ children, className, isOpen = true }: GlassSidebarProps) {
  const { enabled } = useGlassmorphism();

  return (
    <aside
      className={cn(
        'h-full border-r transition-all duration-300',
        {
          'glass backdrop-blur-xl bg-white/80 border-white/20': enabled,
          'bg-white border-gray-200': !enabled,
          'translate-x-0': isOpen,
          '-translate-x-full': !isOpen,
        },
        className
      )}
    >
      {children}
    </aside>
  );
}

// Glass Input Component
interface GlassInputProps {
  className?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  disabled?: boolean;
}

export function GlassInput({
  className,
  placeholder,
  value,
  onChange,
  type = 'text',
  disabled = false,
}: GlassInputProps) {
  const { enabled } = useGlassmorphism();

  return (
    <input
      type={type}
      className={cn(
        'w-full rounded-lg px-4 py-2 transition-all duration-300 focus:outline-none focus:ring-2',
        {
          'glass-input text-theme-foreground placeholder-theme-foreground-muted': enabled,
          'bg-theme-background border border-theme-border text-theme-foreground placeholder-theme-foreground-muted focus:border-primary focus:ring-primary/20': !enabled,
          'opacity-50 cursor-not-allowed': disabled,
        },
        className
      )}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
    />
  );
}
