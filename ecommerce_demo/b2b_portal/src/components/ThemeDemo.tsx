'use client';

import { useState } from 'react';
import { useTheme, useThemeColors, useGlassmorphism } from '../contexts/ThemeContext';
import { GlassCard, GlassButton, GlassModal, GlassInput } from './ui/GlassCard';
import ThemeToggle from './ThemeToggle';
import { cn } from '../lib/utils';

export default function ThemeDemo() {
  const { mode, style, customerTheme, setStyle } = useTheme();
  const colors = useThemeColors();
  const { enabled: isGlass } = useGlassmorphism();
  const [showModal, setShowModal] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectedTab, setSelectedTab] = useState('overview');
  const [notifications, setNotifications] = useState([
    { id: 1, title: 'New Order', message: 'Order #1234 has been placed', type: 'info', time: '2 min ago' },
    { id: 2, title: 'Payment Received', message: 'Payment for order #1233 confirmed', type: 'success', time: '5 min ago' },
    { id: 3, title: 'Low Stock Alert', message: 'Product XYZ is running low', type: 'warning', time: '10 min ago' },
  ]);
  const [progress, setProgress] = useState(65);

  return (
    <div className="min-h-screen transition-all duration-300 bg-theme-background"
         style={{
           backgroundImage: `linear-gradient(135deg, ${colors.primary}15, ${colors.accent}15)`,
           backgroundAttachment: 'fixed'
         }}>

      {/* Header */}
      <div className="max-w-7xl mx-auto p-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-theme-foreground mb-2">
              {customerTheme.themeName} Showcase
            </h1>
            <p className="text-theme-foreground-muted">
              Comprehensive theme system demo with {isGlass ? 'glassmorphism' : 'normal'} style • {mode} mode
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <ThemeToggle variant="switch" showLabel />
            <ThemeToggle variant="dropdown" showLabel />
          </div>
        </div>

        {/* Navigation Tabs */}
        <GlassCard className="p-1 mb-8">
          <div className="flex space-x-1">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'components', label: 'Components' },
              { id: 'forms', label: 'Forms' },
              { id: 'navigation', label: 'Navigation' },
              { id: 'data', label: 'Data Display' },
              { id: 'feedback', label: 'Feedback' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={cn(
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all',
                  selectedTab === tab.id
                    ? 'bg-primary text-white shadow-md'
                    : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-background-secondary'
                )}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </GlassCard>

        {/* Overview Tab */}
        {selectedTab === 'overview' && (
          <>
            {/* Theme Info Card */}
            <GlassCard className="p-6 mb-8">
              <h2 className="text-2xl font-semibold text-theme-foreground mb-4">
                Current Theme Configuration
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="font-medium text-theme-foreground mb-2">Theme Details</h3>
                  <ul className="space-y-1 text-sm text-theme-foreground-muted">
                    <li><strong>Customer:</strong> {customerTheme.customerId}</li>
                    <li><strong>Mode:</strong> {mode}</li>
                    <li><strong>Style:</strong> {style}</li>
                    <li><strong>Glassmorphism:</strong> {isGlass ? 'Enabled' : 'Disabled'}</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium text-theme-foreground mb-2">Color Palette</h3>
                  <div className="grid grid-cols-4 gap-2">
                    <div
                      className="w-8 h-8 rounded border-2 border-theme-border"
                      style={{ backgroundColor: colors.primary }}
                      title="Primary"
                    />
                    <div
                      className="w-8 h-8 rounded border-2 border-theme-border"
                      style={{ backgroundColor: colors.secondary }}
                      title="Secondary"
                    />
                    <div
                      className="w-8 h-8 rounded border-2 border-theme-border"
                      style={{ backgroundColor: colors.accent }}
                      title="Accent"
                    />
                    <div
                      className="w-8 h-8 rounded border-2 border-theme-border"
                      style={{ backgroundColor: colors.success }}
                      title="Success"
                    />
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-theme-foreground mb-2">Typography</h3>
                  <div className="text-sm text-theme-foreground-muted">
                    <p style={{ fontFamily: customerTheme.typography.fontPrimary }}>
                      Primary Font
                    </p>
                    <p style={{ fontFamily: customerTheme.typography.fontSecondary }}>
                      Secondary Font
                    </p>
                    <p style={{ fontFamily: customerTheme.typography.fontArabic }}>
                      Arabic Font - خط عربي
                    </p>
                  </div>
                </div>
              </div>
            </GlassCard>

            {/* Style Switcher */}
            <GlassCard className="p-6 mb-8">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Style Switcher
              </h3>

              <div className="flex gap-4">
                <button
                  onClick={() => setStyle('normal')}
                  className={cn(
                    'px-4 py-2 rounded-lg border transition-all',
                    style === 'normal'
                      ? 'bg-primary text-white border-primary'
                      : 'bg-theme-background-secondary text-theme-foreground border-theme-border hover:border-primary'
                  )}
                >
                  Normal Theme
                </button>

                <button
                  onClick={() => setStyle('glassmorphism')}
                  className={cn(
                    'px-4 py-2 rounded-lg border transition-all',
                    style === 'glassmorphism'
                      ? 'bg-primary text-white border-primary'
                      : 'bg-theme-background-secondary text-theme-foreground border-theme-border hover:border-primary'
                  )}
                >
                  Glassmorphism
                </button>
              </div>

              <p className="text-sm text-theme-foreground-muted mt-3">
                Switch between normal and glassmorphism styles to see the difference.
                {!isGlass && style === 'glassmorphism' && (
                  <span className="text-warning">
                    {' '}(Glassmorphism disabled for this theme)
                  </span>
                )}
              </p>
            </GlassCard>
          </>
        )}

        {/* Components Tab */}
        {selectedTab === 'components' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

            {/* Buttons Demo */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Button Components
              </h3>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-theme-foreground-muted mb-2">Sizes</h4>
                  <div className="flex flex-wrap gap-3">
                    <GlassButton variant="primary" size="sm">
                      Small
                    </GlassButton>
                    <GlassButton variant="primary" size="md">
                      Medium
                    </GlassButton>
                    <GlassButton variant="primary" size="lg">
                      Large
                    </GlassButton>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-theme-foreground-muted mb-2">Variants</h4>
                  <div className="flex flex-wrap gap-3">
                    <GlassButton variant="primary">
                      Primary
                    </GlassButton>
                    <GlassButton variant="secondary">
                      Secondary
                    </GlassButton>
                    <GlassButton variant="accent">
                      Accent
                    </GlassButton>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-theme-foreground-muted mb-2">States</h4>
                  <div className="flex flex-wrap gap-3">
                    <GlassButton variant="primary">
                      Normal
                    </GlassButton>
                    <GlassButton variant="primary" disabled>
                      Disabled
                    </GlassButton>
                  </div>
                </div>
              </div>
            </GlassCard>

            {/* Cards Demo */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Card Components
              </h3>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-theme-foreground-muted mb-2">Default Card</h4>
                  <GlassCard className="p-4">
                    <p className="text-theme-foreground">This is a default glass card with some content.</p>
                  </GlassCard>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-theme-foreground-muted mb-2">Elevated Card</h4>
                  <GlassCard variant="elevated" className="p-4">
                    <p className="text-theme-foreground">This is an elevated card with enhanced shadow.</p>
                  </GlassCard>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-theme-foreground-muted mb-2">Interactive Card</h4>
                  <GlassCard interactive className="p-4">
                    <p className="text-theme-foreground">This card responds to hover interactions.</p>
                  </GlassCard>
                </div>
              </div>
            </GlassCard>

            {/* Status Indicators */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Status Indicators
              </h3>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-success"></div>
                  <span className="text-theme-foreground">Success Status</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-warning"></div>
                  <span className="text-theme-foreground">Warning Status</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-error"></div>
                  <span className="text-theme-foreground">Error Status</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-info"></div>
                  <span className="text-theme-foreground">Info Status</span>
                </div>
              </div>
            </GlassCard>

            {/* Progress Bars */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Progress Indicators
              </h3>

              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm text-theme-foreground-muted mb-1">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-theme-background-secondary rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm text-theme-foreground-muted mb-1">
                    <span>Success</span>
                    <span>85%</span>
                  </div>
                  <div className="w-full bg-theme-background-secondary rounded-full h-2">
                    <div className="bg-success h-2 rounded-full w-[85%]"></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm text-theme-foreground-muted mb-1">
                    <span>Warning</span>
                    <span>45%</span>
                  </div>
                  <div className="w-full bg-theme-background-secondary rounded-full h-2">
                    <div className="bg-warning h-2 rounded-full w-[45%]"></div>
                  </div>
                </div>

                <div className="flex gap-2 mt-4">
                  <GlassButton
                    size="sm"
                    variant="secondary"
                    onClick={() => setProgress(Math.max(0, progress - 10))}
                  >
                    -10%
                  </GlassButton>
                  <GlassButton
                    size="sm"
                    variant="secondary"
                    onClick={() => setProgress(Math.min(100, progress + 10))}
                  >
                    +10%
                  </GlassButton>
                </div>
              </div>
            </GlassCard>
          </div>
        )}

        {/* Style Switcher */}
        <GlassCard className="p-6">
          <h3 className="text-xl font-semibold text-theme-foreground mb-4">
            Style Switcher
          </h3>
          
          <div className="flex gap-4">
            <button
              onClick={() => setStyle('normal')}
              className={cn(
                'px-4 py-2 rounded-lg border transition-all',
                style === 'normal'
                  ? 'bg-primary text-white border-primary'
                  : 'bg-theme-background-secondary text-theme-foreground border-theme-border hover:border-primary'
              )}
            >
              Normal Theme
            </button>
            
            <button
              onClick={() => setStyle('glassmorphism')}
              className={cn(
                'px-4 py-2 rounded-lg border transition-all',
                style === 'glassmorphism'
                  ? 'bg-primary text-white border-primary'
                  : 'bg-theme-background-secondary text-theme-foreground border-theme-border hover:border-primary'
              )}
            >
              Glassmorphism
            </button>
          </div>
          
          <p className="text-sm text-theme-foreground-muted mt-3">
            Switch between normal and glassmorphism styles to see the difference.
            {!isGlass && style === 'glassmorphism' && (
              <span className="text-theme-warning"> 
                {' '}(Glassmorphism disabled for this theme)
              </span>
            )}
          </p>
        </GlassCard>

        {/* Forms Tab */}
        {selectedTab === 'forms' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

            {/* Basic Form */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Basic Form Elements
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-theme-foreground mb-1">
                    Full Name
                  </label>
                  <GlassInput
                    placeholder="Enter your full name"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-theme-foreground mb-1">
                    Email Address
                  </label>
                  <GlassInput
                    placeholder="<EMAIL>"
                    type="email"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-theme-foreground mb-1">
                    Password
                  </label>
                  <GlassInput
                    placeholder="Enter your password"
                    type="password"
                  />
                </div>

                <div className="flex gap-3 pt-2">
                  <GlassButton variant="primary" className="flex-1">
                    Submit Form
                  </GlassButton>
                  <GlassButton variant="secondary">
                    Cancel
                  </GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Modal Demo */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Modal & Overlay
              </h3>

              <div className="space-y-4">
                <p className="text-theme-foreground-muted">
                  Test the glassmorphism modal overlay effect.
                </p>

                <GlassButton
                  variant="primary"
                  onClick={() => setShowModal(true)}
                  className="w-full"
                >
                  Open Modal Demo
                </GlassButton>
              </div>
            </GlassCard>
          </div>
        )}

        {/* Data Tab */}
        {selectedTab === 'data' && (
          <div className="space-y-8">

            {/* Data Table */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Data Display
              </h3>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-theme-border">
                      <th className="text-left py-3 px-4 text-theme-foreground font-medium">Product</th>
                      <th className="text-left py-3 px-4 text-theme-foreground font-medium">Category</th>
                      <th className="text-left py-3 px-4 text-theme-foreground font-medium">Price</th>
                      <th className="text-left py-3 px-4 text-theme-foreground font-medium">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { name: 'MacBook Pro', category: 'Laptops', price: '$2,499', status: 'In Stock' },
                      { name: 'iPhone 15', category: 'Phones', price: '$999', status: 'Low Stock' },
                      { name: 'iPad Air', category: 'Tablets', price: '$599', status: 'Out of Stock' },
                    ].map((item, index) => (
                      <tr key={index} className="border-b border-theme-border/50 hover:bg-theme-background-secondary/50">
                        <td className="py-3 px-4 text-theme-foreground">{item.name}</td>
                        <td className="py-3 px-4 text-theme-foreground-muted">{item.category}</td>
                        <td className="py-3 px-4 text-theme-foreground font-medium">{item.price}</td>
                        <td className="py-3 px-4">
                          <span className={cn(
                            'px-2 py-1 rounded-full text-xs font-medium',
                            item.status === 'In Stock' && 'bg-success/20 text-success',
                            item.status === 'Low Stock' && 'bg-warning/20 text-warning',
                            item.status === 'Out of Stock' && 'bg-error/20 text-error'
                          )}>
                            {item.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </GlassCard>
          </div>
        )}

        {/* Feedback Tab */}
        {selectedTab === 'feedback' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

            {/* Notifications */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Notifications
              </h3>

              <div className="space-y-3">
                {notifications.map((notification) => (
                  <div key={notification.id} className={cn(
                    'p-4 rounded-lg border-l-4 transition-all',
                    {
                      'glass border-l-info': isGlass && notification.type === 'info',
                      'glass border-l-success': isGlass && notification.type === 'success',
                      'glass border-l-warning': isGlass && notification.type === 'warning',
                      'bg-info/10 border-l-info': !isGlass && notification.type === 'info',
                      'bg-success/10 border-l-success': !isGlass && notification.type === 'success',
                      'bg-warning/10 border-l-warning': !isGlass && notification.type === 'warning',
                    }
                  )}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-theme-foreground">{notification.title}</h4>
                        <p className="text-sm text-theme-foreground-muted mt-1">{notification.message}</p>
                      </div>
                      <span className="text-xs text-theme-foreground-muted">{notification.time}</span>
                    </div>
                  </div>
                ))}
              </div>
            </GlassCard>

            {/* Alerts */}
            <GlassCard className="p-6">
              <h3 className="text-xl font-semibold text-theme-foreground mb-4">
                Alert Messages
              </h3>

              <div className="space-y-4">
                <div className={cn(
                  'p-4 rounded-lg border',
                  {
                    'glass border-success/30': isGlass,
                    'bg-success/10 border-success/30': !isGlass,
                  }
                )}>
                  <div className="flex items-center">
                    <div className="w-5 h-5 rounded-full bg-success mr-3"></div>
                    <div>
                      <h4 className="font-medium text-theme-foreground">Success</h4>
                      <p className="text-sm text-theme-foreground-muted">Your changes have been saved successfully.</p>
                    </div>
                  </div>
                </div>

                <div className={cn(
                  'p-4 rounded-lg border',
                  {
                    'glass border-warning/30': isGlass,
                    'bg-warning/10 border-warning/30': !isGlass,
                  }
                )}>
                  <div className="flex items-center">
                    <div className="w-5 h-5 rounded-full bg-warning mr-3"></div>
                    <div>
                      <h4 className="font-medium text-theme-foreground">Warning</h4>
                      <p className="text-sm text-theme-foreground-muted">Please review your information before proceeding.</p>
                    </div>
                  </div>
                </div>

                <div className={cn(
                  'p-4 rounded-lg border',
                  {
                    'glass border-error/30': isGlass,
                    'bg-error/10 border-error/30': !isGlass,
                  }
                )}>
                  <div className="flex items-center">
                    <div className="w-5 h-5 rounded-full bg-error mr-3"></div>
                    <div>
                      <h4 className="font-medium text-theme-foreground">Error</h4>
                      <p className="text-sm text-theme-foreground-muted">An error occurred while processing your request.</p>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        )}

        {/* CSS Variables Display */}
        <GlassCard className="p-6 mt-8">
          <h3 className="text-xl font-semibold text-theme-foreground mb-4">
            CSS Variables (Live)
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm font-mono">
            <div>
              <h4 className="font-semibold text-theme-foreground mb-2">Colors</h4>
              <div className="space-y-1 text-theme-foreground-muted">
                <div>--color-primary: <span style={{ color: colors.primary }}>{colors.primary}</span></div>
                <div>--color-secondary: <span style={{ color: colors.secondary }}>{colors.secondary}</span></div>
                <div>--color-accent: <span style={{ color: colors.accent }}>{colors.accent}</span></div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-theme-foreground mb-2">Background</h4>
              <div className="space-y-1 text-theme-foreground-muted">
                <div>--color-background: {colors.background}</div>
                <div>--color-foreground: {colors.foreground}</div>
                <div>--color-border: {colors.border}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-theme-foreground mb-2">Status</h4>
              <div className="space-y-1 text-theme-foreground-muted">
                <div>--color-success: <span style={{ color: colors.success }}>{colors.success}</span></div>
                <div>--color-warning: <span style={{ color: colors.warning }}>{colors.warning}</span></div>
                <div>--color-error: <span style={{ color: colors.error }}>{colors.error}</span></div>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Modal Demo */}
      <GlassModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      >
        <div className="p-6">
          <h2 className="text-2xl font-semibold text-theme-foreground mb-4">
            Glassmorphism Modal
          </h2>
          
          <p className="text-theme-foreground-muted mb-6">
            This modal demonstrates the glassmorphism effect with backdrop blur and 
            semi-transparent background. The effect automatically adapts based on 
            the current theme settings.
          </p>
          
          <div className="flex justify-end space-x-3">
            <GlassButton 
              variant="secondary" 
              onClick={() => setShowModal(false)}
            >
              Cancel
            </GlassButton>
            <GlassButton 
              variant="primary" 
              onClick={() => setShowModal(false)}
            >
              Confirm
            </GlassButton>
          </div>
        </div>
      </GlassModal>
    </div>
  );
}
