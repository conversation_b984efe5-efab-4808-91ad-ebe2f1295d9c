'use client';

import { useState } from 'react';
import { useTheme, useThemeColors, useGlassmorphism } from '../contexts/ThemeContext';
import { GlassCard, GlassButton, GlassModal, GlassInput } from './ui/GlassCard';
import ThemeToggle from './ThemeToggle';
import { cn } from '../lib/utils';

export default function ThemeDemo() {
  const { mode, style, customerTheme, setStyle } = useTheme();
  const colors = useThemeColors();
  const { enabled: isGlass } = useGlassmorphism();
  const [showModal, setShowModal] = useState(false);
  const [inputValue, setInputValue] = useState('');

  return (
    <div className="min-h-screen p-8 transition-all duration-300" 
         style={{ background: `linear-gradient(135deg, ${colors.primary}20, ${colors.accent}20)` }}>
      
      {/* Header */}
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-theme-foreground mb-2">
              {customerTheme.themeName} Demo
            </h1>
            <p className="text-theme-foreground-muted">
              Dynamic theme system with {isGlass ? 'glassmorphism' : 'normal'} style
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <ThemeToggle variant="switch" showLabel />
            <ThemeToggle variant="dropdown" showLabel />
          </div>
        </div>

        {/* Theme Info Card */}
        <GlassCard className="p-6 mb-8">
          <h2 className="text-2xl font-semibold text-theme-foreground mb-4">
            Current Theme Configuration
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium text-theme-foreground mb-2">Theme Details</h3>
              <ul className="space-y-1 text-sm text-theme-foreground-muted">
                <li><strong>Customer:</strong> {customerTheme.customerId}</li>
                <li><strong>Mode:</strong> {mode}</li>
                <li><strong>Style:</strong> {style}</li>
                <li><strong>Glassmorphism:</strong> {isGlass ? 'Enabled' : 'Disabled'}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-theme-foreground mb-2">Color Palette</h3>
              <div className="grid grid-cols-4 gap-2">
                <div 
                  className="w-8 h-8 rounded border-2 border-white/20"
                  style={{ backgroundColor: colors.primary }}
                  title="Primary"
                />
                <div 
                  className="w-8 h-8 rounded border-2 border-white/20"
                  style={{ backgroundColor: colors.secondary }}
                  title="Secondary"
                />
                <div 
                  className="w-8 h-8 rounded border-2 border-white/20"
                  style={{ backgroundColor: colors.accent }}
                  title="Accent"
                />
                <div 
                  className="w-8 h-8 rounded border-2 border-white/20"
                  style={{ backgroundColor: colors.success }}
                  title="Success"
                />
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-theme-foreground mb-2">Typography</h3>
              <div className="text-sm text-theme-foreground-muted">
                <p style={{ fontFamily: customerTheme.typography.fontPrimary }}>
                  Primary Font
                </p>
                <p style={{ fontFamily: customerTheme.typography.fontSecondary }}>
                  Secondary Font
                </p>
                <p style={{ fontFamily: customerTheme.typography.fontArabic }}>
                  Arabic Font - خط عربي
                </p>
              </div>
            </div>
          </div>
        </GlassCard>

        {/* Component Showcase */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          
          {/* Buttons Demo */}
          <GlassCard className="p-6">
            <h3 className="text-xl font-semibold text-theme-foreground mb-4">
              Button Components
            </h3>
            
            <div className="space-y-4">
              <div className="flex flex-wrap gap-3">
                <GlassButton variant="primary" size="sm">
                  Primary Small
                </GlassButton>
                <GlassButton variant="primary" size="md">
                  Primary Medium
                </GlassButton>
                <GlassButton variant="primary" size="lg">
                  Primary Large
                </GlassButton>
              </div>
              
              <div className="flex flex-wrap gap-3">
                <GlassButton variant="secondary">
                  Secondary
                </GlassButton>
                <GlassButton variant="accent">
                  Accent
                </GlassButton>
                <GlassButton disabled>
                  Disabled
                </GlassButton>
              </div>
            </div>
          </GlassCard>

          {/* Form Demo */}
          <GlassCard className="p-6">
            <h3 className="text-xl font-semibold text-theme-foreground mb-4">
              Form Components
            </h3>
            
            <div className="space-y-4">
              <GlassInput
                placeholder="Enter your name"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
              />
              
              <GlassInput
                placeholder="Email address"
                type="email"
              />
              
              <GlassInput
                placeholder="Disabled input"
                disabled
              />
              
              <GlassButton 
                variant="primary" 
                onClick={() => setShowModal(true)}
                className="w-full"
              >
                Open Modal Demo
              </GlassButton>
            </div>
          </GlassCard>
        </div>

        {/* Style Switcher */}
        <GlassCard className="p-6">
          <h3 className="text-xl font-semibold text-theme-foreground mb-4">
            Style Switcher
          </h3>
          
          <div className="flex gap-4">
            <button
              onClick={() => setStyle('normal')}
              className={cn(
                'px-4 py-2 rounded-lg border transition-all',
                style === 'normal'
                  ? 'bg-primary text-white border-primary'
                  : 'bg-theme-background-secondary text-theme-foreground border-theme-border hover:border-primary'
              )}
            >
              Normal Theme
            </button>
            
            <button
              onClick={() => setStyle('glassmorphism')}
              className={cn(
                'px-4 py-2 rounded-lg border transition-all',
                style === 'glassmorphism'
                  ? 'bg-primary text-white border-primary'
                  : 'bg-theme-background-secondary text-theme-foreground border-theme-border hover:border-primary'
              )}
            >
              Glassmorphism
            </button>
          </div>
          
          <p className="text-sm text-theme-foreground-muted mt-3">
            Switch between normal and glassmorphism styles to see the difference.
            {!isGlass && style === 'glassmorphism' && (
              <span className="text-theme-warning"> 
                {' '}(Glassmorphism disabled for this theme)
              </span>
            )}
          </p>
        </GlassCard>

        {/* CSS Variables Display */}
        <GlassCard className="p-6 mt-8">
          <h3 className="text-xl font-semibold text-theme-foreground mb-4">
            CSS Variables (Live)
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm font-mono">
            <div>
              <h4 className="font-semibold text-theme-foreground mb-2">Colors</h4>
              <div className="space-y-1 text-theme-foreground-muted">
                <div>--color-primary: <span style={{ color: colors.primary }}>{colors.primary}</span></div>
                <div>--color-secondary: <span style={{ color: colors.secondary }}>{colors.secondary}</span></div>
                <div>--color-accent: <span style={{ color: colors.accent }}>{colors.accent}</span></div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-theme-foreground mb-2">Background</h4>
              <div className="space-y-1 text-theme-foreground-muted">
                <div>--color-background: {colors.background}</div>
                <div>--color-foreground: {colors.foreground}</div>
                <div>--color-border: {colors.border}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-theme-foreground mb-2">Status</h4>
              <div className="space-y-1 text-theme-foreground-muted">
                <div>--color-success: <span style={{ color: colors.success }}>{colors.success}</span></div>
                <div>--color-warning: <span style={{ color: colors.warning }}>{colors.warning}</span></div>
                <div>--color-error: <span style={{ color: colors.error }}>{colors.error}</span></div>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Modal Demo */}
      <GlassModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      >
        <div className="p-6">
          <h2 className="text-2xl font-semibold text-theme-foreground mb-4">
            Glassmorphism Modal
          </h2>
          
          <p className="text-theme-foreground-muted mb-6">
            This modal demonstrates the glassmorphism effect with backdrop blur and 
            semi-transparent background. The effect automatically adapts based on 
            the current theme settings.
          </p>
          
          <div className="flex justify-end space-x-3">
            <GlassButton 
              variant="secondary" 
              onClick={() => setShowModal(false)}
            >
              Cancel
            </GlassButton>
            <GlassButton 
              variant="primary" 
              onClick={() => setShowModal(false)}
            >
              Confirm
            </GlassButton>
          </div>
        </div>
      </GlassModal>
    </div>
  );
}
