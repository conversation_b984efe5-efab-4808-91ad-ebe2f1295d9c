'use client';

import { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Product } from '../types';
import { addItemToOrder, createOrder, updateOrderName } from '../lib/redux/slices/orders';
import { addToWishlist, removeFromWishlist } from '../lib/redux/slices/wishlist';
import { RootState } from '../lib/redux/store';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useLanguage } from '../contexts/LanguageContext';

interface ProductCardProps {
  product: Product;
}

export default function ProductCard({ product }: ProductCardProps) {
  const dispatch = useDispatch();
  const router = useRouter();
  const { t } = useLanguage();

  const [quantity, setQuantity] = useState(1);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [newOrderName, setNewOrderName] = useState('');
  const [editingOrderId, setEditingOrderId] = useState<string | null>(null);
  const [editOrderName, setEditOrderName] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { orders, activeOrderId } = useSelector((state: RootState) => state.orders);
  const { items: wishlistItems } = useSelector((state: RootState) => state.wishlist);
  const isInWishlist = wishlistItems.some(item => Number(item.id) === Number(product.id));

  // Get draft orders for the dropdown
  const draftOrders = orders.filter(order => order.state === 'draft');

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setIsCreatingOrder(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleAddToBasket = (orderId?: string) => {
    // If no orderId is provided, use the active order
    const targetOrderId = orderId || activeOrderId;
    if (!targetOrderId) {
      alert('Please select or create an order first');
      return;
    }

    const item = {
      productId: product.id,
      quantity,
      selectedVariantId: product.variants.length > 0 ? product.variants[0].id : undefined,
      selectedServiceOptions: [],
    };

    dispatch(addItemToOrder({
      orderId: targetOrderId,
      item,
    }));

    // Reset quantity
    setQuantity(1);

    // Show success message
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 2000);

    // Close dropdown
    setIsDropdownOpen(false);
  };

  const handleCreateOrder = () => {
    if (!newOrderName.trim()) return;

    dispatch(createOrder({ name: newOrderName.trim() }));
    setNewOrderName('');
    setIsCreatingOrder(false);

    // Get the newly created order ID (it will be the last one in the list)
    setTimeout(() => {
      const newOrders = orders.filter(order => order.state === 'draft');
      if (newOrders.length > 0) {
        const newOrderId = newOrders[newOrders.length - 1].id;
        handleAddToBasket(newOrderId);
      }
    }, 100);
  };

  const handleEditOrder = (orderId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const order = orders.find(o => o.id === orderId);
    if (order) {
      setEditingOrderId(orderId);
      setEditOrderName(order.name);
    }
  };

  const handleSaveOrderName = (e?: React.MouseEvent | React.KeyboardEvent) => {
    if (e) e.stopPropagation();
    if (editingOrderId && editOrderName.trim()) {
      dispatch(updateOrderName({ orderId: editingOrderId, name: editOrderName.trim() }));
      setEditingOrderId(null);
    }
  };

  const handleCancelEdit = (e?: React.MouseEvent | React.KeyboardEvent) => {
    if (e) e.stopPropagation();
    setEditingOrderId(null);
  };

  const handleProductClick = () => {
    router.push(`/products/${product.id}`);
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer border border-gray-100 dark:border-gray-700"
      onClick={handleProductClick}
    >
      <div className="relative h-40 w-full bg-gray-50 dark:bg-gray-700 transition-colors duration-200">
        {product.image && (
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-contain p-2"
          />
        )}

        {/* Wishlist button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            if (isInWishlist) {
              dispatch(removeFromWishlist(String(product.id)));
            } else {
              dispatch(addToWishlist(product));
            }
          }}
          className={`absolute top-2 right-2 p-2 rounded-full shadow-sm transition-colors duration-200 ${isInWishlist ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50' : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'}`}
          title={isInWishlist ? t('wishlist.removeFromWishlist') : t('wishlist.addToWishlist')}
          aria-label={isInWishlist ? t('wishlist.removeFromWishlist') : t('wishlist.addToWishlist')}
        >
          <svg className="h-4 w-4" viewBox="0 0 24 24" fill={isInWishlist ? 'currentColor' : 'none'} stroke="currentColor" strokeWidth={isInWishlist ? '0' : '2'}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>
      </div>

      <div className="p-4" onClick={(e) => e.stopPropagation()}>
        <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100 line-clamp-2 h-12 transition-colors duration-200">{product.name}</h3>

        <div className="flex flex-col mt-1">
          <div className="flex justify-between items-center">
            <p className="text-sm text-blue-600 dark:text-blue-400 transition-colors duration-200">
              {product.brand}
            </p>
            <span className="text-xs text-gray-500 dark:text-gray-400 transition-colors duration-200">SKU: {product.sku}</span>
          </div>
        </div>

        <div className="text-gray-700 dark:text-gray-300 mb-2 transition-colors duration-200 mt-2">
          {product.discountPercentage ? (
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-lg font-bold text-green-600 dark:text-green-400 transition-colors duration-200">
                ${product.discountedPrice?.toFixed(2)}
              </span>
              <span className="text-sm line-through text-gray-500 dark:text-gray-400 transition-colors duration-200">
                ${product.price.toFixed(2)}
              </span>
              <span className="text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300 px-1.5 py-0.5 rounded">
                -{product.discountPercentage}%
              </span>
            </div>
          ) : (
            <span className="text-lg font-bold text-blue-600 dark:text-blue-400 transition-colors duration-200">
              ${product.price.toFixed(2)}
            </span>
          )}
        </div>

        <div className="mt-2 flex flex-wrap gap-1">
          {product.categories.map(category => (
            <span
              key={category}
              className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-xs px-2 py-1 rounded transition-colors duration-200"
            >
              {category}
            </span>
          ))}
        </div>

        <div className="mt-2">
          <div className="flex items-center justify-between">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 transition-colors duration-200">{t('products.quantity')}:</label>
            <div className="flex rounded-md shadow-sm">
              <button
                type="button"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="relative inline-flex items-center px-1.5 py-0.5 rounded-l-md border border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100"
              >
                <span className="sr-only">Decrease</span>
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              <input
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                className="block w-12 min-w-0 text-center border-y border-blue-300 bg-white text-gray-700 text-sm"
              />
              <button
                type="button"
                onClick={() => setQuantity(quantity + 1)}
                className="relative inline-flex items-center px-1.5 py-0.5 rounded-r-md border border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100"
              >
                <span className="sr-only">Increase</span>
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="flex gap-2 mt-2">
          {/* Order Selection Dropdown */}
          <div className="relative flex-grow" ref={dropdownRef}>
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              disabled={draftOrders.length === 0 && !activeOrderId}
              className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white py-1.5 px-3 rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed text-sm flex justify-between items-center transition-colors duration-200"
            >
              <div className="flex items-center overflow-hidden max-w-[calc(100%-20px)]">
                <span className="truncate">
                  {activeOrderId ? (
                    <>
                      {t('products.addToOrder')}:{' '}
                      <span className="font-medium truncate max-w-[120px] inline-block align-bottom">
                        {orders.find(o => o.id === activeOrderId)?.name || 'Order'}
                      </span>
                    </>
                  ) : (
                    t('products.addToOrder')
                  )}
                </span>
              </div>
              <svg className={`ml-1 h-4 w-4 flex-shrink-0 transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {isDropdownOpen && (
              <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                {/* Quick add to active order */}
                {activeOrderId && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToBasket(activeOrderId);
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 font-medium border-b border-gray-100 dark:border-gray-700 flex justify-between items-center transition-colors duration-200"
                  >
                    <span className="flex items-center overflow-hidden">
                      <span className="truncate">{t('orders.addToActiveOrder')}: </span>
                      <span className="font-medium truncate max-w-[100px] inline-block ml-1">{orders.find(o => o.id === activeOrderId)?.name || 'active order'}</span>
                    </span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Active</span>
                  </button>
                )}

                {/* Draft orders list */}
                {draftOrders.length > 0 ? (
                  <div className="max-h-40 overflow-y-auto">
                    {draftOrders.map(order => (
                      <div key={order.id}>
                        {editingOrderId === order.id ? (
                          <div className="p-2 border-b border-gray-100 dark:border-gray-700">
                            <div className="flex items-center space-x-1">
                              <input
                                type="text"
                                value={editOrderName}
                                onChange={(e) => setEditOrderName(e.target.value)}
                                onClick={(e) => e.stopPropagation()}
                                className="flex-1 text-sm border border-blue-300 dark:border-blue-600 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-medium placeholder-gray-700 dark:placeholder-gray-300"
                                onKeyDown={(e) => {
                                  e.stopPropagation();
                                  if (e.key === 'Enter') handleSaveOrderName(e);
                                  if (e.key === 'Escape') handleCancelEdit(e);
                                }}
                              />
                              <button
                                onClick={handleSaveOrderName}
                                className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 p-1"
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              </button>
                              <button
                                onClick={handleCancelEdit}
                                className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 p-1"
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddToBasket(order.id);
                            }}
                            className={`w-full text-left px-3 py-2 text-sm ${activeOrderId === order.id ? 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'} transition-colors duration-200`}
                          >
                            <div className="flex justify-between items-center">
                              <div className="flex items-center space-x-1 flex-1 min-w-0">
                                <span className="font-medium truncate max-w-[120px] inline-block">{order.name}</span>
                                {order.state === 'draft' && (
                                  <div
                                    onClick={(e) => {
                                      e.stopPropagation(); // Prevent triggering the parent button's onClick
                                      handleEditOrder(order.id, e);
                                    }}
                                    className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1 cursor-pointer"
                                    role="button"
                                    tabIndex={0}
                                    aria-label="Edit order name"
                                  >
                                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                  </div>
                                )}
                              </div>
                              <span className="text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 transition-colors duration-200 flex-shrink-0">
                                {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                              </span>
                            </div>
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                ) : !activeOrderId && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200">
                    No draft orders available
                  </div>
                )}

                {/* Create new order */}
                {isCreatingOrder ? (
                  <div className="p-2 border-t border-gray-200 dark:border-gray-700">
                    <input
                      type="text"
                      value={newOrderName}
                      onChange={(e) => setNewOrderName(e.target.value)}
                      placeholder="Enter order name..."
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 font-medium text-gray-900 dark:text-white placeholder-gray-700 dark:placeholder-gray-300 bg-white dark:bg-gray-700 transition-colors duration-200"
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex space-x-2 mt-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCreateOrder();
                        }}
                        disabled={!newOrderName.trim()}
                        className="flex-1 px-2 py-1 text-xs bg-blue-600 dark:bg-blue-700 text-white rounded hover:bg-blue-700 dark:hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium transition-colors duration-200"
                      >
                        Create & Add
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsCreatingOrder(false);
                        }}
                        className="flex-1 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 transition-colors duration-200"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsCreatingOrder(true);
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900 border-t border-gray-200 dark:border-gray-700 font-medium transition-colors duration-200"
                  >
                    + {t('orders.createNewOrder')}
                  </button>
                )}
              </div>
            )}

            {/* Success message */}
            {showSuccessMessage && (
              <div className="absolute top-0 left-0 right-0 -mt-8 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs py-1 px-2 rounded text-center transition-colors duration-200">
                Added to order!
              </div>
            )}
          </div>

          {/* Quick Add Button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAddToBasket();
            }}
            disabled={!activeOrderId}
            className="flex-shrink-0 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white py-1.5 px-3 rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed text-sm flex items-center justify-center transition-colors duration-200"
            title="Add to cart"
            aria-label="Add to cart"
          >
            <svg className="h-4 w-4" viewBox="0 0 576 512" fill="currentColor">
              <path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM252 160c0 11 9 20 20 20h44v44c0 11 9 20 20 20s20-9 20-20V180h44c11 0 20-9 20-20s-9-20-20-20H356V96c0-11-9-20-20-20s-20 9-20 20v44H272c-11 0-20 9-20 20z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
