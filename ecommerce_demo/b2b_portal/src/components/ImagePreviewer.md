# ImagePreviewer Component

A modern, interactive image previewer component for e-commerce platforms built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

- Thumbnail navigation (vertical or horizontal layout)
- Interactive zoom functionality (hover/click toggle)
- Magnifying glass cursor indicator
- Responsive design (mobile/desktop)
- Keyboard accessibility (ESC to close zoom)
- Touch support for mobile devices
- Loading states for images
- ARIA labels for accessibility

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `images` | `string[]` | Required | Array of image URLs to display |
| `alt` | `string` | Required | Alt text for the images (will be appended with image number) |
| `mainImageWidth` | `number` | `800` | Width of the main image in pixels |
| `mainImageHeight` | `number` | `800` | Height of the main image in pixels |
| `thumbnailWidth` | `number` | `100` | Width of thumbnail images in pixels |
| `thumbnailHeight` | `number` | `100` | Height of thumbnail images in pixels |
| `layout` | `'vertical' \| 'horizontal'` | `'vertical'` | Layout direction for thumbnails |

## Usage Example

```tsx
import ImagePreviewer from '@/components/ImagePreviewer';

export default function ProductPage() {
  const productImages = [
    'https://example.com/product-image-1.jpg',
    'https://example.com/product-image-2.jpg',
    'https://example.com/product-image-3.jpg',
    'https://example.com/product-image-4.jpg',
  ];

  return (
    <div className="container mx-auto p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <ImagePreviewer 
            images={productImages} 
            alt="Product Name" 
            mainImageWidth={600}
            mainImageHeight={600}
            thumbnailWidth={80}
            thumbnailHeight={80}
            layout="vertical"
          />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Product Name</h1>
          <p className="mt-4">Product description goes here...</p>
          {/* Other product details */}
        </div>
      </div>
    </div>
  );
}
```

## Behavior

### Desktop
- Click on the main image to toggle zoom mode
- When zoomed, move your cursor over the image to see different parts of the image in the zoom panel
- Click again or press ESC to exit zoom mode
- Click on thumbnails to switch between images

### Mobile
- Tap on the main image to enter full-screen zoom mode
- Move your finger across the screen to see different parts of the zoomed image
- Tap the close button or anywhere on the screen to exit zoom mode
- Swipe horizontally to navigate through thumbnails

## Accessibility

- Fully keyboard navigable
- ARIA roles and labels for screen readers
- Focus indicators for keyboard users
- ESC key to exit zoom mode
