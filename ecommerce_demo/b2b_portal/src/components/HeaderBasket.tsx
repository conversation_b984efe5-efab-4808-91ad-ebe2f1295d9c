'use client';

import { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { setActiveBasket, createBasket } from '../lib/redux/slices/baskets';
import Link from 'next/link';

export default function HeaderBasket() {
  const dispatch = useDispatch();
  const { baskets, activeBasketId } = useSelector((state: RootState) => state.baskets);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isCreatingBasket, setIsCreatingBasket] = useState(false);
  const [newBasketName, setNewBasketName] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const activeBasket = baskets.find(b => b.id === activeBasketId);
  const totalItems = activeBasket?.items.reduce((sum, item) => sum + item.quantity, 0) || 0;
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const handleBasketSelect = (basketId: string) => {
    dispatch(setActiveBasket(basketId));
    setIsDropdownOpen(false);
  };
  
  const handleCreateBasket = () => {
    if (newBasketName.trim()) {
      dispatch(createBasket({ name: newBasketName.trim() }));
      setNewBasketName('');
      setIsCreatingBasket(false);
    }
  };
  
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div className="relative mr-1">
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          {totalItems > 0 && (
            <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {totalItems}
            </span>
          )}
        </div>
        <span className="hidden md:inline">{activeBasket?.name || 'No Basket'}</span>
        <svg className={`ml-1 h-4 w-4 transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10">
          <div className="p-2 border-b">
            <h3 className="text-sm font-medium text-gray-700">Your Baskets</h3>
          </div>
          
          <div className="max-h-60 overflow-y-auto">
            {baskets.length > 0 ? (
              <div className="py-1">
                {baskets.map(basket => (
                  <button
                    key={basket.id}
                    onClick={() => handleBasketSelect(basket.id)}
                    className={`w-full text-left px-4 py-2 text-sm ${activeBasketId === basket.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{basket.name}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${basket.state === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                        {basket.items.length} {basket.items.length === 1 ? 'item' : 'items'}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="py-2 px-4 text-sm text-gray-500">
                No baskets yet. Create your first basket.
              </div>
            )}
          </div>
          
          <div className="p-2 border-t">
            {isCreatingBasket ? (
              <div className="space-y-2">
                <input
                  type="text"
                  value={newBasketName}
                  onChange={(e) => setNewBasketName(e.target.value)}
                  placeholder="Basket name"
                  className="w-full px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  autoFocus
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleCreateBasket}
                    disabled={!newBasketName.trim()}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    Create
                  </button>
                  <button
                    onClick={() => setIsCreatingBasket(false)}
                    className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={() => setIsCreatingBasket(true)}
                  className="flex-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  New Basket
                </button>
                <Link
                  href="/baskets"
                  onClick={() => setIsDropdownOpen(false)}
                  className="flex-1 px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-100 text-center"
                >
                  View All
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
