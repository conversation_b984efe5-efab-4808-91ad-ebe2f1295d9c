'use client';

import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../lib/redux/store';
import { setActiveOrder, ensureDefaultOrder, updateOrderName } from '../lib/redux/slices/orders';
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

export default function OrderSelector() {
  const dispatch = useDispatch<AppDispatch>();
  const { orders, activeOrderId } = useSelector((state: RootState) => state.orders);
  const { t } = useLanguage();

  // Filter to only show open orders (draft and submitted)
  const openOrders = orders.filter(order => order.state === 'draft' || order.state === 'submitted');

  // Group orders by state
  const draftOrders = openOrders.filter(order => order.state === 'draft');
  const submittedOrders = openOrders.filter(order => order.state === 'submitted');

  const [editingOrderId, setEditingOrderId] = useState<string | null>(null);
  const [editOrderName, setEditOrderName] = useState('');
  const editInputRef = useRef<HTMLInputElement>(null);
  const [activeTab, setActiveTab] = useState<'draft' | 'submitted'>('draft');

  // Ensure a default order exists and select it if no active order
  useEffect(() => {
    dispatch(ensureDefaultOrder());

    // If there are orders but no active order, select the first draft order
    if (orders.length > 0 && !activeOrderId) {
      const draftOrders = orders.filter(order => order.state === 'draft');
      if (draftOrders.length > 0) {
        dispatch(setActiveOrder(draftOrders[0].id));
      } else {
        dispatch(setActiveOrder(orders[0].id));
      }
    }
  }, [dispatch, orders, activeOrderId]);

  // Focus the edit input when it appears
  useEffect(() => {
    if (editingOrderId && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editingOrderId]);

  const handleEditOrder = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (order) {
      setEditingOrderId(orderId);
      setEditOrderName(order.name);
    }
  };

  const handleSaveOrderName = () => {
    if (editingOrderId && editOrderName.trim()) {
      dispatch(updateOrderName({ orderId: editingOrderId, name: editOrderName.trim() }));
      setEditingOrderId(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingOrderId(null);
  };

  const handleSelectOrder = (orderId: string) => {
    dispatch(setActiveOrder(orderId));
  };

  // Get the count of orders in each state
  const draftCount = draftOrders.length;
  const submittedCount = submittedOrders.length;

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4 transition-colors duration-200">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">{t('orders.yourOrders')}</h2>
        <Link href="/orders" className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200">
          {t('orders.viewAll')}
        </Link>
      </div>

      {/* Order status tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-3 transition-colors duration-200">
        <nav className="-mb-px flex space-x-4" aria-label="Order status tabs">
          <button
            className={`${
              activeTab === 'draft'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            } whitespace-nowrap pb-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200`}
            onClick={() => setActiveTab('draft')}
          >
            {t('orders.draft')} ({draftCount})
          </button>
          <button
            className={`${
              activeTab === 'submitted'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            } whitespace-nowrap pb-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200`}
            onClick={() => setActiveTab('submitted')}
          >
            {t('orders.submitted')} ({submittedCount})
          </button>
        </nav>
      </div>

      {/* Order list */}
      <div className="space-y-2 max-h-60 overflow-y-auto pr-1 scrollbar-thin">
        {openOrders.length > 0 ? (
          (activeTab === 'draft' ? draftOrders : submittedOrders).map(order => (
            <div
              key={order.id}
              className={`p-2 rounded-md cursor-pointer transition-colors duration-200 ${
                activeOrderId === order.id
                  ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-gray-100 dark:border-gray-700'
              }`}
              onClick={() => handleSelectOrder(order.id)}
            >
              <div className="flex justify-between items-center">
                <div className="flex-1 min-w-0">
                  {editingOrderId === order.id ? (
                    <div className="flex items-center space-x-1">
                      <input
                        ref={editInputRef}
                        type="text"
                        value={editOrderName}
                        onChange={(e) => setEditOrderName(e.target.value)}
                        className="w-full text-sm border border-blue-300 dark:border-blue-700 rounded px-1 py-0.5 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors duration-200"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleSaveOrderName();
                          if (e.key === 'Escape') handleCancelEdit();
                        }}
                      />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSaveOrderName();
                        }}
                        className="text-green-600 hover:text-green-800 p-1"
                      >
                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCancelEdit();
                        }}
                        className="text-red-600 hover:text-red-800 p-1"
                      >
                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-center">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate transition-colors duration-200">{order.name}</h3>
                        {order.state === 'draft' && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditOrder(order.id);
                            }}
                            className="ml-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1 transition-colors duration-200"
                          >
                            <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                          </button>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 transition-colors duration-200">
                        {order.items.length} {order.items.length === 1 ? t('orders.item') : t('orders.items')}
                      </p>
                    </>
                  )}
                </div>
                <div>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                      order.state === 'draft'
                        ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300'
                        : 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300'
                    }`}
                  >
                    {t(`orders.${order.state}`)}
                  </span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4 transition-colors duration-200">
            {activeTab === 'draft' ? t('orders.noDraftOrders') : t('orders.noSubmittedOrders')}
          </p>
        )}
      </div>
    </div>
  );
}
