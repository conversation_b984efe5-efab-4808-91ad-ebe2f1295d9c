export interface ProductAttribute {
  name: string;
  value: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  color?: string; // Color hex code
  colorName?: string; // Color name
  price?: number; // Optional price adjustment
  attributes: ProductAttribute[];
  image?: string; // Variant-specific image
  stock: number;
}

export interface Product {
  id: number;
  name: string;
  description: string;
  categories: string[];
  variants: ProductVariant[];
  price: number;
  discountPercentage?: number; // Optional discount percentage
  discountedPrice?: number; // Calculated discounted price
  service_options: string[];
  image: string;
  images: string[]; // Additional product images
  stock: number;
  sku: string;
  attributes: ProductAttribute[]; // Base product attributes
  brand?: string;
  model?: string;
}

export interface OrderItem {
  productId: number;
  quantity: number;
  selectedVariantId?: string;
  selectedServiceOptions?: string[]; // Kept for backward compatibility but no longer used
  targetPrice?: number; // User's desired price for negotiation
}

export interface Order {
  id: string;
  name: string;
  items: OrderItem[];
  state: 'draft' | 'submitted' | 'confirmed' | 'done';
  createdAt: string;
  updatedAt: string;
}

// Keep for backward compatibility
export type Basket = Order;
export type BasketItem = OrderItem;

export interface FilterState {
  categories: string[];
  subcategories: string[];
  brands: string[];
  priceRange: {
    min: number;
    max: number;
  };

  searchQuery: string;
}

export interface OdooResponse {
  success: boolean;
  data?: any;
  error?: string;
}
