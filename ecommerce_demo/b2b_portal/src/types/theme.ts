// Dynamic Customer Theme System - Types
// Theme mode type
export type ThemeMode = 'light' | 'dark';

// Theme style type
export type ThemeStyle = 'normal' | 'glassmorphism';

// Color palette interface
export interface ColorPalette {
  primary: string;
  primaryHover: string;
  primaryActive: string;
  secondary: string;
  secondaryHover: string;
  accent: string;
  accentHover: string;
  background: string;
  backgroundSecondary: string;
  backgroundTertiary: string;
  foreground: string;
  foregroundSecondary: string;
  foregroundMuted: string;
  border: string;
  borderHover: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

// Glassmorphism specific properties
export interface GlassmorphismConfig {
  enabled: boolean;
  blur: string;
  opacity: string;
  borderOpacity: string;
  shadowIntensity: string;
  backdropSaturation: string;
}

// Typography configuration
export interface TypographyConfig {
  fontPrimary: string;
  fontSecondary: string;
  fontArabic: string;
  fontSizes: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };
  fontWeights: {
    light: string;
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
  };
}

// Layout configuration
export interface LayoutConfig {
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// Customer theme configuration
export interface CustomerTheme {
  customerId: string;
  themeName: string;
  logoUrl: string;
  faviconUrl: string;
  colors: {
    light: ColorPalette;
    dark: ColorPalette;
  };
  typography: TypographyConfig;
  layout: LayoutConfig;
  glassmorphism: {
    light: GlassmorphismConfig;
    dark: GlassmorphismConfig;
  };
  customCSS?: string;
}

// Theme context interface
export interface ThemeContextType {
  // Current theme state
  mode: ThemeMode;
  style: ThemeStyle;
  customerTheme: CustomerTheme;
  
  // Theme actions
  toggleMode: () => void;
  setMode: (mode: ThemeMode) => void;
  setStyle: (style: ThemeStyle) => void;
  setCustomerTheme: (theme: CustomerTheme) => void;
  
  // Utility functions
  getColor: (colorKey: keyof ColorPalette) => string;
  isGlassmorphism: boolean;
  isDark: boolean;
}

// Theme configuration from environment
export interface ThemeEnvironmentConfig {
  customerId: string;
  themeStyle: ThemeStyle;
  enableGlassmorphism: boolean;
}
