'use client';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'next/navigation';
import { RootState, AppDispatch } from '../../../lib/redux/store';
import { getProducts, setSearchQuery } from '../../../lib/redux/slices/products';
import { ensureDefaultOrder } from '../../../lib/redux/slices/orders';
import ProductCard from '../../../components/ProductCard';
import FiltersSidebar from '../../../components/FiltersSidebar';
import OrderSelector from '../../../components/OrderSelector';
import { useGlassmorphism } from '../../../contexts/ThemeContext';
import { GlassCard } from '../../../components/ui/GlassCard';
import { cn } from '../../../lib/utils';

export default function ProductsPage() {
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const { filteredProducts, loading, error } = useSelector((state: RootState) => state.products);
  const { enabled: isGlass } = useGlassmorphism();

  useEffect(() => {
    dispatch(getProducts());
    dispatch(ensureDefaultOrder());
  }, [dispatch]);

  // Apply search query from URL if present
  useEffect(() => {
    if (query) {
      dispatch(setSearchQuery(query));
    }
  }, [query, dispatch]);

  return (
    <div className="flex flex-col lg:flex-row gap-4">
      {/* Left sidebar */}
      <div className="w-full lg:w-1/4 xl:w-1/5 space-y-4">
        <FiltersSidebar />
        <OrderSelector />
      </div>

      {/* Main content */}
      <div className="w-full lg:w-3/4 xl:w-4/5">
        <h1 className="text-2xl font-bold text-theme-foreground mb-2 transition-colors duration-200">KAUST Marketplace</h1>
        <p className="text-theme-foreground-muted mb-6 transition-colors duration-200">
          {query ? `Showing results for "${query}"` : 'Browse our complete product catalog'}
        </p>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary transition-colors duration-200"></div>
          </div>
        ) : error ? (
          <GlassCard className={cn(
            'px-4 py-3 border-l-4',
            {
              'glass border-l-error': isGlass,
              'bg-error/10 border border-error/30 border-l-error': !isGlass,
            }
          )}>
            <p className="text-error">{error}</p>
          </GlassCard>
        ) : filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 md:gap-5">
            {filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <GlassCard className={cn(
            'px-4 py-3 border-l-4',
            {
              'glass border-l-warning': isGlass,
              'bg-warning/10 border border-warning/30 border-l-warning': !isGlass,
            }
          )}>
            <p className="text-warning">No products found matching your filters. Try adjusting your search criteria.</p>
          </GlassCard>
        )}
      </div>
    </div>
  );
}
