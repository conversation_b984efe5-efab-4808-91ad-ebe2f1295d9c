'use client';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../lib/redux/store';
import { getProducts } from '../../lib/redux/slices/products';
import ProductCard from '../../components/ProductCard';
import FiltersSidebar from '../../components/FiltersSidebar';
import BasketSelector from '../../components/BasketSelector';

export default function HomePage() {
  const dispatch = useDispatch();
  const { filteredProducts, loading, error } = useSelector((state: RootState) => state.products);
  
  useEffect(() => {
    // @ts-ignore - TypeScript doesn't recognize the thunk action correctly
    dispatch(getProducts());
  }, [dispatch]);
  
  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Left sidebar */}
      <div className="md:w-1/4 space-y-6">
        <FiltersSidebar />
        <BasketSelector />
      </div>
      
      {/* Main content */}
      <div className="md:w-3/4">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Electronics Marketplace</h1>
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            <p>{error}</p>
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
            <p>No products found matching your filters. Try adjusting your search criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}
