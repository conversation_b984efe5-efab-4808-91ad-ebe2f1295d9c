'use client';

import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../lib/redux/store';
import {
  removeItemFromOrder,
  updateItemQuantity,
  confirmOrder,
  clearOrder,
  deleteOrder
} from '../../../lib/redux/slices/orders';
import { confirmBasket as confirmOrderApi } from '../../../lib/api/odoo';
import OrderSelector from '../../../components/OrderSelector';
import { Product } from '../../../types';
import { mockProducts } from '../../../lib/api/mockData';
import Link from 'next/link';

export default function OrdersPage() {
  const dispatch = useDispatch();
  const { orders, activeOrderId } = useSelector((state: RootState) => state.orders);

  const [isConfirming, setIsConfirming] = useState(false);
  const [confirmationResult, setConfirmationResult] = useState<{ success: boolean; message: string } | null>(null);

  const activeOrder = orders.find(o => o.id === activeOrderId);
  
  // Get product details for basket items
  const getProductDetails = (productId: number): Product | undefined => {
    return mockProducts.find(p => p.id === productId);
  };
  
  // Calculate order total
  const calculateTotal = (): number => {
    if (!activeOrder) return 0;

    return activeOrder.items.reduce((total, item) => {
      const product = getProductDetails(item.productId);
      return total + (product?.price || 0) * item.quantity;
    }, 0);
  };

  // Handle quantity change
  const handleQuantityChange = (itemIndex: number, quantity: number) => {
    if (!activeOrderId) return;

    dispatch(updateItemQuantity({
      orderId: activeOrderId,
      itemIndex,
      quantity,
    }));
  };

  // Handle remove item
  const handleRemoveItem = (itemIndex: number) => {
    if (!activeOrderId) return;

    dispatch(removeItemFromOrder({
      orderId: activeOrderId,
      itemIndex,
    }));
  };

  // Handle clear order
  const handleClearOrder = () => {
    if (!activeOrderId) return;

    if (window.confirm('Are you sure you want to clear this order?')) {
      dispatch(clearOrder(activeOrderId));
    }
  };

  // Handle delete order
  const handleDeleteOrder = () => {
    if (!activeOrderId) return;

    if (window.confirm('Are you sure you want to delete this order?')) {
      dispatch(deleteOrder(activeOrderId));
    }
  };
  
  // Handle confirm order
  const handleConfirmOrder = async () => {
    if (!activeOrder) return;

    setIsConfirming(true);
    setConfirmationResult(null);

    try {
      const result = await confirmOrderApi(activeOrder);

      if (result.success) {
        dispatch(confirmOrder(activeOrder.id));
        setConfirmationResult({
          success: true,
          message: `Order confirmed successfully! RFQ ID: ${result.data?.rfq_id}`,
        });
      } else {
        setConfirmationResult({
          success: false,
          message: result.error || 'Failed to confirm order',
        });
      }
    } catch (error) {
      setConfirmationResult({
        success: false,
        message: 'An error occurred while confirming the order',
      });
    } finally {
      setIsConfirming(false);
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Left sidebar */}
      <div className="md:w-1/4">
        <OrderSelector />
      </div>
      
      {/* Main content */}
      <div className="md:w-3/4">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Your Orders</h1>

        {activeOrder ? (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">
                  {activeOrder.name}
                </h2>
                <span className={`
                  px-3 py-1 text-sm rounded-full
                  ${activeOrder.state === 'draft'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-green-100 text-green-800'}
                `}>
                  {activeOrder.state === 'draft' ? 'Draft' : 'Confirmed'}
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Created: {new Date(activeOrder.createdAt).toLocaleDateString()}
              </p>
            </div>

            {activeOrder.items.length > 0 ? (
              <>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Variant
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {activeOrder.items.map((item, index) => {
                        const product = getProductDetails(item.productId);
                        if (!product) return null;
                        
                        return (
                          <tr key={`${item.productId}-${index}`}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md"></div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {product.name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    SKU: {product.sku}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {item.selectedVariant || 'Standard'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ${product.price.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {activeOrder.state === 'draft' ? (
                                <div className="flex items-center">
                                  <button
                                    onClick={() => handleQuantityChange(index, Math.max(1, item.quantity - 1))}
                                    className="p-1 rounded-md text-gray-400 hover:text-gray-500"
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </button>
                                  <span className="mx-2 text-gray-700">{item.quantity}</span>
                                  <button
                                    onClick={() => handleQuantityChange(index, item.quantity + 1)}
                                    className="p-1 rounded-md text-gray-400 hover:text-gray-500"
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                  </button>
                                </div>
                              ) : (
                                <span className="text-gray-700">{item.quantity}</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ${(product.price * item.quantity).toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              {activeOrder.state === 'draft' && (
                                <button
                                  onClick={() => handleRemoveItem(index)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  Remove
                                </button>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>

                <div className="p-6 bg-gray-50 border-t">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-lg font-semibold text-gray-800">
                        Total: ${calculateTotal().toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {activeOrder.items.length} {activeOrder.items.length === 1 ? 'item' : 'items'}
                      </p>
                    </div>

                    <div className="flex space-x-3">
                      {activeOrder.state === 'draft' ? (
                        <>
                          <button
                            onClick={handleClearOrder}
                            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                          >
                            Clear Order
                          </button>
                          <button
                            onClick={handleConfirmOrder}
                            disabled={isConfirming || activeOrder.items.length === 0}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                          >
                            {isConfirming ? 'Confirming...' : 'Confirm Order'}
                          </button>
                        </>
                      ) : (
                        <Link
                          href="/orders"
                          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                        >
                          View Orders
                        </Link>
                      )}
                    </div>
                  </div>
                  
                  {confirmationResult && (
                    <div className={`mt-4 p-3 rounded-md ${confirmationResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                      {confirmationResult.message}
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="p-6 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Order is empty</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Start adding products to your order from the marketplace.
                </p>
                <div className="mt-6">
                  <Link
                    href="/"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Browse Products
                  </Link>
                </div>
              </div>
            )}

            {activeOrder.state === 'draft' && (
              <div className="p-4 border-t bg-gray-50">
                <button
                  onClick={handleDeleteOrder}
                  className="text-sm text-red-600 hover:text-red-900"
                >
                  Delete Order
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Create your first order to start shopping.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
