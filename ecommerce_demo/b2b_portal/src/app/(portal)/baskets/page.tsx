'use client';

import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../lib/redux/store';
import { 
  removeItemFromBasket, 
  updateItemQuantity, 
  confirmBasket, 
  clearBasket, 
  deleteBasket 
} from '../../../lib/redux/slices/baskets';
import { confirmBasket as confirmBasketApi } from '../../../lib/api/odoo';
import BasketSelector from '../../../components/BasketSelector';
import { Product } from '../../../types';
import { mockProducts } from '../../../lib/api/mockData';
import Link from 'next/link';

export default function BasketsPage() {
  const dispatch = useDispatch();
  const { baskets, activeBasketId } = useSelector((state: RootState) => state.baskets);
  
  const [isConfirming, setIsConfirming] = useState(false);
  const [confirmationResult, setConfirmationResult] = useState<{ success: boolean; message: string } | null>(null);
  
  const activeBasket = baskets.find(b => b.id === activeBasketId);
  
  // Get product details for basket items
  const getProductDetails = (productId: number): Product | undefined => {
    return mockProducts.find(p => p.id === productId);
  };
  
  // Calculate basket total
  const calculateTotal = (): number => {
    if (!activeBasket) return 0;
    
    return activeBasket.items.reduce((total, item) => {
      const product = getProductDetails(item.productId);
      return total + (product?.price || 0) * item.quantity;
    }, 0);
  };
  
  // Handle quantity change
  const handleQuantityChange = (itemIndex: number, quantity: number) => {
    if (!activeBasketId) return;
    
    dispatch(updateItemQuantity({
      basketId: activeBasketId,
      itemIndex,
      quantity,
    }));
  };
  
  // Handle remove item
  const handleRemoveItem = (itemIndex: number) => {
    if (!activeBasketId) return;
    
    dispatch(removeItemFromBasket({
      basketId: activeBasketId,
      itemIndex,
    }));
  };
  
  // Handle clear basket
  const handleClearBasket = () => {
    if (!activeBasketId) return;
    
    if (window.confirm('Are you sure you want to clear this basket?')) {
      dispatch(clearBasket(activeBasketId));
    }
  };
  
  // Handle delete basket
  const handleDeleteBasket = () => {
    if (!activeBasketId) return;
    
    if (window.confirm('Are you sure you want to delete this basket?')) {
      dispatch(deleteBasket(activeBasketId));
    }
  };
  
  // Handle confirm basket
  const handleConfirmBasket = async () => {
    if (!activeBasket) return;
    
    setIsConfirming(true);
    setConfirmationResult(null);
    
    try {
      const result = await confirmBasketApi(activeBasket);
      
      if (result.success) {
        dispatch(confirmBasket(activeBasket.id));
        setConfirmationResult({
          success: true,
          message: `Basket confirmed successfully! RFQ ID: ${result.data?.rfq_id}`,
        });
      } else {
        setConfirmationResult({
          success: false,
          message: result.error || 'Failed to confirm basket',
        });
      }
    } catch (error) {
      setConfirmationResult({
        success: false,
        message: 'An error occurred while confirming the basket',
      });
    } finally {
      setIsConfirming(false);
    }
  };
  
  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Left sidebar */}
      <div className="md:w-1/4">
        <BasketSelector />
      </div>
      
      {/* Main content */}
      <div className="md:w-3/4">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Your Baskets</h1>
        
        {activeBasket ? (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">
                  {activeBasket.name}
                </h2>
                <span className={`
                  px-3 py-1 text-sm rounded-full
                  ${activeBasket.state === 'draft' 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-green-100 text-green-800'}
                `}>
                  {activeBasket.state === 'draft' ? 'Draft' : 'Confirmed'}
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Created: {new Date(activeBasket.createdAt).toLocaleDateString()}
              </p>
            </div>
            
            {activeBasket.items.length > 0 ? (
              <>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Variant
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {activeBasket.items.map((item, index) => {
                        const product = getProductDetails(item.productId);
                        if (!product) return null;
                        
                        return (
                          <tr key={`${item.productId}-${index}`}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md"></div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {product.name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    SKU: {product.sku}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {item.selectedVariant || 'Standard'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ${product.price.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {activeBasket.state === 'draft' ? (
                                <div className="flex items-center">
                                  <button
                                    onClick={() => handleQuantityChange(index, Math.max(1, item.quantity - 1))}
                                    className="p-1 rounded-md text-gray-400 hover:text-gray-500"
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </button>
                                  <span className="mx-2 text-gray-700">{item.quantity}</span>
                                  <button
                                    onClick={() => handleQuantityChange(index, item.quantity + 1)}
                                    className="p-1 rounded-md text-gray-400 hover:text-gray-500"
                                  >
                                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                  </button>
                                </div>
                              ) : (
                                <span className="text-gray-700">{item.quantity}</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              ${(product.price * item.quantity).toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              {activeBasket.state === 'draft' && (
                                <button
                                  onClick={() => handleRemoveItem(index)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  Remove
                                </button>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
                
                <div className="p-6 bg-gray-50 border-t">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-lg font-semibold text-gray-800">
                        Total: ${calculateTotal().toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {activeBasket.items.length} {activeBasket.items.length === 1 ? 'item' : 'items'}
                      </p>
                    </div>
                    
                    <div className="flex space-x-3">
                      {activeBasket.state === 'draft' ? (
                        <>
                          <button
                            onClick={handleClearBasket}
                            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                          >
                            Clear Basket
                          </button>
                          <button
                            onClick={handleConfirmBasket}
                            disabled={isConfirming || activeBasket.items.length === 0}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                          >
                            {isConfirming ? 'Confirming...' : 'Confirm Basket'}
                          </button>
                        </>
                      ) : (
                        <Link
                          href="/orders"
                          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                        >
                          View Orders
                        </Link>
                      )}
                    </div>
                  </div>
                  
                  {confirmationResult && (
                    <div className={`mt-4 p-3 rounded-md ${confirmationResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                      {confirmationResult.message}
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="p-6 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Basket is empty</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Start adding products to your basket from the marketplace.
                </p>
                <div className="mt-6">
                  <Link
                    href="/"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Browse Products
                  </Link>
                </div>
              </div>
            )}
            
            {activeBasket.state === 'draft' && (
              <div className="p-4 border-t bg-gray-50">
                <button
                  onClick={handleDeleteBasket}
                  className="text-sm text-red-600 hover:text-red-900"
                >
                  Delete Basket
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No baskets yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Create your first basket to start shopping.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
