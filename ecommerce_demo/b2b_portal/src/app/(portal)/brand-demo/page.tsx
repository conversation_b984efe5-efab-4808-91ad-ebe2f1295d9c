'use client';

import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';

export default function BrandDemoPage() {
  const { brandConfig, theme } = useTheme();
  const { language } = useLanguage();

  const colorShades = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {language === 'ar' ? 'عرض نظام العلامة التجارية' : 'Brand Configuration Demo'}
          </h1>
          <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-2">
              {language === 'ar' ? 'التكوين الحالي' : 'Current Configuration'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <strong>{language === 'ar' ? 'العلامة التجارية:' : 'Brand:'}</strong> {brandConfig.displayName}
              </div>
              <div>
                <strong>{language === 'ar' ? 'الوضع:' : 'Theme:'}</strong> {theme}
              </div>
              <div>
                <strong>{language === 'ar' ? 'اللغة:' : 'Language:'}</strong> {language}
              </div>
            </div>
          </div>
        </div>

        {/* Primary Colors */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
            {language === 'ar' ? 'الألوان الأساسية' : 'Primary Colors'}
          </h3>
          <div className="grid grid-cols-5 md:grid-cols-11 gap-2">
            {colorShades.map((shade) => (
              <div key={shade} className="text-center">
                <div
                  className="w-full h-16 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                  style={{ backgroundColor: `var(--color-primary-${shade})` }}
                ></div>
                <div className="text-xs text-gray-600 dark:text-gray-400">{shade}</div>
                <div className="text-xs font-mono text-gray-500 dark:text-gray-500">
                  {brandConfig.colors.primary[shade as keyof typeof brandConfig.colors.primary]}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Secondary Colors */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
            {language === 'ar' ? 'الألوان الثانوية' : 'Secondary Colors'}
          </h3>
          <div className="grid grid-cols-5 md:grid-cols-11 gap-2">
            {colorShades.map((shade) => (
              <div key={shade} className="text-center">
                <div
                  className="w-full h-16 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                  style={{ backgroundColor: `var(--color-secondary-${shade})` }}
                ></div>
                <div className="text-xs text-gray-600 dark:text-gray-400">{shade}</div>
                <div className="text-xs font-mono text-gray-500 dark:text-gray-500">
                  {brandConfig.colors.secondary[shade as keyof typeof brandConfig.colors.secondary]}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Semantic Colors */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
            {language === 'ar' ? 'الألوان الدلالية' : 'Semantic Colors'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Success */}
            <div>
              <h4 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                {language === 'ar' ? 'النجاح' : 'Success'}
              </h4>
              <div className="grid grid-cols-4 gap-2">
                {[50, 500, 600, 700].map((shade) => (
                  <div key={shade} className="text-center">
                    <div
                      className="w-full h-12 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                      style={{ backgroundColor: `var(--color-success-${shade})` }}
                    ></div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{shade}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Warning */}
            <div>
              <h4 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                {language === 'ar' ? 'التحذير' : 'Warning'}
              </h4>
              <div className="grid grid-cols-4 gap-2">
                {[50, 500, 600, 700].map((shade) => (
                  <div key={shade} className="text-center">
                    <div
                      className="w-full h-12 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                      style={{ backgroundColor: `var(--color-warning-${shade})` }}
                    ></div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{shade}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Error */}
            <div>
              <h4 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                {language === 'ar' ? 'الخطأ' : 'Error'}
              </h4>
              <div className="grid grid-cols-4 gap-2">
                {[50, 500, 600, 700].map((shade) => (
                  <div key={shade} className="text-center">
                    <div
                      className="w-full h-12 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                      style={{ backgroundColor: `var(--color-error-${shade})` }}
                    ></div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{shade}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Component Examples */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
            {language === 'ar' ? 'أمثلة المكونات' : 'Component Examples'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Primary Button */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                {language === 'ar' ? 'الأزرار الأساسية' : 'Primary Buttons'}
              </h4>
              <button className="w-full bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg transition-colors">
                {language === 'ar' ? 'زر أساسي' : 'Primary Button'}
              </button>
              <button className="w-full bg-secondary-500 hover:bg-secondary-600 text-white px-4 py-2 rounded-lg transition-colors">
                {language === 'ar' ? 'زر ثانوي' : 'Secondary Button'}
              </button>
            </div>

            {/* Status Buttons */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                {language === 'ar' ? 'أزرار الحالة' : 'Status Buttons'}
              </h4>
              <button className="w-full bg-success-500 hover:bg-success-600 text-white px-4 py-2 rounded-lg transition-colors">
                {language === 'ar' ? 'نجح' : 'Success'}
              </button>
              <button className="w-full bg-warning-500 hover:bg-warning-600 text-white px-4 py-2 rounded-lg transition-colors">
                {language === 'ar' ? 'تحذير' : 'Warning'}
              </button>
              <button className="w-full bg-error-500 hover:bg-error-600 text-white px-4 py-2 rounded-lg transition-colors">
                {language === 'ar' ? 'خطأ' : 'Error'}
              </button>
            </div>

            {/* Text Examples */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                {language === 'ar' ? 'أمثلة النص' : 'Text Examples'}
              </h4>
              <p className="text-primary-500">
                {language === 'ar' ? 'نص بلون أساسي' : 'Primary colored text'}
              </p>
              <p className="text-secondary-500">
                {language === 'ar' ? 'نص بلون ثانوي' : 'Secondary colored text'}
              </p>
              <p className="text-success-500">
                {language === 'ar' ? 'نص نجاح' : 'Success text'}
              </p>
              <p className="text-warning-600">
                {language === 'ar' ? 'نص تحذير' : 'Warning text'}
              </p>
              <p className="text-error-500">
                {language === 'ar' ? 'نص خطأ' : 'Error text'}
              </p>
            </div>
          </div>
        </div>

        {/* Environment Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
            {language === 'ar' ? 'تعليمات التبديل' : 'Switching Instructions'}
          </h3>
          <div className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
            <p>
              {language === 'ar' 
                ? 'لتبديل العلامة التجارية، قم بتحديث متغير البيئة في .env.local:'
                : 'To switch brands, update the environment variable in .env.local:'
              }
            </p>
            <code className="block bg-blue-100 dark:bg-blue-900/40 p-2 rounded font-mono text-xs">
              NEXT_PUBLIC_CUSTOMER_BRAND="kaust"  # {language === 'ar' ? 'للعلامة التجارية كاوست' : 'For KAUST branding'}<br/>
              NEXT_PUBLIC_CUSTOMER_BRAND="default"  # {language === 'ar' ? 'للعلامة التجارية الافتراضية' : 'For default branding'}
            </code>
            <p className="text-xs">
              {language === 'ar'
                ? 'سيتم إعادة تحميل الصفحة تلقائياً عند تغيير متغير البيئة.'
                : 'The page will automatically reload when the environment variable changes.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
