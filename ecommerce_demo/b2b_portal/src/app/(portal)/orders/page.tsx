'use client';

import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../../lib/redux/store';
import { submitOrder, confirmOrder, ensureDefaultOrder } from '../../../lib/redux/slices/orders';
import { mockProducts } from '../../../lib/api/mockData';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useGlassmorphism } from '../../../contexts/ThemeContext';
import { GlassCard, GlassButton } from '../../../components/ui/GlassCard';
import { cn } from '../../../lib/utils';

// We'll use the Order type from our Redux store

export default function OrdersPage() {
  const dispatch = useDispatch();
  const { orders } = useSelector((state: RootState) => state.orders);
  const { t } = useLanguage();
  const { enabled: isGlass } = useGlassmorphism();

  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirming, setIsConfirming] = useState(false);
  const [actionResult, setActionResult] = useState<{ success: boolean; message: string } | null>(null);
  const [filterType, setFilterType] = useState<'all' | 'open' | 'closed'>('open');

  // Ensure a default order exists
  useEffect(() => {
    dispatch(ensureDefaultOrder());
  }, [dispatch]);

  // Get the selected order
  const selectedOrder = selectedOrderId ? orders.find(o => o.id === selectedOrderId) : null;

  // Filter orders based on the selected filter type
  const filteredOrders = orders.filter(order => {
    if (filterType === 'all') return true;
    if (filterType === 'open') return order.state === 'draft' || order.state === 'submitted';
    if (filterType === 'closed') return order.state === 'confirmed' || order.state === 'done' || order.state === 'cancelled';
    return true;
  });

  // Get product details for order items
  const getProductDetails = (productId: number) => {
    return mockProducts.find(p => p.id === productId);
  };

  // Calculate order total
  const calculateTotal = (orderItems: any[]): number => {
    return orderItems.reduce((total, item) => {
      const product = getProductDetails(item.productId);
      return total + (product?.price || 0) * item.quantity;
    }, 0);
  };

  // Handle submit order
  const handleSubmitOrder = async (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || order.state !== 'draft' || order.items.length === 0) return;

    setIsSubmitting(true);
    setActionResult(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      dispatch(submitOrder(orderId));
      setActionResult({
        success: true,
        message: 'Order submitted successfully!',
      });

      setTimeout(() => {
        setActionResult(null);
      }, 3000);
    } catch (error) {
      setActionResult({
        success: false,
        message: 'An error occurred while submitting the order',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle confirm order
  const handleConfirmOrder = async (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || order.state !== 'submitted') return;

    setIsConfirming(true);
    setActionResult(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      dispatch(confirmOrder(orderId));
      setActionResult({
        success: true,
        message: 'Order confirmed successfully!',
      });

      setTimeout(() => {
        setActionResult(null);
      }, 3000);
    } catch (error) {
      setActionResult({
        success: false,
        message: 'An error occurred while confirming the order',
      });
    } finally {
      setIsConfirming(false);
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'submitted':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'done':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Your Orders</h1>
          <div className="mt-2 flex space-x-2">
            <button
              onClick={() => setFilterType('open')}
              className={`px-3 py-1 text-sm rounded-md ${filterType === 'open' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}
            >
              {t('orders.openOrders')}
            </button>
            <button
              onClick={() => setFilterType('closed')}
              className={`px-3 py-1 text-sm rounded-md ${filterType === 'closed' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}
            >
              {t('orders.closedOrders')}
            </button>
            <button
              onClick={() => setFilterType('all')}
              className={`px-3 py-1 text-sm rounded-md ${filterType === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}
            >
              {t('orders.allOrders')}
            </button>
          </div>
        </div>
        <Link
          href="/products"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
        >
          Continue Shopping
        </Link>
      </div>

      {filteredOrders.length > 0 ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredOrders.map((order) => (
              <li
                key={order.id}
                className={`hover:bg-gray-50 cursor-pointer ${selectedOrderId === order.id ? 'bg-blue-50' : ''}`}
                onClick={() => setSelectedOrderId(order.id)}
              >
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div>
                        <Link href={`/orders/${order.id}`} className="hover:underline">
                          <p className="text-sm font-medium text-blue-600 dark:text-blue-400 truncate">
                            {order.name}
                          </p>
                        </Link>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <Link href={`/orders/${order.id}`} className="hover:text-blue-600 dark:hover:text-blue-400">
                            Order #{order.id.substring(0, 8).toUpperCase()}
                          </Link>
                        </p>
                      </div>
                      <div className="ml-4 flex-shrink-0 flex">
                        <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(order.state)}`}>
                          {order.state.charAt(0).toUpperCase() + order.state.slice(1)}
                        </p>
                      </div>
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      <p className="text-sm text-gray-500">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500">
                        {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <p className="font-medium text-gray-900">
                        ${calculateTotal(order.items).toFixed(2)}
                      </p>
                    </div>
                  </div>

                  {/* Order actions */}
                  {selectedOrderId === order.id && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500">
                            Updated: {new Date(order.updatedAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex space-x-3">
                          {order.state === 'draft' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSubmitOrder(order.id);
                              }}
                              disabled={isSubmitting || order.items.length === 0}
                              className="px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                              {isSubmitting ? 'Submitting...' : 'Submit Order'}
                            </button>
                          )}

                          {order.state === 'submitted' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleConfirmOrder(order.id);
                              }}
                              disabled={isConfirming}
                              className="px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                              {isConfirming ? 'Confirming...' : 'Confirm Order'}
                            </button>
                          )}
                        </div>
                      </div>

                      {/* Order items preview */}
                      {order.items.length > 0 && (
                        <div className="mt-4 border-t border-gray-200 pt-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Order Items</h4>
                          <div className="space-y-2">
                            {order.items.slice(0, 3).map((item, index) => {
                              const product = getProductDetails(item.productId);
                              if (!product) return null;

                              return (
                                <div key={index} className="flex items-center">
                                  <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md relative overflow-hidden">
                                    {product.image && (
                                      <Image
                                        src={product.image}
                                        alt={product.name}
                                        fill
                                        className="object-contain"
                                      />
                                    )}
                                  </div>
                                  <div className="ml-3 flex-1">
                                    <p className="text-sm font-medium text-gray-900">{product.name}</p>
                                    <p className="text-sm text-gray-500">{item.quantity} × ${product.price.toFixed(2)}</p>
                                  </div>
                                </div>
                              );
                            })}

                            {order.items.length > 3 && (
                              <p className="text-sm text-gray-500 italic">
                                +{order.items.length - 3} more items
                              </p>
                            )}
                          </div>
                        </div>
                      )}

                      {actionResult && selectedOrderId === order.id && (
                        <div className={`mt-4 p-3 rounded-md ${actionResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                          {actionResult.message}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          {orders.length > 0 ? (
            <>
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {filterType === 'open' ? t('orders.noOpenOrders') : filterType === 'closed' ? t('orders.noClosedOrders') : t('orders.noOrders')}
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Try changing your filter or create a new order.
              </p>
              <div className="mt-6 flex justify-center space-x-3">
                <button
                  onClick={() => setFilterType(filterType === 'open' ? 'all' : 'open')}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  {filterType === 'open' ? t('orders.viewAllOrders') : t('orders.viewOpenOrders')}
                </button>
                <Link
                  href="/products"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                >
                  Browse Products
                </Link>
              </div>
            </>
          ) : (
            <>
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No orders yet</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Start by creating an order and adding products to it.
              </p>
              <div className="mt-6">
                <Link
                  href="/products"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                >
                  Browse Products
                </Link>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
