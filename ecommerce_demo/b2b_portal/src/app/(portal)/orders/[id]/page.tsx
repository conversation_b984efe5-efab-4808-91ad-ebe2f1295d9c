'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../../../lib/redux/store';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '../../../../contexts/LanguageContext';
import OrderChatter from '../../../../components/OrderChatter';
import { updateOrderName, updateItemTargetPrice, submitOrder, confirmOrder, removeItemFromOrder, updateItemQuantity } from '../../../../lib/redux/slices/orders';
import { showSuccessAlert, showErrorAlert, showInfoAlert } from '../../../../components/Alert';
import ConfirmDialog from '../../../../components/ConfirmDialog';

export default function OrderDetailPage() {
  const { id } = useParams();
  const orderId = Array.isArray(id) ? id[0] : id;
  const dispatch = useDispatch();
  const { t } = useLanguage();

  const { orders } = useSelector((state: RootState) => state.orders);
  const { products } = useSelector((state: RootState) => state.products);

  const [isEditingName, setIsEditingName] = useState(false);
  const [orderName, setOrderName] = useState('');
  const [editingItemIndex, setEditingItemIndex] = useState<number | null>(null);
  const [targetPrice, setTargetPrice] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirming, setIsConfirming] = useState(false);
  const [editingQuantityIndex, setEditingQuantityIndex] = useState<number | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [itemToRemove, setItemToRemove] = useState<number | null>(null);

  const order = orders.find(o => o.id === orderId);

  useEffect(() => {
    if (order) {
      setOrderName(order.name);
    }
  }, [order]);

  // Track the previous order state to detect changes
  const [prevOrderState, setPrevOrderState] = useState<string | null>(null);

  useEffect(() => {
    if (order && prevOrderState && order.state !== prevOrderState) {
      // Order state has changed, refresh the component
      setEditingItemIndex(null);

      // If the order was submitted, show a success message
      if (prevOrderState === 'draft' && order.state === 'submitted') {
        showSuccessAlert(t('orders.submitSuccess'));
      }
    }

    // Update the previous state
    if (order) {
      setPrevOrderState(order.state);
    }
  }, [order?.state, prevOrderState, t]);

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Order Not Found</h1>
          <p className="text-gray-800 dark:text-gray-200 mb-6">The order you are looking for does not exist or has been removed.</p>
          <Link
            href="/orders"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Return to Orders
          </Link>
        </div>
      </div>
    );
  }

  // Get product details for each item in the order
  const orderItemsWithProducts = order.items.map(item => {
    const product = products.find(p => p.id === item.productId);
    let variant = undefined;

    if (product && item.selectedVariantId) {
      variant = product.variants.find(v => v.id === item.selectedVariantId);
    }

    return {
      ...item,
      product,
      variant
    };
  });

  const totalItems = order.items.reduce((sum, item) => sum + item.quantity, 0);
  const subtotal = orderItemsWithProducts.reduce((sum, item) => {
    return sum + (item.product ?
      (item.product.discountedPrice || item.product.price) * item.quantity : 0);
  }, 0);

  // Calculate savings from discounts
  const savings = orderItemsWithProducts.reduce((sum, item) => {
    if (item.product?.discountPercentage) {
      return sum + ((item.product.price - (item.product.discountedPrice || 0)) * item.quantity);
    }
    return sum;
  }, 0);

  const handleSaveOrderName = () => {
    if (orderName.trim()) {
      dispatch(updateOrderName({ orderId: order.id, name: orderName.trim() }));
      setIsEditingName(false);
    }
  };

  const handleEditTargetPrice = (index: number, currentPrice?: number) => {
    setEditingItemIndex(index);
    setTargetPrice(currentPrice ? currentPrice.toString() : '');
  };

  const handleSaveTargetPrice = (index: number) => {
    if (targetPrice.trim() === '') {
      dispatch(updateItemTargetPrice({
        orderId: order.id,
        itemIndex: index,
        targetPrice: undefined
      }));
    } else {
      const price = parseFloat(targetPrice);
      if (!isNaN(price) && price >= 0) {
        dispatch(updateItemTargetPrice({
          orderId: order.id,
          itemIndex: index,
          targetPrice: price
        }));
      }
    }
    setEditingItemIndex(null);
  };

  const handleEditQuantity = (index: number, currentQuantity: number) => {
    setEditingQuantityIndex(index);
    setQuantity(currentQuantity);
  };

  const handleSaveQuantity = (index: number) => {
    if (quantity > 0) {
      dispatch(updateItemQuantity({
        orderId: order.id,
        itemIndex: index,
        quantity: quantity
      }));
    }
    setEditingQuantityIndex(null);
  };

  const handleRemoveItem = (index: number) => {
    setItemToRemove(index);
    setShowConfirmDialog(true);
  };

  const confirmRemoveItem = () => {
    if (itemToRemove !== null) {
      dispatch(removeItemFromOrder({
        orderId: order.id,
        itemIndex: itemToRemove
      }));
      showSuccessAlert(t('orders.itemRemoved'));
      setItemToRemove(null);
    }
    setShowConfirmDialog(false);
  };

  const cancelRemoveItem = () => {
    setItemToRemove(null);
    setShowConfirmDialog(false);
  };

  const handleSubmitOrder = () => {
    if (!order) return;

    if (order.items.length === 0) {
      showErrorAlert(t('orders.submitEmptyError'));
      return;
    }

    setIsSubmitting(true);
    dispatch(submitOrder(order.id));

    // Reset submitting state after a short delay
    setTimeout(() => {
      setIsSubmitting(false);
    }, 1000);
  };

  const handleConfirmOrder = () => {
    if (!order) return;

    setIsConfirming(true);
    dispatch(confirmOrder(order.id));

    // Reset confirming state after a short delay
    setTimeout(() => {
      setIsConfirming(false);
      showSuccessAlert(t('orders.confirmSuccess'));
    }, 1000);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'done':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link
          href="/orders"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          <svg className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          {t('orders.backToOrders')}
        </Link>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-colors duration-200">
        {/* Order Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex-1">
              <div className="flex items-center">
                {isEditingName ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={orderName}
                      onChange={(e) => setOrderName(e.target.value)}
                      className="text-xl font-bold text-gray-900 dark:text-white border border-blue-300 dark:border-blue-600 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-700 placeholder-gray-700 dark:placeholder-gray-300"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveOrderName();
                        if (e.key === 'Escape') setIsEditingName(false);
                      }}
                      autoFocus
                    />
                    <button
                      onClick={handleSaveOrderName}
                      className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 p-1"
                    >
                      <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setIsEditingName(false)}
                      className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 p-1"
                    >
                      <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{order.name}</h1>
                    {order.state === 'draft' && (
                      <button
                        onClick={() => setIsEditingName(true)}
                        className="ml-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1"
                      >
                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                      </button>
                    )}
                  </>
                )}
              </div>
              <div className="mt-2 flex flex-wrap items-center gap-3">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t('orders.orderNumber')}: <span className="font-medium">#{order.id.substring(0, 8).toUpperCase()}</span>
                </span>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t('orders.created')}: <span className="font-medium">{new Date(order.createdAt).toLocaleDateString()}</span>
                </span>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t('orders.items')}: <span className="font-medium">{totalItems}</span>
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(order.state)}`}>
                  {t(`orders.${order.state}`)}
                </span>
              </div>
            </div>

            <div className="mt-4 md:mt-0 flex flex-col md:items-end">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                ${subtotal.toFixed(2)}
              </div>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {t('orders.subtotal')}
              </div>

              {(order.state === 'draft' || order.state === 'submitted') && (
                <div className="mt-4 flex space-x-2">
                  {order.state === 'draft' && (
                    <button
                      onClick={handleSubmitOrder}
                      disabled={isSubmitting}
                      className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors duration-200 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('orders.submitting')}
                        </>
                      ) : t('orders.submitOrder')}
                    </button>
                  )}

                  {order.state === 'submitted' && (
                    <button
                      onClick={handleConfirmOrder}
                      disabled={isConfirming}
                      className="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-md hover:bg-green-700 dark:hover:bg-green-800 transition-colors duration-200 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {isConfirming ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('orders.confirming')}
                        </>
                      ) : t('orders.confirmOrder')}
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('orders.product')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('orders.variant')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('orders.unitPrice')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('orders.quantity')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('orders.total')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('orders.targetPrice')}
                </th>
                {(order.state === 'draft' || order.state === 'submitted') && (
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('orders.actions')}
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {orderItemsWithProducts.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0 relative bg-gray-100 dark:bg-gray-700 rounded-md overflow-hidden">
                        {item.product?.image && (
                          <Image
                            src={item.product.image}
                            alt={item.product.name}
                            fill
                            className="object-contain"
                          />
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {item.product?.name || 'Unknown Product'}
                        </div>
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          SKU: {item.product?.sku || 'N/A'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {item.variant ? item.variant.name : 'Standard'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {item.product?.discountPercentage ? (
                      <div>
                        <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                          ${item.product.discountedPrice?.toFixed(2) || '0.00'}
                        </div>
                        <div className="flex items-center">
                          <span className="text-xs line-through text-gray-500 dark:text-gray-400">
                            ${item.product.price.toFixed(2)}
                          </span>
                          <span className="ml-1 text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300 px-1 rounded">
                            -{item.product.discountPercentage}%
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-900 dark:text-white font-medium">
                        ${item.product?.price.toFixed(2) || '0.00'}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {editingQuantityIndex === index ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="number"
                          min="1"
                          value={quantity}
                          onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                          className="w-16 px-2 py-1 text-sm border border-blue-300 dark:border-blue-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-medium placeholder-gray-700 dark:placeholder-gray-300"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveQuantity(index);
                            if (e.key === 'Escape') setEditingQuantityIndex(null);
                          }}
                          autoFocus
                        />
                        <button
                          onClick={() => handleSaveQuantity(index)}
                          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 p-1"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                        <button
                          onClick={() => setEditingQuantityIndex(null)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 p-1"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900 dark:text-white font-medium">
                          {item.quantity}
                        </div>
                        {(order.state === 'draft' || order.state === 'submitted') && (
                          <button
                            onClick={() => handleEditQuantity(index, item.quantity)}
                            className="ml-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1"
                          >
                            <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white font-medium">
                      ${(item.product ?
                        (item.product.discountedPrice || item.product.price) * item.quantity
                        : 0).toFixed(2)}
                    </div>
                    {item.product?.discountPercentage && (
                      <div className="text-xs text-green-600 dark:text-green-400">
                        Save: ${((item.product.price - (item.product.discountedPrice || 0)) * item.quantity).toFixed(2)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {editingItemIndex === index ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={targetPrice}
                          onChange={(e) => setTargetPrice(e.target.value)}
                          className="w-24 px-2 py-1 text-sm border border-blue-300 dark:border-blue-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-700 dark:placeholder-gray-300"
                          placeholder="Enter price"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveTargetPrice(index);
                            if (e.key === 'Escape') setEditingItemIndex(null);
                          }}
                          autoFocus
                        />
                        <button
                          onClick={() => handleSaveTargetPrice(index)}
                          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 p-1"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                        <button
                          onClick={() => setEditingItemIndex(null)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 p-1"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        {item.targetPrice !== undefined ? (
                          <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                            ${item.targetPrice.toFixed(2)}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-500 dark:text-gray-400 italic">
                            Not set
                          </span>
                        )}
                        {order.state === 'draft' && (
                          <button
                            onClick={() => handleEditTargetPrice(index, item.targetPrice)}
                            className="ml-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1"
                          >
                            <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    )}
                  </td>
                  {(order.state === 'draft' || order.state === 'submitted') && (
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 mr-3"
                      >
                        {t('orders.remove')}
                      </button>
                      <button
                        onClick={() => handleEditQuantity(index, item.quantity)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      >
                        {t('orders.edit')}
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Order Summary */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex justify-end">
            <div className="w-full md:w-1/3">
              <div className="flex justify-between py-2 text-sm text-gray-700 dark:text-gray-300">
                <span>{t('orders.subtotal')}:</span>
                <span className="font-medium text-gray-900 dark:text-white">${subtotal.toFixed(2)}</span>
              </div>
              {savings > 0 && (
                <div className="flex justify-between py-2 text-sm text-green-600 dark:text-green-400">
                  <span>{t('orders.savings')}:</span>
                  <span className="font-medium">${savings.toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between py-2 text-sm text-gray-700 dark:text-gray-300">
                <span>{t('orders.shipping')}:</span>
                <span className="font-medium text-gray-900 dark:text-white">$0.00</span>
              </div>
              <div className="flex justify-between py-2 text-sm text-gray-700 dark:text-gray-300">
                <span>{t('orders.tax')}:</span>
                <span className="font-medium text-gray-900 dark:text-white">$0.00</span>
              </div>
              <div className="flex justify-between py-2 text-base font-bold border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
                <span className="text-gray-900 dark:text-white">{t('orders.total')}:</span>
                <span className="text-gray-900 dark:text-white">${subtotal.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Order Chatter - Only show for non-draft orders */}
      <div className="mt-8">
        {showConfirmDialog && (
          <ConfirmDialog
            title={t('orders.removeItemTitle')}
            message={t('orders.confirmRemoveItem')}
            onConfirm={confirmRemoveItem}
            onCancel={cancelRemoveItem}
            type="danger"
          />
        )}

        {order.state === 'draft' ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center transition-colors duration-200">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('orders.chatterUnavailable')}</h2>
            <p className="text-gray-700 dark:text-gray-300">
              {t('orders.chatterDraftMessage')}
            </p>
            <button
              onClick={handleSubmitOrder}
              disabled={isSubmitting}
              className="mt-4 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors duration-200 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('orders.submitting')}
                </>
              ) : t('orders.submitOrder')}
            </button>
          </div>
        ) : (
          <OrderChatter orderId={order.id} />
        )}
      </div>
    </div>
  );
}
