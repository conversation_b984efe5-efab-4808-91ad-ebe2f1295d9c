'use client';

import { ReactNode } from 'react';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { LanguageProvider } from '../../contexts/LanguageContext';
import PortalLayoutContent from '../../components/PortalLayoutContent';
import { AlertContainer } from '../../components/Alert';

interface PortalLayoutProps {
  children: ReactNode;
}

export default function PortalLayout({ children }: PortalLayoutProps) {
  return (
    <ThemeProvider>
      <LanguageProvider>
        <PortalLayoutContent>
          <AlertContainer />
          {children}
        </PortalLayoutContent>
      </LanguageProvider>
    </ThemeProvider>
  );
}
