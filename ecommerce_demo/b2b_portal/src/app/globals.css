@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base CSS Variables - Will be overridden by brand theme system */
:root {
  /* Legacy variables for backward compatibility */
  --background: #ffffff;
  --foreground: #171717;

  /* Brand color variables - will be dynamically set by ThemeProvider */
  /* These are fallback values, actual values come from brandConfig.ts */

  /* Primary brand colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  /* Secondary brand colors */
  --color-secondary-50: #f9fafb;
  --color-secondary-100: #f3f4f6;
  --color-secondary-200: #e5e7eb;
  --color-secondary-300: #d1d5db;
  --color-secondary-400: #9ca3af;
  --color-secondary-500: #6b7280;
  --color-secondary-600: #4b5563;
  --color-secondary-700: #374151;
  --color-secondary-800: #1f2937;
  --color-secondary-900: #111827;
  --color-secondary-950: #030712;

  /* Semantic colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-500: #fbbf24;
  --color-warning-600: #f59e0b;
  --color-warning-700: #d97706;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  /* Neutral colors */
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;
  --color-neutral-950: #030712;

  /* Typography variables */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-secondary: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
  --font-arabic: 'Noto Sans Arabic', 'Tajawal', 'Cairo', sans-serif;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Layout variables */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Shadow variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);


}

/* Light theme styles */
.light {
  --background: var(--color-neutral-50);
  --foreground: var(--color-neutral-900);
  --color-background: var(--color-neutral-50);
  --color-background-secondary: var(--color-neutral-100);
  --color-background-tertiary: var(--color-neutral-200);
  --color-foreground: var(--color-neutral-900);
  --color-foreground-secondary: var(--color-neutral-700);
  --color-foreground-muted: var(--color-neutral-500);
  --color-border: var(--color-neutral-200);
  --color-border-hover: var(--color-neutral-300);
}

/* Dark theme styles */
.dark {
  --background: var(--color-neutral-950);
  --foreground: var(--color-neutral-50);
  --color-background: var(--color-neutral-950);
  --color-background-secondary: var(--color-neutral-900);
  --color-background-tertiary: var(--color-neutral-800);
  --color-foreground: var(--color-neutral-50);
  --color-foreground-secondary: var(--color-neutral-200);
  --color-foreground-muted: var(--color-neutral-400);
  --color-border: var(--color-neutral-800);
  --color-border-hover: var(--color-neutral-700);
}

/* Base body styles */
body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* RTL Support */
[dir="rtl"] .rtl-mirror {
  transform: scaleX(-1);
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .md\:order-1 {
  order: 2;
}

[dir="rtl"] .md\:order-2 {
  order: 1;
}



/* Smooth transitions for theme changes */
* {
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease,
    fill 0.3s ease,
    stroke 0.3s ease,
    box-shadow 0.3s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-primary-500);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-600);
}

/* Utility classes for brand colors */
.bg-primary { background-color: var(--color-primary-500); }
.bg-primary-hover { background-color: var(--color-primary-600); }
.bg-primary-light { background-color: var(--color-primary-50); }
.bg-secondary { background-color: var(--color-secondary-500); }
.bg-secondary-hover { background-color: var(--color-secondary-600); }
.bg-success { background-color: var(--color-success-500); }
.bg-warning { background-color: var(--color-warning-500); }
.bg-error { background-color: var(--color-error-500); }

.text-primary { color: var(--color-primary-500); }
.text-primary-dark { color: var(--color-primary-700); }
.text-secondary { color: var(--color-secondary-500); }
.text-success { color: var(--color-success-500); }
.text-warning { color: var(--color-warning-500); }
.text-error { color: var(--color-error-500); }
.text-foreground { color: var(--color-foreground); }
.text-foreground-muted { color: var(--color-foreground-muted); }

.border-primary { border-color: var(--color-primary-500); }
.border-secondary { border-color: var(--color-border); }
.border-neutral { border-color: var(--color-neutral-200); }

/* Brand-specific button styles */
.btn-primary {
  background-color: var(--color-primary-500);
  color: white;
  border: 1px solid var(--color-primary-500);
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
  border-color: var(--color-primary-600);
}

.btn-secondary {
  background-color: var(--color-secondary-500);
  color: white;
  border: 1px solid var(--color-secondary-500);
}

.btn-secondary:hover {
  background-color: var(--color-secondary-600);
  border-color: var(--color-secondary-600);
}
