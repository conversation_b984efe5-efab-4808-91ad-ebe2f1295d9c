@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base CSS Variables - Will be overridden by theme system */
:root {
  /* Legacy variables for backward compatibility */
  --background: #ffffff;
  --foreground: #171717;

  /* Default theme variables - will be overridden by ThemeProvider */
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-primary-active: #1D4ED8;
  --color-secondary: #6B7280;
  --color-secondary-hover: #4B5563;
  --color-accent: #10B981;
  --color-accent-hover: #059669;
  --color-background: #FFFFFF;
  --color-background-secondary: #F9FAFB;
  --color-background-tertiary: #F3F4F6;
  --color-foreground: #111827;
  --color-foreground-secondary: #374151;
  --color-foreground-muted: #6B7280;
  --color-border: #E5E7EB;
  --color-border-hover: #D1D5DB;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;

  /* Typography variables */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-secondary: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
  --font-arabic: 'Noto Sans Arabic', 'Tajawal', 'Cairo', sans-serif;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Layout variables */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Shadow variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);


}

/* Dark mode fallback */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --color-background: #111827;
    --color-background-secondary: #1F2937;
    --color-background-tertiary: #374151;
    --color-foreground: #F9FAFB;
    --color-foreground-secondary: #E5E7EB;
    --color-foreground-muted: #9CA3AF;
    --color-border: #374151;
    --color-border-hover: #4B5563;
  }
}

/* Base body styles */
body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* RTL Support */
[dir="rtl"] .rtl-mirror {
  transform: scaleX(-1);
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .md\:order-1 {
  order: 2;
}

[dir="rtl"] .md\:order-2 {
  order: 1;
}



/* Smooth transitions for theme changes */
* {
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease,
    fill 0.3s ease,
    stroke 0.3s ease,
    box-shadow 0.3s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-hover);
}

/* Utility classes for theme colors */
.bg-primary { background-color: var(--color-primary); }
.bg-primary-hover { background-color: var(--color-primary-hover); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-accent { background-color: var(--color-accent); }
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-accent { color: var(--color-accent); }
.text-foreground { color: var(--color-foreground); }
.text-foreground-muted { color: var(--color-foreground-muted); }
.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-border); }
