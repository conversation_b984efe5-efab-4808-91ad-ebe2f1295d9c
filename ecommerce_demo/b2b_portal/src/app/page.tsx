'use client';

import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Link from 'next/link';
import { RootState } from '../lib/redux/store';
import { getProducts } from '../lib/redux/slices/products';
import { useLanguage } from '../contexts/LanguageContext';
import { useTheme } from '../contexts/ThemeContext';
import { Product } from '../types';
import ProductCard from '../components/ProductCard';
import ThemeToggle from '../components/ThemeToggle';
import LanguageToggle from '../components/LanguageToggle';

export default function Home() {
  const dispatch = useDispatch();
  const { filteredProducts, loading } = useSelector((state: RootState) => state.products);
  const { language } = useLanguage();
  const { brandConfig } = useTheme();
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);

  useEffect(() => {
    // @ts-expect-error - Redux dispatch type issue
    dispatch(getProducts());
  }, [dispatch]);

  useEffect(() => {
    // Get first 6 products as featured
    if (filteredProducts.length > 0) {
      setFeaturedProducts(filteredProducts.slice(0, 6));
    }
  }, [filteredProducts]);

  const categories = [
    {
      id: 'electronics',
      name: 'Electronics',
      nameAr: 'الإلكترونيات',
      icon: '🔌',
      count: '150+ Products',
      description: 'Industrial sensors, controllers, and IoT devices'
    },
    {
      id: 'networking',
      name: 'Networking',
      nameAr: 'الشبكات',
      icon: '🌐',
      count: '80+ Products',
      description: 'Switches, routers, and connectivity solutions'
    },
    {
      id: 'power',
      name: 'Power Systems',
      nameAr: 'أنظمة الطاقة',
      icon: '⚡',
      count: '60+ Products',
      description: 'UPS systems, power supplies, and batteries'
    },
    {
      id: 'displays',
      name: 'Displays',
      nameAr: 'الشاشات',
      icon: '📺',
      count: '45+ Products',
      description: 'Industrial monitors and display solutions'
    },
    {
      id: 'storage',
      name: 'Storage',
      nameAr: 'التخزين',
      icon: '💾',
      count: '70+ Products',
      description: 'Enterprise storage and data solutions'
    },
    {
      id: 'development',
      name: 'Development Kits',
      nameAr: 'أدوات التطوير',
      icon: '🛠️',
      count: '35+ Products',
      description: 'Prototyping and development tools'
    }
  ];

  const businessBenefits = [
    {
      icon: '📦',
      title: 'Bulk Ordering',
      titleAr: 'الطلبات بالجملة',
      description: 'Special pricing for large quantity orders'
    },
    {
      icon: '👤',
      title: 'Account Manager',
      titleAr: 'مدير الحساب',
      description: 'Dedicated support for your business needs'
    },
    {
      icon: '💰',
      title: 'Custom Quotes',
      titleAr: 'عروض أسعار مخصصة',
      description: 'Tailored pricing for your specific requirements'
    },
    {
      icon: '🚚',
      title: 'Fast Delivery',
      titleAr: 'التوصيل السريع',
      description: 'Priority shipping for business customers'
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-primary">
                {brandConfig.displayName} Marketplace
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <LanguageToggle />
              <Link
                href="/products"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                {language === 'ar' ? 'دخول البوابة' : 'Enter Portal'}
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className={`${brandConfig.name === 'kaust'
        ? 'bg-gradient-to-r from-primary-500 to-tertiary-500'
        : 'bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-800 dark:to-blue-900'
      } text-white`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                {language === 'ar'
                  ? `سوق ${brandConfig.displayName} للأعمال`
                  : `${brandConfig.displayName} B2B Marketplace`
                }
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100">
                {language === 'ar'
                  ? 'حلول تقنية متقدمة للشركات والمؤسسات'
                  : 'Advanced Technology Solutions for Businesses & Institutions'
                }
              </p>
              <p className="text-lg mb-8 text-blue-200">
                {language === 'ar'
                  ? 'اكتشف مجموعة واسعة من المنتجات الصناعية والتقنية مع أسعار خاصة للشركات وخدمة عملاء مخصصة'
                  : 'Discover a wide range of industrial and technology products with special business pricing and dedicated customer service'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  href="/products"
                  className={`${brandConfig.name === 'kaust'
                    ? 'bg-white text-primary-600 hover:bg-secondary-50'
                    : 'bg-white text-blue-600 hover:bg-gray-100'
                  } px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200`}
                >
                  {language === 'ar' ? 'تصفح المنتجات' : 'Browse Products'}
                </Link>
                <Link
                  href="/account"
                  className={`${brandConfig.name === 'kaust'
                    ? 'border-2 border-white text-white hover:bg-secondary-500 hover:text-white hover:border-secondary-500'
                    : 'border-2 border-white text-white hover:bg-white hover:text-blue-600'
                  } px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200`}
                >
                  {language === 'ar' ? 'إنشاء حساب' : 'Create Account'}
                </Link>
              </div>
            </div>
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-md">
                <div className="text-center">
                  <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">
                    {language === 'ar' ? 'أكثر من 500 منتج' : '500+ Products'}
                  </h3>
                  <p className="text-blue-200">
                    {language === 'ar'
                      ? 'منتجات صناعية وتقنية عالية الجودة'
                      : 'High-quality industrial and technology products'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Category Browse Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {language === 'ar' ? 'تصفح حسب الفئة' : 'Browse by Category'}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              {language === 'ar'
                ? 'اكتشف مجموعة واسعة من المنتجات المصنفة حسب احتياجاتك التجارية'
                : 'Discover our wide range of products organized by your business needs'
              }
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/products?category=${category.id}`}
                className="group bg-white dark:bg-gray-700 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-600"
              >
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-4">{category.icon}</div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                      {language === 'ar' ? category.nameAr : category.name}
                    </h3>
                    <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                      {category.count}
                    </p>
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {category.description}
                </p>
                <div className="mt-4 flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium">
                  {language === 'ar' ? 'عرض المنتجات' : 'View Products'}
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-16 bg-white dark:bg-gray-900 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {language === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              {language === 'ar'
                ? 'اكتشف أحدث المنتجات التقنية المناسبة للشركات والمؤسسات'
                : 'Discover the latest technology products suitable for businesses and institutions'
              }
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              href="/products"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200"
            >
              {language === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Business Benefits Section */}
      <section className="py-16 bg-blue-50 dark:bg-gray-800 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {language === 'ar' ? 'مزايا الأعمال' : 'Business Benefits'}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              {language === 'ar'
                ? 'استفد من خدماتنا المخصصة للشركات والمؤسسات'
                : 'Take advantage of our specialized services for businesses and institutions'
              }
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {businessBenefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="bg-white dark:bg-gray-700 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <span className="text-3xl">{benefit.icon}</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {language === 'ar' ? benefit.titleAr : benefit.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Highlights Bar */}
      <section className="py-12 bg-gray-900 dark:bg-gray-950 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-400 mb-2">
                {language === 'ar' ? 'خصومات الجملة' : 'Bulk Discounts'}
              </div>
              <div className="text-sm text-gray-300">
                {language === 'ar' ? 'وفر أكثر مع الطلبات الكبيرة' : 'Save more with large orders'}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400 mb-2">
                {language === 'ar' ? 'مدير حساب مخصص' : 'Dedicated Manager'}
              </div>
              <div className="text-sm text-gray-300">
                {language === 'ar' ? 'دعم شخصي لحسابك' : 'Personal support for your account'}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400 mb-2">
                {language === 'ar' ? 'عروض أسعار مخصصة' : 'Custom Quotes'}
              </div>
              <div className="text-sm text-gray-300">
                {language === 'ar' ? 'أسعار مصممة لاحتياجاتك' : 'Pricing tailored to your needs'}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400 mb-2">
                {language === 'ar' ? 'توصيل سريع' : 'Fast Delivery'}
              </div>
              <div className="text-sm text-gray-300">
                {language === 'ar' ? 'شحن أولوية للشركات' : 'Priority shipping for businesses'}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call-to-Action Section */}
      <section className={`py-16 ${brandConfig.name === 'kaust'
        ? 'bg-gradient-to-r from-secondary-500 to-tertiary-500'
        : 'bg-gradient-to-r from-blue-600 to-teal-600'
      } text-white`}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {language === 'ar' ? 'ابدأ رحلتك التجارية معنا' : 'Start Your Business Journey With Us'}
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            {language === 'ar'
              ? 'انضم إلى آلاف الشركات التي تثق في حلولنا التقنية'
              : 'Join thousands of businesses that trust our technology solutions'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/account"
              className={`${brandConfig.name === 'kaust'
                ? 'bg-white text-secondary-600 hover:bg-primary-50'
                : 'bg-white text-blue-600 hover:bg-gray-100'
              } px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200`}
            >
              {language === 'ar' ? 'إنشاء حساب تجاري' : 'Create Business Account'}
            </Link>
            <Link
              href="/products"
              className={`${brandConfig.name === 'kaust'
                ? 'border-2 border-white text-white hover:bg-primary-500 hover:border-primary-500'
                : 'border-2 border-white text-white hover:bg-white hover:text-blue-600'
              } px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200`}
            >
              {language === 'ar' ? 'استكشف المنتجات' : 'Explore Products'}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold text-primary mb-4">
                {brandConfig.displayName} Marketplace
              </h3>
              <p className="text-gray-300 mb-4">
                {language === 'ar'
                  ? 'منصة رائدة لحلول التكنولوجيا والمنتجات الصناعية للشركات والمؤسسات'
                  : 'Leading platform for technology solutions and industrial products for businesses and institutions'
                }
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">
                {language === 'ar' ? 'روابط سريعة' : 'Quick Links'}
              </h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/products" className="hover:text-blue-400 transition-colors duration-200">{language === 'ar' ? 'المنتجات' : 'Products'}</Link></li>
                <li><Link href="/orders" className="hover:text-blue-400 transition-colors duration-200">{language === 'ar' ? 'الطلبات' : 'Orders'}</Link></li>
                <li><Link href="/account" className="hover:text-blue-400 transition-colors duration-200">{language === 'ar' ? 'الحساب' : 'Account'}</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">
                {language === 'ar' ? 'تواصل معنا' : 'Contact Us'}
              </h4>
              <p className="text-gray-300">
                {language === 'ar' ? 'البريد الإلكتروني: ' : 'Email: '}
                <a href={`mailto:marketplace@${brandConfig.name.toLowerCase()}.edu.sa`} className="text-primary hover:opacity-80">
                  marketplace@{brandConfig.name.toLowerCase()}.edu.sa
                </a>
              </p>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 {brandConfig.displayName} Marketplace. {language === 'ar' ? 'جميع الحقوق محفوظة' : 'All rights reserved'}.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
