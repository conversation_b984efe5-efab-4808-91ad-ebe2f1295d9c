'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();

  // Redirect to products page after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push('/products');
    }, 2000); // 2 seconds delay

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <div className="text-center max-w-md mx-auto p-8 rounded-xl shadow-lg bg-white/80 backdrop-blur-sm">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-blue-700 mb-2">KAUST Marketplace</h1>
          <p className="text-gray-600">Your one-stop shop for industrial electronics</p>
        </div>

        <div className="flex flex-col items-center justify-center mb-6">
          {/* Logo or icon could go here */}
          <div className="w-24 h-24 rounded-full bg-blue-600 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>

          {/* Loading animation */}
          <div className="flex items-center justify-center space-x-2 mt-4">
            <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>

          <p className="text-sm text-gray-500 mt-4">Loading portal...</p>
        </div>
      </div>
    </div>
  );
}
