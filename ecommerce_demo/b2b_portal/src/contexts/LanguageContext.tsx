'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export type Language = 'en' | 'ar';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string) => string;
  dir: 'ltr' | 'rtl';
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Simple translations
const translations: Record<Language, Record<string, string>> = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.orders': 'Orders',
    'nav.account': 'My Account',

    // Product page
    'products.title': 'Products',
    'products.search': 'Search products...',
    'products.filters': 'Filters',
    'products.productType': 'Product Type',
    'products.category': 'Category',
    'products.brands': 'Brands',
    'products.priceRange': 'Price Range',
    'products.minPrice': 'Min Price',
    'products.maxPrice': 'Max Price',
    'products.applyFilters': 'Apply Filters',
    'products.resetFilters': 'Reset Filters',
    'products.clearFilters': 'Clear All Filters',
    'products.quantity': 'Quantity',
    'products.addToOrder': 'Add to Order',
    'products.sku': 'SKU',
    'products.selectOrderFirst': 'Please select an order first',

    // Orders
    'orders.title': 'Your Orders',
    'orders.newOrder': 'New Order',

    'orders.confirmed': 'Confirmed',
    'orders.done': 'Done',
    'orders.orderName': 'Order Name',
    'orders.createOrder': 'Create Order',
    'orders.addToActiveOrder': 'Add to active order',
    'orders.createNewOrder': 'Create new order',
    'orders.orderEmpty': 'Order is empty',
    'orders.continueShopping': 'Continue Shopping',
    'orders.backToOrders': 'Back to Orders',
    'orders.orderNumber': 'Order Number',
    'orders.created': 'Created',

    'orders.subtotal': 'Subtotal',
    'orders.shipping': 'Shipping',
    'orders.tax': 'Tax',
    'orders.total': 'Total',
    'orders.submitOrder': 'Submit Order',
    'orders.submitting': 'Submitting...',
    'orders.confirmOrder': 'Confirm Order',
    'orders.confirming': 'Confirming...',
    'orders.confirmSuccess': 'Order confirmed successfully!',
    'orders.product': 'Product',
    'orders.variant': 'Variant',
    'orders.unitPrice': 'Unit Price',
    'orders.quantity': 'Quantity',
    'orders.actions': 'Actions',
    'orders.remove': 'Remove',
    'orders.edit': 'Edit',
    'orders.targetPrice': 'Target Price',
    'orders.savings': 'Savings',
    'orders.discussion': 'Discussion',
    'orders.participants': 'Participants',
    'orders.typeMessage': 'Type a message...',
    'orders.chatterUnavailable': 'Discussion Not Available',
    'orders.chatterDraftMessage': 'The discussion feature will be available after you submit this order. Submit your order to start a conversation with our team.',
    'orders.submitSuccess': 'Order submitted successfully! You can now start a conversation with our team.',
    'orders.submitEmptyError': 'Cannot submit an empty order. Please add items to your order first.',
    'orders.confirmRemoveItem': 'Are you sure you want to remove this item from the order?',
    'orders.removeItemTitle': 'Remove Item',
    'orders.itemRemoved': 'Item has been removed from the order.',
    'orders.allOrders': 'All Orders',
    'orders.openOrders': 'Open Orders',
    'orders.closedOrders': 'Closed Orders',
    'orders.noOpenOrders': 'No open orders yet. Create your first order.',
    'orders.noDraftOrders': 'No draft orders yet. Create your first order.',
    'orders.noSubmittedOrders': 'No submitted orders yet.',
    'orders.noClosedOrders': 'No closed orders found',
    'orders.viewAllOrders': 'View All Orders',
    'orders.viewOpenOrders': 'View Open Orders',
    'orders.noOrders': 'No orders found',
    'orders.yourOrders': 'Your Orders',
    'orders.viewAll': 'View All',
    'orders.draftOrders': 'Draft Orders',
    'orders.submittedOrders': 'Submitted Orders',
    'orders.item': 'item',
    'orders.items': 'items',
    'orders.draft': 'Draft',
    'orders.submitted': 'Submitted',
    'orders.orderNamePlaceholder': 'Enter order name...',

    // Wishlist
    'wishlist.yourWishlist': 'Your Wishlist',
    'wishlist.items': 'items',
    'wishlist.noItems': 'No items in your wishlist',
    'wishlist.viewAll': 'View All Wishlist',
    'wishlist.addToWishlist': 'Add to Wishlist',
    'wishlist.removeFromWishlist': 'Remove from Wishlist',
    'wishlist.title': 'My Wishlist',
    'wishlist.emptyMessage': 'Your wishlist is empty',
    'wishlist.continueShopping': 'Continue Shopping',
    'wishlist.product': 'Product',
    'wishlist.price': 'Price',
    'wishlist.brand': 'Brand',
    'wishlist.actions': 'Actions',
    'wishlist.addToCart': 'Add to Cart',

    // Account
    'account.title': 'My Account',
    'account.profile': 'Profile Information',
    'account.company': 'Company Details',
    'account.team': 'Team Members',
    'account.activity': 'Recent Activity',
    'account.editProfile': 'Edit Profile',
    'account.personalDetails': 'Personal Details',
    'account.accountInfo': 'Account Information',
    'account.statistics': 'Account Statistics',

    // General
    'general.language': 'Language',
    'general.theme': 'Theme',
    'general.light': 'Light',
    'general.dark': 'Dark',
    'general.english': 'English',
    'general.arabic': 'Arabic',
    'general.save': 'Save',
    'general.cancel': 'Cancel',
    'general.confirm': 'Confirm',
    'general.close': 'Close',
    'general.loading': 'Loading...',
    'general.error': 'An error occurred',
    'general.success': 'Success',

    // Notifications
    'notifications.messages': 'Messages',
    'notifications.markAllAsRead': 'Mark all as read',
    'notifications.noMessages': 'No new messages',
    'notifications.viewAllOrders': 'View all orders',
  },
  ar: {
    // Navigation
    'nav.home': 'الرئيسية',
    'nav.products': 'المنتجات',
    'nav.orders': 'الطلبات',
    'nav.account': 'حسابي',

    // Product page
    'products.title': 'المنتجات',
    'products.search': 'البحث عن منتجات...',
    'products.filters': 'التصفية',
    'products.productType': 'نوع المنتج',
    'products.category': 'الفئة',
    'products.brands': 'العلامات التجارية',
    'products.priceRange': 'نطاق السعر',
    'products.minPrice': 'الحد الأدنى للسعر',
    'products.maxPrice': 'الحد الأقصى للسعر',
    'products.applyFilters': 'تطبيق التصفية',
    'products.resetFilters': 'إعادة ضبط التصفية',
    'products.clearFilters': 'مسح جميع عوامل التصفية',
    'products.quantity': 'الكمية',
    'products.addToOrder': 'إضافة إلى الطلب',
    'products.sku': 'رمز المنتج',
    'products.selectOrderFirst': 'الرجاء تحديد طلب أولاً',

    // Orders
    'orders.title': 'طلباتك',
    'orders.newOrder': 'طلب جديد',

    'orders.confirmed': 'مؤكد',
    'orders.done': 'منتهي',
    'orders.orderName': 'اسم الطلب',
    'orders.createOrder': 'إنشاء طلب',
    'orders.addToActiveOrder': 'إضافة إلى الطلب النشط',
    'orders.createNewOrder': 'إنشاء طلب جديد',
    'orders.orderEmpty': 'الطلب فارغ',
    'orders.continueShopping': 'متابعة التسوق',
    'orders.backToOrders': 'العودة إلى الطلبات',
    'orders.orderNumber': 'رقم الطلب',
    'orders.created': 'تاريخ الإنشاء',

    'orders.subtotal': 'المجموع الفرعي',
    'orders.shipping': 'الشحن',
    'orders.tax': 'الضريبة',
    'orders.total': 'المجموع',
    'orders.submitOrder': 'تقديم الطلب',
    'orders.submitting': 'جاري التقديم...',
    'orders.confirmOrder': 'تأكيد الطلب',
    'orders.confirming': 'جاري التأكيد...',
    'orders.confirmSuccess': 'تم تأكيد الطلب بنجاح!',
    'orders.product': 'المنتج',
    'orders.variant': 'النوع',
    'orders.unitPrice': 'سعر الوحدة',
    'orders.quantity': 'الكمية',
    'orders.actions': 'الإجراءات',
    'orders.remove': 'إزالة',
    'orders.edit': 'تعديل',
    'orders.targetPrice': 'السعر المستهدف',
    'orders.savings': 'التوفير',
    'orders.discussion': 'المناقشة',
    'orders.participants': 'المشاركون',
    'orders.typeMessage': 'اكتب رسالة...',
    'orders.chatterUnavailable': 'المناقشة غير متاحة',
    'orders.chatterDraftMessage': 'ستكون ميزة المناقشة متاحة بعد تقديم هذا الطلب. قم بتقديم طلبك لبدء محادثة مع فريقنا.',
    'orders.submitSuccess': 'تم تقديم الطلب بنجاح! يمكنك الآن بدء محادثة مع فريقنا.',
    'orders.submitEmptyError': 'لا يمكن تقديم طلب فارغ. يرجى إضافة عناصر إلى طلبك أولاً.',
    'orders.confirmRemoveItem': 'هل أنت متأكد من أنك تريد إزالة هذا العنصر من الطلب؟',
    'orders.removeItemTitle': 'إزالة العنصر',
    'orders.itemRemoved': 'تمت إزالة العنصر من الطلب.',
    'orders.allOrders': 'جميع الطلبات',
    'orders.openOrders': 'الطلبات المفتوحة',
    'orders.closedOrders': 'الطلبات المغلقة',
    'orders.noOpenOrders': 'لا توجد طلبات مفتوحة بعد. قم بإنشاء طلبك الأول.',
    'orders.noDraftOrders': 'لا توجد طلبات مسودة بعد. قم بإنشاء طلبك الأول.',
    'orders.noSubmittedOrders': 'لا توجد طلبات مقدمة بعد.',
    'orders.noClosedOrders': 'لا توجد طلبات مغلقة',
    'orders.viewAllOrders': 'عرض جميع الطلبات',
    'orders.viewOpenOrders': 'عرض الطلبات المفتوحة',
    'orders.noOrders': 'لا توجد طلبات',
    'orders.yourOrders': 'طلباتك',
    'orders.viewAll': 'عرض الكل',
    'orders.draftOrders': 'طلبات مسودة',
    'orders.submittedOrders': 'الطلبات المقدمة',
    'orders.item': 'عنصر',
    'orders.items': 'عناصر',
    'orders.draft': 'مسودة',
    'orders.submitted': 'مقدم',
    'orders.orderNamePlaceholder': 'أدخل اسم الطلب...',

    // Wishlist
    'wishlist.yourWishlist': 'قائمة الرغبات',
    'wishlist.items': 'عناصر',
    'wishlist.noItems': 'لا توجد عناصر في قائمة الرغبات',
    'wishlist.viewAll': 'عرض كل قائمة الرغبات',
    'wishlist.addToWishlist': 'أضف إلى قائمة الرغبات',
    'wishlist.removeFromWishlist': 'إزالة من قائمة الرغبات',
    'wishlist.title': 'قائمة رغباتي',
    'wishlist.emptyMessage': 'قائمة الرغبات فارغة',
    'wishlist.continueShopping': 'مواصلة التسوق',
    'wishlist.product': 'المنتج',
    'wishlist.price': 'السعر',
    'wishlist.brand': 'العلامة التجارية',
    'wishlist.actions': 'الإجراءات',
    'wishlist.addToCart': 'إضافة إلى السلة',

    // Account
    'account.title': 'حسابي',
    'account.profile': 'معلومات الملف الشخصي',
    'account.company': 'تفاصيل الشركة',
    'account.team': 'أعضاء الفريق',
    'account.activity': 'النشاط الأخير',
    'account.editProfile': 'تعديل الملف الشخصي',
    'account.personalDetails': 'التفاصيل الشخصية',
    'account.accountInfo': 'معلومات الحساب',
    'account.statistics': 'إحصائيات الحساب',

    // General
    'general.language': 'اللغة',
    'general.theme': 'المظهر',
    'general.light': 'فاتح',
    'general.dark': 'داكن',
    'general.english': 'الإنجليزية',
    'general.arabic': 'العربية',
    'general.save': 'حفظ',
    'general.cancel': 'إلغاء',
    'general.confirm': 'تأكيد',
    'general.close': 'إغلاق',
    'general.loading': 'جاري التحميل...',
    'general.error': 'حدث خطأ',
    'general.success': 'تم بنجاح',

    // Notifications
    'notifications.messages': 'الرسائل',
    'notifications.markAllAsRead': 'تعليم الكل كمقروء',
    'notifications.noMessages': 'لا توجد رسائل جديدة',
    'notifications.viewAllOrders': 'عرض جميع الطلبات',
  },
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  // Initialize language from localStorage if available, otherwise use browser language
  const [language, setLanguageState] = useState<Language>('en');

  useEffect(() => {
    // Check for saved language preference or use browser language
    const savedLanguage = localStorage.getItem('language') as Language | null;
    const browserLanguage = navigator.language.startsWith('ar') ? 'ar' : 'en';

    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
      setLanguageState(savedLanguage);
    } else {
      setLanguageState(browserLanguage);
    }
  }, []);

  useEffect(() => {
    // Update document direction when language changes
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = language;

    // Save language preference to localStorage
    localStorage.setItem('language', language);
  }, [language]);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
  };

  // Translation function
  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  // Direction based on language
  const dir: 'ltr' | 'rtl' = language === 'ar' ? 'rtl' : 'ltr';

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, dir }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
