'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { ThemeContextType, ThemeMode, ThemeStyle, CustomerTheme, ColorPalette } from '../types/theme';
import {
  themeRegistry,
  getThemeConfigFromEnv,
  applyThemeToDocument,
  createGlassmorphismCSS
} from '../lib/theme/themeRegistry';

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider props
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultMode?: ThemeMode;
  defaultStyle?: ThemeStyle;
}

// Legacy support - keep old interface for backward compatibility
interface LegacyThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

// Theme provider component
export function ThemeProvider({
  children,
  defaultMode = 'light',
  defaultStyle = 'normal'
}: ThemeProviderProps) {
  // Get theme configuration from environment
  const envConfig = getThemeConfigFromEnv();

  // State management
  const [mode, setModeState] = useState<ThemeMode>(defaultMode);
  const [style, setStyleState] = useState<ThemeStyle>(envConfig.themeStyle || defaultStyle);
  const [customerTheme, setCustomerThemeState] = useState<CustomerTheme>(
    themeRegistry.getTheme(envConfig.customerId)
  );

  // Initialize theme from localStorage on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check for legacy theme first
      const legacyTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;
      const savedMode = localStorage.getItem('theme-mode') as ThemeMode;
      const savedStyle = localStorage.getItem('theme-style') as ThemeStyle;

      // Migrate legacy theme to new system
      if (legacyTheme && !savedMode) {
        setModeState(legacyTheme);
        localStorage.setItem('theme-mode', legacyTheme);
        localStorage.removeItem('theme'); // Remove legacy
      } else if (savedMode && (savedMode === 'light' || savedMode === 'dark')) {
        setModeState(savedMode);
      }

      if (savedStyle && (savedStyle === 'normal' || savedStyle === 'glassmorphism')) {
        setStyleState(savedStyle);
      }
    }
  }, []);

  // Apply theme to document whenever theme changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      applyThemeToDocument(customerTheme, mode);

      // Apply legacy dark class for backward compatibility
      if (mode === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }

      // Inject glassmorphism CSS if enabled
      if (style === 'glassmorphism' && customerTheme.glassmorphism[mode].enabled) {
        injectGlassmorphismCSS();
      } else {
        removeGlassmorphismCSS();
      }
    }
  }, [customerTheme, mode, style]);

  // Inject glassmorphism CSS
  const injectGlassmorphismCSS = useCallback(() => {
    const existingStyle = document.getElementById('glassmorphism-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    const styleElement = document.createElement('style');
    styleElement.id = 'glassmorphism-styles';
    styleElement.textContent = createGlassmorphismCSS(customerTheme, mode);
    document.head.appendChild(styleElement);
  }, [customerTheme, mode]);

  // Remove glassmorphism CSS
  const removeGlassmorphismCSS = useCallback(() => {
    const existingStyle = document.getElementById('glassmorphism-styles');
    if (existingStyle) {
      existingStyle.remove();
    }
  }, []);

  // Toggle between light and dark mode
  const toggleMode = useCallback(() => {
    const newMode = mode === 'light' ? 'dark' : 'light';
    setModeState(newMode);
    localStorage.setItem('theme-mode', newMode);
  }, [mode]);

  // Legacy toggle function for backward compatibility
  const toggleTheme = toggleMode;

  // Set specific mode
  const setMode = useCallback((newMode: ThemeMode) => {
    setModeState(newMode);
    localStorage.setItem('theme-mode', newMode);
  }, []);

  // Set theme style
  const setStyle = useCallback((newStyle: ThemeStyle) => {
    setStyleState(newStyle);
    localStorage.setItem('theme-style', newStyle);
  }, []);

  // Set customer theme
  const setCustomerTheme = useCallback((theme: CustomerTheme) => {
    setCustomerThemeState(theme);
  }, []);

  // Get color value by key
  const getColor = useCallback((colorKey: keyof ColorPalette): string => {
    return customerTheme.colors[mode][colorKey];
  }, [customerTheme, mode]);

  // Computed values
  const isGlassmorphism = style === 'glassmorphism' && customerTheme.glassmorphism[mode].enabled;
  const isDark = mode === 'dark';

  // Context value with legacy support
  const contextValue: ThemeContextType & LegacyThemeContextType = {
    // New API
    mode,
    style,
    customerTheme,
    toggleMode,
    setMode,
    setStyle,
    setCustomerTheme,
    getColor,
    isGlassmorphism,
    isDark,

    // Legacy API for backward compatibility
    theme: mode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme context (enhanced)
export function useTheme(): ThemeContextType & LegacyThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook to get theme colors
export function useThemeColors() {
  const { customerTheme, mode } = useTheme();
  return customerTheme.colors[mode];
}

// Hook to get glassmorphism status
export function useGlassmorphism() {
  const { style, customerTheme, mode } = useTheme();
  return {
    enabled: style === 'glassmorphism' && customerTheme.glassmorphism[mode].enabled,
    config: customerTheme.glassmorphism[mode],
  };
}

// Hook to get theme typography
export function useThemeTypography() {
  const { customerTheme } = useTheme();
  return customerTheme.typography;
}

// Hook to get theme layout
export function useThemeLayout() {
  const { customerTheme } = useTheme();
  return customerTheme.layout;
}
