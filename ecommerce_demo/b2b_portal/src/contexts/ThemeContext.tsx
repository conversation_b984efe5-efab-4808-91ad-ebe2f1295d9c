'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { getBrandConfig, getCSSVariables, type BrandConfig } from '../config/brandConfig';

// Theme context interface
interface ThemeContextType {
  theme: 'light' | 'dark';
  brandConfig: BrandConfig;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [brandConfig] = useState<BrandConfig>(getBrandConfig());

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;

    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    // Apply theme class for light/dark mode
    root.classList.remove('light', 'dark');
    root.classList.add(theme);

    // Apply brand colors as CSS variables
    const cssVariables = getCSSVariables(brandConfig);
    Object.entries(cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Save theme preference to localStorage
    localStorage.setItem('theme', theme);
  }, [theme, brandConfig]);

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const themeValue: ThemeContextType = {
    theme,
    brandConfig,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={themeValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}