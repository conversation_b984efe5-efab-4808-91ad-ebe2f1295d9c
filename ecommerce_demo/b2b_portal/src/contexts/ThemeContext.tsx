'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

// Theme configuration interface
interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
  };
}

// Available themes for different customers
const themes: Record<string, ThemeConfig> = {
  kaust: {
    name: 'KAUST',
    colors: {
      primary: '30 64 175',    // KAUST Blue #1e40af
      secondary: '15 118 110', // Teal #0f766e
      success: '5 150 105',    // Green #059669
      warning: '217 119 6',    // Orange #d97706
      error: '220 38 38',      // Red #dc2626
    },
  },
  // Add more customer themes here
  default: {
    name: 'Default',
    colors: {
      primary: '59 130 246',   // Blue #3b82f6
      secondary: '107 114 128', // Gray #6b7280
      success: '34 197 94',    // Green #22c55e
      warning: '251 191 36',   // Yellow #fbbf24
      error: '239 68 68',      // Red #ef4444
    },
  },
};

// Theme context interface
interface ThemeContextType {
  theme: 'light' | 'dark';
  themeConfig: ThemeConfig;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Get theme configuration based on environment or customer
const getThemeForCustomer = (): ThemeConfig => {
  const customer = process.env.NEXT_PUBLIC_CUSTOMER || 'kaust';
  return themes[customer] || themes.kaust;
};

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [themeConfig] = useState<ThemeConfig>(getThemeForCustomer());

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;

    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    // Apply theme class
    root.classList.remove('light', 'dark');
    root.classList.add(theme);

    // Apply theme colors as CSS variables
    Object.entries(themeConfig.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Save to localStorage
    localStorage.setItem('theme', theme);
  }, [theme, themeConfig]);

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const themeValue: ThemeContextType = {
    theme,
    themeConfig,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={themeValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}