// Customer Brand Configuration System
// This file defines the color palettes and branding for different customers

export interface BrandColors {
  // Primary brand colors
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // Main brand color
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
  
  // Secondary/accent colors
  secondary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // Main secondary color
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
  
  // Semantic colors
  success: {
    50: string;
    500: string;
    600: string;
    700: string;
  };
  
  warning: {
    50: string;
    500: string;
    600: string;
    700: string;
  };
  
  error: {
    50: string;
    500: string;
    600: string;
    700: string;
  };
  
  // Neutral colors for text and backgrounds
  neutral: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
}

export interface BrandConfig {
  name: string;
  displayName: string;
  colors: BrandColors;
  logo?: {
    light: string;
    dark: string;
  };
}

// KAUST Brand Configuration
// Based on official KAUST branding guidelines and logo colors
const kaustBrand: BrandConfig = {
  name: 'kaust',
  displayName: 'KAUST',
  colors: {
    primary: {
      50: '#eff6ff',    // Very light blue
      100: '#dbeafe',   // Light blue
      200: '#bfdbfe',   // Lighter blue
      300: '#93c5fd',   // Light blue
      400: '#60a5fa',   // Medium light blue
      500: '#1e40af',   // KAUST Main Blue
      600: '#1d4ed8',   // Darker blue
      700: '#1e3a8a',   // Dark blue
      800: '#1e3a8a',   // Darker blue
      900: '#1e293b',   // Very dark blue
      950: '#0f172a',   // Darkest blue
    },
    secondary: {
      50: '#f0fdfa',    // Very light teal
      100: '#ccfbf1',   // Light teal
      200: '#99f6e4',   // Lighter teal
      300: '#5eead4',   // Light teal
      400: '#2dd4bf',   // Medium teal
      500: '#0f766e',   // KAUST Teal
      600: '#0d9488',   // Darker teal
      700: '#0f766e',   // Dark teal
      800: '#115e59',   // Darker teal
      900: '#134e4a',   // Very dark teal
      950: '#042f2e',   // Darkest teal
    },
    success: {
      50: '#f0fdf4',    // Light green background
      500: '#059669',   // Success green
      600: '#047857',   // Darker success
      700: '#065f46',   // Dark success
    },
    warning: {
      50: '#fffbeb',    // Light orange background
      500: '#d97706',   // Warning orange
      600: '#c2410c',   // Darker warning
      700: '#9a3412',   // Dark warning
    },
    error: {
      50: '#fef2f2',    // Light red background
      500: '#dc2626',   // Error red
      600: '#b91c1c',   // Darker error
      700: '#991b1b',   // Dark error
    },
    neutral: {
      50: '#f8fafc',    // Very light gray
      100: '#f1f5f9',   // Light gray
      200: '#e2e8f0',   // Lighter gray
      300: '#cbd5e1',   // Light gray
      400: '#94a3b8',   // Medium gray
      500: '#64748b',   // Gray
      600: '#475569',   // Dark gray
      700: '#334155',   // Darker gray
      800: '#1e293b',   // Very dark gray
      900: '#0f172a',   // Darkest gray
      950: '#020617',   // Almost black
    },
  },
};

// Default Brand Configuration
// Fallback theme with neutral colors
const defaultBrand: BrandConfig = {
  name: 'default',
  displayName: 'Default',
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',   // Default blue
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    secondary: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',   // Default gray
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
    success: {
      50: '#f0fdf4',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
    },
    warning: {
      50: '#fffbeb',
      500: '#fbbf24',
      600: '#f59e0b',
      700: '#d97706',
    },
    error: {
      50: '#fef2f2',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
  },
};

// Brand registry - add new customer brands here
export const brandConfigs: Record<string, BrandConfig> = {
  kaust: kaustBrand,
  default: defaultBrand,
  // Future customer brands can be added here:
  // company2: company2Brand,
  // company3: company3Brand,
};

// Get brand configuration based on environment variable
export function getBrandConfig(): BrandConfig {
  const customerBrand = process.env.NEXT_PUBLIC_CUSTOMER_BRAND || 'kaust';
  const config = brandConfigs[customerBrand];
  
  if (!config) {
    console.warn(`Brand configuration for "${customerBrand}" not found. Falling back to KAUST brand.`);
    return brandConfigs.kaust;
  }
  
  return config;
}

// Helper function to get CSS custom property values
export function getCSSVariables(brand: BrandConfig): Record<string, string> {
  const variables: Record<string, string> = {};
  
  // Primary colors
  Object.entries(brand.colors.primary).forEach(([shade, color]) => {
    variables[`--color-primary-${shade}`] = color;
  });
  
  // Secondary colors
  Object.entries(brand.colors.secondary).forEach(([shade, color]) => {
    variables[`--color-secondary-${shade}`] = color;
  });
  
  // Semantic colors
  Object.entries(brand.colors.success).forEach(([shade, color]) => {
    variables[`--color-success-${shade}`] = color;
  });
  
  Object.entries(brand.colors.warning).forEach(([shade, color]) => {
    variables[`--color-warning-${shade}`] = color;
  });
  
  Object.entries(brand.colors.error).forEach(([shade, color]) => {
    variables[`--color-error-${shade}`] = color;
  });
  
  // Neutral colors
  Object.entries(brand.colors.neutral).forEach(([shade, color]) => {
    variables[`--color-neutral-${shade}`] = color;
  });
  
  return variables;
}
