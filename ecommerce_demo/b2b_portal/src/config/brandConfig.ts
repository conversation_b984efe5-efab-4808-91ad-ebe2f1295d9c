// Customer Brand Configuration System
// This file defines the color palettes and branding for different customers

export interface BrandColors {
  // Primary brand colors
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // Main brand color
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
  
  // Secondary/accent colors
  secondary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // Main secondary color
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
  
  // Tertiary colors (for KAUST teal)
  tertiary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;  // Main tertiary color
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };

  // Semantic colors
  success: {
    50: string;
    500: string;
    600: string;
    700: string;
  };

  warning: {
    50: string;
    500: string;
    600: string;
    700: string;
  };

  error: {
    50: string;
    500: string;
    600: string;
    700: string;
  };
  
  // Neutral colors for text and backgrounds
  neutral: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
}

export interface BrandConfig {
  name: string;
  displayName: string;
  colors: BrandColors;
  logo?: {
    light: string;
    dark: string;
  };
}

// KAUST Brand Configuration
// Based on official KAUST logo colors (yellow, orange, green, teal)
const kaustBrand: BrandConfig = {
  name: 'kaust',
  displayName: 'KAUST',
  colors: {
    primary: {
      50: '#fffbeb',    // Very light yellow
      100: '#fef3c7',   // Light yellow
      200: '#fde68a',   // Lighter yellow
      300: '#fcd34d',   // Light yellow
      400: '#fbbf24',   // Medium yellow
      500: '#FFD600',   // KAUST Logo Yellow (main)
      600: '#d97706',   // Darker yellow/orange
      700: '#b45309',   // Dark yellow/orange
      800: '#92400e',   // Darker yellow/orange
      900: '#78350f',   // Very dark yellow/orange
      950: '#451a03',   // Darkest yellow/orange
    },
    secondary: {
      50: '#ecfdf5',    // Very light green
      100: '#d1fae5',   // Light green
      200: '#a7f3d0',   // Lighter green
      300: '#6ee7b7',   // Light green
      400: '#34d399',   // Medium green
      500: '#B2DF28',   // KAUST Logo Green
      600: '#059669',   // Darker green
      700: '#047857',   // Dark green
      800: '#065f46',   // Darker green
      900: '#064e3b',   // Very dark green
      950: '#022c22',   // Darkest green
    },
    tertiary: {
      50: '#f0fdfa',    // Very light teal
      100: '#ccfbf1',   // Light teal
      200: '#99f6e4',   // Lighter teal
      300: '#5eead4',   // Light teal
      400: '#2dd4bf',   // Medium teal
      500: '#26C6DA',   // KAUST Logo Teal
      600: '#0891b2',   // Darker teal
      700: '#0e7490',   // Dark teal
      800: '#155e75',   // Darker teal
      900: '#164e63',   // Very dark teal
      950: '#083344',   // Darkest teal
    },
    success: {
      50: '#ecfdf5',    // Light green background
      500: '#B2DF28',   // KAUST Logo Green for success
      600: '#059669',   // Darker success
      700: '#047857',   // Dark success
    },
    warning: {
      50: '#fff7ed',    // Light orange background
      500: '#FFA040',   // KAUST Logo Orange for warning
      600: '#ea580c',   // Darker warning
      700: '#c2410c',   // Dark warning
    },
    error: {
      50: '#fef2f2',    // Light red background
      500: '#dc2626',   // Error red (keeping standard red)
      600: '#b91c1c',   // Darker error
      700: '#991b1b',   // Dark error
    },
    neutral: {
      50: '#f8fafc',    // Very light gray
      100: '#f1f5f9',   // Light gray
      200: '#e2e8f0',   // Lighter gray
      300: '#cbd5e1',   // Light gray
      400: '#94a3b8',   // Medium gray
      500: '#64748b',   // Gray
      600: '#475569',   // Dark gray
      700: '#334155',   // Darker gray
      800: '#1e293b',   // Very dark gray
      900: '#0f172a',   // Darkest gray
      950: '#020617',   // Almost black
    },
  },
};

// Default Brand Configuration
// Fallback theme with neutral colors
const defaultBrand: BrandConfig = {
  name: 'default',
  displayName: 'Default',
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',   // Default blue
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    secondary: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',   // Default gray
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
    tertiary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',   // Default teal
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49',
    },
    success: {
      50: '#f0fdf4',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
    },
    warning: {
      50: '#fffbeb',
      500: '#fbbf24',
      600: '#f59e0b',
      700: '#d97706',
    },
    error: {
      50: '#fef2f2',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
  },
};

// Brand registry - add new customer brands here
export const brandConfigs: Record<string, BrandConfig> = {
  kaust: kaustBrand,
  default: defaultBrand,
  // Future customer brands can be added here:
  // company2: company2Brand,
  // company3: company3Brand,
};

// Get brand configuration based on environment variable
export function getBrandConfig(): BrandConfig {
  const customerBrand = (process.env.NEXT_PUBLIC_CUSTOMER_BRAND || 'kaust').toLowerCase();
  const config = brandConfigs[customerBrand];

  if (!config) {
    console.warn(`Brand configuration for "${customerBrand}" not found. Falling back to KAUST brand.`);
    return brandConfigs.kaust;
  }

  return config;
}

// Helper function to get CSS custom property values
export function getCSSVariables(brand: BrandConfig): Record<string, string> {
  const variables: Record<string, string> = {};
  
  // Primary colors
  Object.entries(brand.colors.primary).forEach(([shade, color]) => {
    variables[`--color-primary-${shade}`] = color;
  });
  
  // Secondary colors
  Object.entries(brand.colors.secondary).forEach(([shade, color]) => {
    variables[`--color-secondary-${shade}`] = color;
  });

  // Tertiary colors
  Object.entries(brand.colors.tertiary).forEach(([shade, color]) => {
    variables[`--color-tertiary-${shade}`] = color;
  });

  // Semantic colors
  Object.entries(brand.colors.success).forEach(([shade, color]) => {
    variables[`--color-success-${shade}`] = color;
  });
  
  Object.entries(brand.colors.warning).forEach(([shade, color]) => {
    variables[`--color-warning-${shade}`] = color;
  });
  
  Object.entries(brand.colors.error).forEach(([shade, color]) => {
    variables[`--color-error-${shade}`] = color;
  });
  
  // Neutral colors
  Object.entries(brand.colors.neutral).forEach(([shade, color]) => {
    variables[`--color-neutral-${shade}`] = color;
  });
  
  return variables;
}
