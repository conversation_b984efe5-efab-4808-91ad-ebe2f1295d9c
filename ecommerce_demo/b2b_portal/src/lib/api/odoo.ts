import { Basket, OdooResponse, Product } from '../../types';
import { mockProducts } from './mockData';

// Simulate API delay
const simulateApiDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Mock Odoo API endpoint
const ODOO_ENDPOINT = process.env.NEXT_PUBLIC_ODOO_API_URL || 'https://api.example.com/odoo';

/**
 * Fetch all products from the mock API
 */
export const fetchProducts = async (): Promise<Product[]> => {
  // Simulate API delay
  await simulateApiDelay();

  // Return mock products
  return mockProducts;
};

/**
 * Fetch a single product by ID
 */
export const fetchProductById = async (productId: number): Promise<Product | null> => {
  // Simulate API delay
  await simulateApiDelay();

  // Find product by ID
  const product = mockProducts.find(p => p.id === productId);

  return product || null;
};

/**
 * Search products by query
 */
export const searchProducts = async (query: string): Promise<Product[]> => {
  // Simulate API delay
  await simulateApiDelay();

  // Filter products by query
  if (!query) return [];

  const lowerCaseQuery = query.toLowerCase();
  return mockProducts.filter(product =>
    product.name.toLowerCase().includes(lowerCaseQuery) ||
    product.description.toLowerCase().includes(lowerCaseQuery) ||
    product.categories.some(cat => cat.toLowerCase().includes(lowerCaseQuery)) ||
    product.variants.some(variant => variant.name.toLowerCase().includes(lowerCaseQuery)) ||
    (product.brand && product.brand.toLowerCase().includes(lowerCaseQuery))
  );
};

/**
 * Confirm a basket (create RFQ in Odoo)
 */
export const confirmBasket = async (basket: Basket): Promise<OdooResponse> => {
  // Simulate API delay
  await simulateApiDelay(1000);

  // Validate basket
  if (!basket.id) {
    return {
      success: false,
      error: 'Invalid basket ID',
    };
  }

  if (basket.items.length === 0) {
    return {
      success: false,
      error: 'Basket is empty',
    };
  }

  // Simulate successful API call
  console.log(`Confirming basket ${basket.id} with ${basket.items.length} items`);

  // In a real implementation, this would make an HTTP POST request to Odoo
  // Example:
  // const response = await fetch(`${ODOO_ENDPOINT}/sale_order/confirm_rfq`, {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json',
  //   },
  //   body: JSON.stringify({
  //     basket_id: basket.id,
  //     items: basket.items,
  //     state: 'confirmed',
  //   }),
  // });
  // return await response.json();

  return {
    success: true,
    data: {
      rfq_id: `RFQ${Math.floor(Math.random() * 10000)}`,
      message: 'Request for Quotation created successfully',
    },
  };
};

/**
 * Get order history
 */
export const getOrderHistory = async (): Promise<OdooResponse> => {
  // Simulate API delay
  await simulateApiDelay(800);

  // Simulate successful API call
  return {
    success: true,
    data: {
      orders: [
        {
          id: 'ORD10001',
          date: '2023-04-01',
          status: 'Delivered',
          total: 1299.99,
          items: 5,
        },
        {
          id: 'ORD10002',
          date: '2023-04-15',
          status: 'Processing',
          total: 799.50,
          items: 3,
        },
        {
          id: 'ORD10003',
          date: '2023-05-02',
          status: 'Pending',
          total: 2499.99,
          items: 8,
        },
      ],
    },
  };
};
