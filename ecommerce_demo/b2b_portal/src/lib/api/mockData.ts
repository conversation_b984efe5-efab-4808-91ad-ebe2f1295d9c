import { Product, ProductVariant, ProductAttribute } from '../../types';
import { laptopProducts } from './laptopMockData';
import { phoneProducts } from './phoneMockData';

// Helper function to generate random price within a range
const randomPrice = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1) + min);
};

// Helper function to generate random stock quantity
const randomStock = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1) + min);
};

// Categories for electronics products
const categories = [
  'Sensors',
  'Controllers',
  'Displays',
  'Networking',
  'Power Supplies',
  'Processors',
  'Storage',
  'Connectors',
  'Development Kits',
  'IoT Devices',
];

// Variants for electronics products
const variants = [
  '5G',
  'LoRaWAN',
  'Bluetooth',
  'WiFi',
  'Ethernet',
  'USB',
  'HDMI',
  'VGA',
  'DisplayPort',
  'Waterproof',
  'Industrial Grade',
  'Military Grade',
  'Consumer Grade',
];

// Service options for electronics products
const serviceOptions = [
  'Installation',
  'Calibration',
  'Maintenance',
  'Extended Warranty',
  'Technical Support',
  'Training',
  'Consulting',
  'Custom Development',
  'Integration',
];

// Convert old product format to new format
const convertOldProducts = (oldProducts: any[]): Product[] => {
  return oldProducts.map(oldProduct => {
    // Convert string variants to ProductVariant objects
    const variants: ProductVariant[] = oldProduct.variants.map((variant: string, index: number) => ({
      id: `${oldProduct.id}-${index}`,
      name: variant,
      attributes: [],
      stock: Math.floor(oldProduct.stock / oldProduct.variants.length),
    }));

    // Create base attributes
    const attributes: ProductAttribute[] = [];

    return {
      ...oldProduct,
      variants,
      attributes,
      images: [oldProduct.image],
    };
  });
};

// Old format products
const oldFormatProducts = [
  {
    id: 1,
    name: 'Industrial IoT Sensor',
    description: 'High-precision industrial IoT sensor for temperature and humidity monitoring.',
    categories: ['Sensors', 'IoT Devices'],
    variants: ['5G', 'LoRaWAN'],
    price: 199,
    service_options: ['Installation', 'Calibration', 'Maintenance'],
    image: '/products/iot-sensor.jpg',
    stock: 150,
    sku: 'IOT-SEN-001',
  },
  {
    id: 2,
    name: 'Smart Controller Module',
    description: 'Programmable controller module for industrial automation systems.',
    categories: ['Controllers', 'IoT Devices'],
    variants: ['Ethernet', 'Industrial Grade'],
    price: 349,
    service_options: ['Installation', 'Training', 'Custom Development'],
    image: '/products/controller.jpg',
    stock: 75,
    sku: 'CTRL-MOD-002',
  },
  {
    id: 3,
    name: 'High-Resolution Display Panel',
    description: '4K industrial display panel with touchscreen capabilities.',
    categories: ['Displays'],
    variants: ['HDMI', 'DisplayPort', 'Industrial Grade'],
    price: 599,
    service_options: ['Installation', 'Extended Warranty'],
    image: '/products/display.jpg',
    stock: 50,
    sku: 'DISP-PNL-003',
  },
  {
    id: 4,
    name: 'Industrial Network Switch',
    description: 'Managed network switch for industrial environments.',
    categories: ['Networking'],
    variants: ['Ethernet', 'Industrial Grade'],
    price: 449,
    service_options: ['Installation', 'Configuration', 'Maintenance'],
    image: '/products/network-switch.jpg',
    stock: 60,
    sku: 'NET-SWT-004',
  },
  {
    id: 5,
    name: 'Uninterruptible Power Supply',
    description: 'Industrial UPS for critical systems with surge protection.',
    categories: ['Power Supplies'],
    variants: ['Industrial Grade'],
    price: 799,
    service_options: ['Installation', 'Maintenance'],
    image: '/products/ups.jpg',
    stock: 40,
    sku: 'PWR-UPS-005',
  },
];

// Generate additional mock products for oldFormatProducts
for (let i = 6; i <= 20; i++) {
  // Randomly select 1-2 categories
  const productCategories: string[] = [];
  const numCategories = Math.floor(Math.random() * 2) + 1;
  for (let j = 0; j < numCategories; j++) {
    const category = categories[Math.floor(Math.random() * categories.length)];
    if (!productCategories.includes(category)) {
      productCategories.push(category);
    }
  }

  // Randomly select 1-3 variants
  const productVariants: string[] = [];
  const numVariants = Math.floor(Math.random() * 3) + 1;
  for (let j = 0; j < numVariants; j++) {
    const variant = variants[Math.floor(Math.random() * variants.length)];
    if (!productVariants.includes(variant)) {
      productVariants.push(variant);
    }
  }

  // Randomly select 1-3 service options
  const productServiceOptions: string[] = [];
  const numServiceOptions = Math.floor(Math.random() * 3) + 1;
  for (let j = 0; j < numServiceOptions; j++) {
    const option = serviceOptions[Math.floor(Math.random() * serviceOptions.length)];
    if (!productServiceOptions.includes(option)) {
      productServiceOptions.push(option);
    }
  }

  // Generate a product name based on the first category
  let productName = '';
  if (productCategories.includes('Sensors')) {
    productName = `${['Advanced', 'Precision', 'Smart', 'Industrial'][Math.floor(Math.random() * 4)]} Sensor`;
  } else if (productCategories.includes('Controllers')) {
    productName = `${['Programmable', 'Smart', 'Industrial', 'Advanced'][Math.floor(Math.random() * 4)]} Controller`;
  } else if (productCategories.includes('Displays')) {
    productName = `${['HD', '4K', 'Touchscreen', 'Industrial'][Math.floor(Math.random() * 4)]} Display`;
  } else if (productCategories.includes('Networking')) {
    productName = `${['Managed', 'Industrial', 'Smart', 'High-Speed'][Math.floor(Math.random() * 4)]} ${['Switch', 'Router', 'Gateway'][Math.floor(Math.random() * 3)]}`;
  } else if (productCategories.includes('Power Supplies')) {
    productName = `${['Industrial', 'High-Capacity', 'Smart', 'Reliable'][Math.floor(Math.random() * 4)]} Power Supply`;
  } else if (productCategories.includes('Processors')) {
    productName = `${['High-Performance', 'Industrial', 'Multi-Core', 'Embedded'][Math.floor(Math.random() * 4)]} Processor`;
  } else if (productCategories.includes('Storage')) {
    productName = `${['Industrial', 'High-Capacity', 'SSD', 'Flash'][Math.floor(Math.random() * 4)]} Storage`;
  } else if (productCategories.includes('Connectors')) {
    productName = `${['Industrial', 'Waterproof', 'High-Density', 'Rugged'][Math.floor(Math.random() * 4)]} Connector`;
  } else if (productCategories.includes('Development Kits')) {
    productName = `${['IoT', 'Industrial', 'Embedded', 'Prototyping'][Math.floor(Math.random() * 4)]} Development Kit`;
  } else if (productCategories.includes('IoT Devices')) {
    productName = `${['Smart', 'Connected', 'Industrial', 'Wireless'][Math.floor(Math.random() * 4)]} IoT Device`;
  } else {
    productName = `Electronic Component ${i}`;
  }

  // Generate a product description
  const productDescription = `High-quality ${productName.toLowerCase()} for industrial applications.`;

  // Generate a product image path
  const productImage = `/products/product-${i}.jpg`;

  // Generate a product SKU
  const productSku = `PROD-${i.toString().padStart(3, '0')}`;

  // Add the product to the oldFormatProducts array
  oldFormatProducts.push({
    id: i,
    name: productName,
    description: productDescription,
    categories: productCategories,
    variants: productVariants,
    price: randomPrice(99, 1999),
    service_options: productServiceOptions,
    image: productImage,
    stock: randomStock(10, 200),
    sku: productSku,
  });
}

// Convert old format products to new format
const convertedProducts = convertOldProducts(oldFormatProducts);

// Combine all products - prioritizing laptops
export const mockProducts: Product[] = [...laptopProducts, ...phoneProducts, ...convertedProducts];

// Define main categories and subcategories
export const mainCategories = ['Laptops', 'Phones', 'Electronics'];

// Define subcategories for each main category
export const subcategories = {
  'Laptops': ['Business', 'Gaming', 'Ultrabook', 'Convertible', 'Workstation'],
  'Phones': ['Smartphone', 'Feature Phone', 'Rugged Phone', 'Foldable'],
  'Electronics': [...categories]
};

// Get all unique brands from products
const extractBrands = () => {
  const brands = mockProducts
    .filter(product => product.brand)
    .map(product => product.brand as string);
  // Sort the brands alphabetically to ensure consistent order between server and client
  return [...new Set(brands)].sort();
};

// Export all available categories, brands, and service options for filtering
export const availableCategories = mainCategories;
export const availableSubcategories = Object.values(subcategories).flat();
export const availableBrands = extractBrands();
export const availableServiceOptions = serviceOptions;
