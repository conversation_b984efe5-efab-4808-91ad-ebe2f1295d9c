import { Product, ProductAttribute, ProductVariant } from '../../types';

// Phone image URLs from Unsplash
const phoneImages: Record<string, string[]> = {
  'TechX': [
    'https://images.unsplash.com/photo-1598327105666-5b89351aff97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c21hcnRwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8c21hcnRwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1546054454-aa26e2b734c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8c21hcnRwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'GalaxyPro': [
    'https://images.unsplash.com/photo-1610945415295-d9bbf067e59c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2Ftc3VuZ3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1553179459-4514c0f52f41?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fHNhbXN1bmd8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8c2Ftc3VuZ3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'PixelMax': [
    'https://images.unsplash.com/photo-1598965402089-897ce52e8355?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cGl4ZWwlMjBwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1613836255019-a7b845504c8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8cGl4ZWwlMjBwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1591531110744-49b891110d27?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8cGl4ZWwlMjBwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'iConnect': [
    'https://images.unsplash.com/photo-1510557880182-3d4d3cba35a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8aXBob25lfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1591337676887-a217a6970a8a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8aXBob25lfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1556656793-08538906a9f8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8aXBob25lfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60',
  ],
  'OnePlus': [
    'https://images.unsplash.com/photo-1557690756-62754e561982?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8b25lcGx1c3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1569183091671-696b1a43c048?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8b25lcGx1c3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1578598336003-1514a96eeb22?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8b25lcGx1c3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
  ],
};

// Default fallback image
const defaultPhoneImage = 'https://images.unsplash.com/photo-1598327105666-5b89351aff97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c21hcnRwaG9uZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60';

// Helper function to get a phone image based on brand
const getPhoneImage = (brand: string): string => {
  const brandImages = phoneImages[brand];
  if (!brandImages || brandImages.length === 0) {
    return defaultPhoneImage;
  }
  return brandImages[0];
};

// Helper function to get phone images based on brand
const getPhoneImages = (brand: string): string[] => {
  const brandImages = phoneImages[brand];
  if (!brandImages || brandImages.length === 0) {
    return [defaultPhoneImage];
  }
  return brandImages;
};

// Phone brands
const phoneBrands = ['TechX', 'GalaxyPro', 'PixelMax', 'iConnect', 'OnePlus'];

// Phone models by brand
const phoneModels: Record<string, string[]> = {
  'TechX': ['X20 Pro', 'X20 Lite', 'X30 Ultra'],
  'GalaxyPro': ['S22', 'S22+', 'S22 Ultra'],
  'PixelMax': ['Pixel 6', 'Pixel 6 Pro', 'Pixel 6a'],
  'iConnect': ['iConnect 13', 'iConnect 13 Pro', 'iConnect 13 Pro Max'],
  'OnePlus': ['OnePlus 10', 'OnePlus 10 Pro', 'OnePlus Nord'],
};

// Phone colors with hex codes
const phoneColors: Array<{ name: string; hex: string }> = [
  { name: 'Phantom Black', hex: '#000000' },
  { name: 'Phantom White', hex: '#FFFFFF' },
  { name: 'Phantom Silver', hex: '#C0C0C0' },
  { name: 'Phantom Green', hex: '#008000' },
  { name: 'Phantom Blue', hex: '#0000FF' },
  { name: 'Phantom Pink', hex: '#FFC0CB' },
  { name: 'Phantom Gold', hex: '#FFD700' },
];

// Storage options
const storageOptions = ['128GB', '256GB', '512GB', '1TB'];

// RAM options
const ramOptions = ['6GB', '8GB', '12GB', '16GB'];

// Camera options
const cameraOptions = [
  '12MP + 12MP + 12MP',
  '50MP + 12MP + 10MP',
  '108MP + 12MP + 10MP',
  '50MP + 48MP + 48MP',
];

// Display options
const displayOptions = [
  '6.1" FHD+ AMOLED',
  '6.4" FHD+ AMOLED',
  '6.7" QHD+ AMOLED',
  '6.8" QHD+ AMOLED',
];

// Battery options
const batteryOptions = [
  '4000mAh',
  '4500mAh',
  '5000mAh',
  '5500mAh',
];

// Helper function to generate a random phone
const generatePhone = (id: number): Product => {
  // Select random brand and model
  const brand = phoneBrands[Math.floor(Math.random() * phoneBrands.length)];
  const modelOptions = phoneModels[brand];
  const model = modelOptions[Math.floor(Math.random() * modelOptions.length)];

  // Base price depends on the brand and model
  let basePrice = 0;
  if (brand === 'TechX') basePrice = 699;
  else if (brand === 'GalaxyPro') basePrice = 799;
  else if (brand === 'PixelMax') basePrice = 599;
  else if (brand === 'iConnect') basePrice = 899;
  else if (brand === 'OnePlus') basePrice = 649;

  // Adjust price based on model
  if (model.includes('Pro') || model.includes('Ultra') || model.includes('Max')) {
    basePrice += 200;
  } else if (model.includes('Plus') || model.includes('+')) {
    basePrice += 100;
  } else if (model.includes('Lite') || model.includes('a')) {
    basePrice -= 100;
  }

  // Select specs
  const storage = storageOptions[Math.floor(Math.random() * storageOptions.length)];
  const ram = ramOptions[Math.floor(Math.random() * ramOptions.length)];
  const camera = cameraOptions[Math.floor(Math.random() * cameraOptions.length)];
  const display = displayOptions[Math.floor(Math.random() * displayOptions.length)];
  const battery = batteryOptions[Math.floor(Math.random() * batteryOptions.length)];

  // Base attributes
  const attributes: ProductAttribute[] = [
    { name: 'Storage', value: storage },
    { name: 'RAM', value: ram },
    { name: 'Camera', value: camera },
    { name: 'Display', value: display },
    { name: 'Battery', value: battery },
    { name: 'Operating System', value: brand === 'iConnect' ? 'iOS 15' : 'Android 12' },
    { name: 'Connectivity', value: '5G, Wi-Fi 6, Bluetooth 5.2' },
  ];

  // Generate variants (different colors and storage options)
  const variants: ProductVariant[] = [];

  // Add color variants
  phoneColors.forEach((color, index) => {
    // Not all phones have all colors
    if (Math.random() > 0.4) {
      // For each color, create 1-3 storage variants
      const numStorageOptions = Math.floor(Math.random() * 3) + 1;

      for (let i = 0; i < numStorageOptions; i++) {
        // Select storage configuration
        const configStorage = storageOptions[Math.min(i, storageOptions.length - 1)];

        // Price adjustment based on storage
        let priceAdjustment = 0;
        if (configStorage === '256GB') priceAdjustment += 100;
        else if (configStorage === '512GB') priceAdjustment += 200;
        else if (configStorage === '1TB') priceAdjustment += 400;

        // Create variant attributes
        const variantAttributes: ProductAttribute[] = [
          { name: 'Storage', value: configStorage },
        ];

        // Create variant
        variants.push({
          id: `${id}-${color.name}-${configStorage}`,
          name: `${color.name} / ${configStorage}`,
          color: color.hex,
          colorName: color.name,
          price: priceAdjustment,
          attributes: variantAttributes,
          image: getPhoneImage(brand),
          stock: Math.floor(Math.random() * 50) + 5,
        });
      }
    }
  });

  // Determine phone subcategory based on model and brand
  let subcategory = 'Smartphone';
  if (brand === 'OnePlus' && model.includes('Nord')) {
    subcategory = 'Feature Phone';
  } else if (brand === 'GalaxyPro' && model.includes('Ultra')) {
    subcategory = 'Rugged Phone';
  } else if (model.includes('Fold') || model.includes('Flip')) {
    subcategory = 'Foldable';
  }

  // Create the phone product
  return {
    id,
    name: `${brand} ${model}`,
    description: `The ${brand} ${model} features ${storage} storage, ${ram} RAM, and a ${camera} camera system. It has a ${display} display and a ${battery} battery for all-day usage.`,
    categories: ['Phones', subcategory, 'Electronics'],
    variants,
    price: basePrice,
    service_options: ['Screen Protector', 'Phone Case', 'Extended Warranty', 'Trade-In Program'],
    image: getPhoneImage(brand),
    images: getPhoneImages(brand),
    stock: variants.reduce((total, variant) => total + variant.stock, 0),
    sku: `${brand.substring(0, 3).toUpperCase()}-${model.substring(0, 3).toUpperCase()}-${id}`,
    attributes,
    brand,
    model,
  };
};

// Generate phone products
export const phoneProducts: Product[] = [];

// Generate 5 phone products
for (let i = 0; i < 5; i++) {
  phoneProducts.push(generatePhone(2001 + i));
}

// Export phone-related data for filtering
export const availablePhoneBrands = phoneBrands;
export const availablePhoneColors = phoneColors.map(c => c.name);
export const availableStorageOptions = storageOptions;
export const availableRamOptions = ramOptions;
export const availableCameraOptions = cameraOptions;
export const availableDisplayOptions = displayOptions;
export const availableBatteryOptions = batteryOptions;
