import { Product, ProductAttribute, ProductVariant } from '../../types';

// Laptop image URLs from Unsplash
const laptopImages: Record<string, string[]> = {
  'MacBook': [
    'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1569770218135-bea267ed7e84?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1420406676079-b8491f2d07c8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1530893609608-32a9af3aa95c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'Dell XPS': [
    'https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZGVsbCUyMHhwc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1593642632823-8f785ba67e45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8ZGVsbCUyMHhwc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1567521463850-4939134bcd4a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8ZGVsbCUyMHhwc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1720556405438-d67f0f9ecd44?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGRlbGwlMjB4cHN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'HP Spectre': [
    'https://images.unsplash.com/photo-1575320854760-bfffc3550640?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTV8fGRlbGwlMjB4cHN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1595161397851-cb282659df5e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fGRlbGwlMjB4cHN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1595303526913-c7037797ebe7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fGRlbGwlMjB4cHN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1623567238235-940ff1311da7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjB8fGRlbGwlMjB4cHN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'Lenovo ThinkPad': [
    'https://images.unsplash.com/photo-1611258623154-c01feea09b1b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZGVsbCUyMHhwc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1622286346003-c5c7e63b1088?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8ZGVsbCUyMHhwc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1542393545-10f5cde2c810?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1605455699691-abb8c7c8d34a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'ASUS ROG': [
    'https://images.unsplash.com/photo-1537498425277-c283d32ef9db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1504691342899-4d92b50853e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjB8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
  ],
  'Microsoft Surface': [
    'https://images.unsplash.com/photo-1592919933511-ea9d487c85e4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1580522154071-c6ca47a859ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1518448828347-28e2cf0d6e28?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60',
    'https://images.unsplash.com/photo-1651747137395-065bd3af97bb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fG1hY2Jvb2slMjBwcm98ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60',
  ],
};

// Default fallback image
const defaultLaptopImage = 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bWFjYm9vayUyMHByb3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60';

// Helper function to get a laptop image based on brand
const getLaptopImage = (brand: string): string => {
  const brandImages = laptopImages[brand];
  if (!brandImages || brandImages.length === 0) {
    return defaultLaptopImage;
  }
  return brandImages[0];
};

// Helper function to get laptop images based on brand
const getLaptopImages = (brand: string): string[] => {
  const brandImages = laptopImages[brand];
  if (!brandImages || brandImages.length === 0) {
    return [defaultLaptopImage];
  }
  return brandImages;
};

// Laptop brands - using more recognizable names
const laptopBrands = ['MacBook', 'Dell XPS', 'HP Spectre', 'Lenovo ThinkPad', 'ASUS ROG', 'Microsoft Surface'];

// Laptop models by brand - using realistic model names
const laptopModels: Record<string, string[]> = {
  'MacBook': ['Air M2', 'Pro 14"', 'Pro 16"'],
  'Dell XPS': ['13', '15', '17'],
  'HP Spectre': ['x360 13', 'x360 14', 'x360 16'],
  'Lenovo ThinkPad': ['X1 Carbon', 'T14s', 'P1 Gen 5'],
  'ASUS ROG': ['Zephyrus G14', 'Strix G15', 'Flow X13'],
  'Microsoft Surface': ['Laptop 5', 'Laptop Studio', 'Book 3'],
};

// Laptop colors with hex codes - using realistic color options
const laptopColors: Array<{ name: string; hex: string }> = [
  { name: 'Space Gray', hex: '#8D8E8F' },
  { name: 'Silver', hex: '#C0C0C0' },
  { name: 'Midnight Black', hex: '#2C2C2C' },
  { name: 'Rose Gold', hex: '#B76E79' },
  { name: 'Navy Blue', hex: '#000080' },
  { name: 'Forest Green', hex: '#228B22' },
  { name: 'Arctic White', hex: '#F5F5F5' },
  { name: 'Obsidian Black', hex: '#1A1A1A' },
];

// Laptop processor options - updated to latest generations
const processors = [
  'Intel Core i5-13500H',
  'Intel Core i7-13700H',
  'Intel Core i9-13900H',
  'AMD Ryzen 5 7600H',
  'AMD Ryzen 7 7800H',
  'AMD Ryzen 9 7950X',
  'Apple M2',
  'Apple M2 Pro',
  'Apple M2 Max',
];

// RAM options
const ramOptions = ['8GB', '16GB', '32GB', '64GB'];

// Storage options
const storageOptions = ['256GB SSD', '512GB SSD', '1TB SSD', '2TB SSD'];

// Display options
const displayOptions = [
  '13.3" FHD (1920x1080)',
  '14" FHD (1920x1080)',
  '15.6" FHD (1920x1080)',
  '15.6" 4K UHD (3840x2160)',
  '16" QHD (2560x1600)',
  '17.3" FHD (1920x1080)',
];

// Graphics options
const graphicsOptions = [
  'Intel Iris Xe Graphics',
  'NVIDIA GeForce RTX 3050',
  'NVIDIA GeForce RTX 3060',
  'NVIDIA GeForce RTX 3070',
  'NVIDIA GeForce RTX 3080',
  'AMD Radeon RX 6600M',
];

// Helper function to generate a random laptop
const generateLaptop = (id: number): Product => {
  // Select random brand and model
  const brand = laptopBrands[Math.floor(Math.random() * laptopBrands.length)];
  const modelOptions = laptopModels[brand];
  const model = modelOptions[Math.floor(Math.random() * modelOptions.length)];

  // Base price depends on the brand and model
  let basePrice = 0;
  if (brand === 'MacBook') basePrice = 1299;
  else if (brand === 'Dell XPS') basePrice = 1199;
  else if (brand === 'HP Spectre') basePrice = 1099;
  else if (brand === 'Lenovo ThinkPad') basePrice = 1399;
  else if (brand === 'ASUS ROG') basePrice = 1499;
  else if (brand === 'Microsoft Surface') basePrice = 1199;

  // Adjust price based on model
  if (model.includes('Pro') || model.includes('Studio') || model.includes('Max')) {
    basePrice += 500;
  } else if (model.includes('Air') || model.includes('Book')) {
    basePrice += 200;
  } else if (model.includes('ROG') || model.includes('Gaming') || model.includes('Zephyrus') || model.includes('Strix')) {
    basePrice += 600;
  } else if (model.includes('Carbon') || model.includes('X1')) {
    basePrice += 400;
  }

  // Select processor, RAM, storage, display, and graphics
  const processor = processors[Math.floor(Math.random() * processors.length)];
  const ram = ramOptions[Math.floor(Math.random() * ramOptions.length)];
  const storage = storageOptions[Math.floor(Math.random() * storageOptions.length)];
  const display = displayOptions[Math.floor(Math.random() * displayOptions.length)];
  const graphics = graphicsOptions[Math.floor(Math.random() * graphicsOptions.length)];

  // Base attributes
  const attributes: ProductAttribute[] = [
    { name: 'Processor', value: processor },
    { name: 'RAM', value: ram },
    { name: 'Storage', value: storage },
    { name: 'Display', value: display },
    { name: 'Graphics', value: graphics },
    { name: 'Operating System', value: 'Windows 11 Pro' },
    { name: 'Weight', value: `${(1 + Math.random() * 1.5).toFixed(2)} kg` },
  ];

  // Generate variants (different colors and configurations)
  const variants: ProductVariant[] = [];

  // Add color variants
  laptopColors.forEach((color, index) => {
    // Not all laptops have all colors
    if (Math.random() > 0.3) {
      // For each color, create 1-3 configuration variants
      const numConfigs = Math.floor(Math.random() * 3) + 1;

      for (let i = 0; i < numConfigs; i++) {
        // Select random configuration
        const configRam = ramOptions[Math.min(i, ramOptions.length - 1)];
        const configStorage = storageOptions[Math.min(i, storageOptions.length - 1)];

        // Price adjustment based on configuration
        let priceAdjustment = 0;
        if (configRam === '16GB') priceAdjustment += 100;
        else if (configRam === '32GB') priceAdjustment += 300;
        else if (configRam === '64GB') priceAdjustment += 600;

        if (configStorage === '512GB SSD') priceAdjustment += 100;
        else if (configStorage === '1TB SSD') priceAdjustment += 200;
        else if (configStorage === '2TB SSD') priceAdjustment += 400;

        // Create variant attributes
        const variantAttributes: ProductAttribute[] = [
          { name: 'RAM', value: configRam },
          { name: 'Storage', value: configStorage },
        ];

        // Create variant
        variants.push({
          id: `${id}-${color.name}-${i}`,
          name: `${color.name} / ${configRam} / ${configStorage}`,
          color: color.hex,
          colorName: color.name,
          price: priceAdjustment,
          attributes: variantAttributes,
          image: getLaptopImage(brand),
          stock: Math.floor(Math.random() * 50) + 5,
        });
      }
    }
  });

  // Determine laptop subcategory based on model and brand
  let subcategory = 'Business';
  if (model.includes('Gaming') || model.includes('ROG') || model.includes('Strix') || model.includes('Zephyrus')) {
    subcategory = 'Gaming';
  } else if (model.includes('Air') || model.includes('Slim') || model.includes('X1')) {
    subcategory = 'Ultrabook';
  } else if (model.includes('360') || model.includes('Flip') || model.includes('Yoga') || model.includes('x360')) {
    subcategory = 'Convertible';
  } else if (model.includes('Pro') || model.includes('Station') || model.includes('P1')) {
    subcategory = 'Workstation';
  }

  // Randomly apply discounts to some laptops
  const hasDiscount = Math.random() < 0.4; // 40% chance of having a discount
  const discountPercentage = hasDiscount ? Math.floor(Math.random() * 30) + 5 : undefined; // 5-35% discount
  const discountedPrice = discountPercentage ? Math.round(basePrice * (1 - discountPercentage / 100)) : undefined;

  // Create the laptop product
  return {
    id,
    name: `${brand} ${model}`,
    description: `The ${brand} ${model} is a high-performance laptop featuring a ${processor} processor, ${ram} RAM, and ${storage}. It comes with a ${display} display and ${graphics} for exceptional visual performance.`,
    categories: ['Laptops', subcategory, 'Electronics'],
    variants,
    price: basePrice,
    discountPercentage,
    discountedPrice,
    service_options: ['Extended Warranty', 'Technical Support', 'Data Migration'],
    image: getLaptopImage(brand),
    images: getLaptopImages(brand),
    stock: variants.reduce((total, variant) => total + variant.stock, 0),
    sku: `${brand.substring(0, 3).toUpperCase()}-${model.substring(0, 3).toUpperCase()}-${id}`,
    attributes,
    brand,
    model,
  };
};

// Generate laptop products
export const laptopProducts: Product[] = [];

// Generate 15 laptop products
for (let i = 0; i < 15; i++) {
  laptopProducts.push(generateLaptop(1001 + i));
}

// Export laptop-related data for filtering
export const availableLaptopBrands = laptopBrands;
export const availableLaptopColors = laptopColors.map(c => c.name);
export const availableProcessors = processors;
export const availableRamOptions = ramOptions;
export const availableStorageOptions = storageOptions;
export const availableDisplayOptions = displayOptions;
export const availableGraphicsOptions = graphicsOptions;
