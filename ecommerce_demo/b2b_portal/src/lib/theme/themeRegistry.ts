import { CustomerTheme, ThemeEnvironmentConfig, ThemeStyle } from '../../types/theme';
import { kaustTheme } from '../../themes/kaust';
import { defaultTheme } from '../../themes/default';

// Theme Registry - Central place to manage all customer themes
class ThemeRegistry {
  private themes: Map<string, CustomerTheme> = new Map();

  constructor() {
    // Register default themes
    this.registerTheme(defaultTheme);
    this.registerTheme(kaustTheme);
  }

  /**
   * Register a new customer theme
   */
  registerTheme(theme: CustomerTheme): void {
    this.themes.set(theme.customerId, theme);
  }

  /**
   * Get theme by customer ID
   */
  getTheme(customerId: string): CustomerTheme {
    const theme = this.themes.get(customerId);
    if (!theme) {
      console.warn(`Theme not found for customer: ${customerId}, falling back to default`);
      return this.themes.get('default') || defaultTheme;
    }
    return theme;
  }

  /**
   * Get all available themes
   */
  getAllThemes(): CustomerTheme[] {
    return Array.from(this.themes.values());
  }

  /**
   * Check if theme exists
   */
  hasTheme(customerId: string): boolean {
    return this.themes.has(customerId);
  }

  /**
   * Get theme names for selection
   */
  getThemeOptions(): Array<{ value: string; label: string }> {
    return Array.from(this.themes.values()).map(theme => ({
      value: theme.customerId,
      label: theme.themeName,
    }));
  }
}

// Singleton instance
export const themeRegistry = new ThemeRegistry();

/**
 * Get theme configuration from environment variables
 */
export function getThemeConfigFromEnv(): ThemeEnvironmentConfig {
  const customerId = process.env.NEXT_PUBLIC_CUSTOMER_ID || 'default';
  const themeStyle = (process.env.NEXT_PUBLIC_THEME_STYLE as ThemeStyle) || 'normal';
  const enableGlassmorphism = process.env.NEXT_PUBLIC_ENABLE_GLASSMORPHISM === 'true';

  return {
    customerId,
    themeStyle: enableGlassmorphism ? 'glassmorphism' : themeStyle,
    enableGlassmorphism,
  };
}

/**
 * Get the active customer theme based on environment
 */
export function getActiveTheme(): CustomerTheme {
  const config = getThemeConfigFromEnv();
  return themeRegistry.getTheme(config.customerId);
}

/**
 * Generate CSS custom properties from theme
 */
export function generateCSSVariables(theme: CustomerTheme, mode: 'light' | 'dark'): Record<string, string> {
  const colors = theme.colors[mode];
  const typography = theme.typography;
  const layout = theme.layout;
  const glassmorphism = theme.glassmorphism[mode];

  return {
    // Color variables
    '--color-primary': colors.primary,
    '--color-primary-hover': colors.primaryHover,
    '--color-primary-active': colors.primaryActive,
    '--color-secondary': colors.secondary,
    '--color-secondary-hover': colors.secondaryHover,
    '--color-accent': colors.accent,
    '--color-accent-hover': colors.accentHover,
    '--color-background': colors.background,
    '--color-background-secondary': colors.backgroundSecondary,
    '--color-background-tertiary': colors.backgroundTertiary,
    '--color-foreground': colors.foreground,
    '--color-foreground-secondary': colors.foregroundSecondary,
    '--color-foreground-muted': colors.foregroundMuted,
    '--color-border': colors.border,
    '--color-border-hover': colors.borderHover,
    '--color-success': colors.success,
    '--color-warning': colors.warning,
    '--color-error': colors.error,
    '--color-info': colors.info,

    // Typography variables
    '--font-primary': typography.fontPrimary,
    '--font-secondary': typography.fontSecondary,
    '--font-arabic': typography.fontArabic,

    // Layout variables
    '--radius-sm': layout.borderRadius.sm,
    '--radius-md': layout.borderRadius.md,
    '--radius-lg': layout.borderRadius.lg,
    '--radius-xl': layout.borderRadius.xl,
    '--spacing-xs': layout.spacing.xs,
    '--spacing-sm': layout.spacing.sm,
    '--spacing-md': layout.spacing.md,
    '--spacing-lg': layout.spacing.lg,
    '--spacing-xl': layout.spacing.xl,
    '--shadow-sm': layout.shadows.sm,
    '--shadow-md': layout.shadows.md,
    '--shadow-lg': layout.shadows.lg,
    '--shadow-xl': layout.shadows.xl,

    // Glassmorphism variables
    '--glass-blur': glassmorphism.blur,
    '--glass-opacity': glassmorphism.opacity,
    '--glass-border-opacity': glassmorphism.borderOpacity,
    '--glass-shadow-intensity': glassmorphism.shadowIntensity,
    '--glass-backdrop-saturation': glassmorphism.backdropSaturation,
  };
}

/**
 * Apply theme to document
 */
export function applyThemeToDocument(theme: CustomerTheme, mode: 'light' | 'dark'): void {
  const root = document.documentElement;
  const variables = generateCSSVariables(theme, mode);

  // Apply CSS variables
  Object.entries(variables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });

  // Apply theme class
  root.className = root.className.replace(/theme-\w+/g, '');
  root.classList.add(`theme-${theme.customerId}`);

  // Apply mode class
  root.className = root.className.replace(/mode-\w+/g, '');
  root.classList.add(`mode-${mode}`);

  // Update favicon if provided
  if (theme.faviconUrl) {
    const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
    if (favicon) {
      favicon.href = theme.faviconUrl;
    }
  }
}

/**
 * Create glassmorphism CSS class
 */
export function createGlassmorphismCSS(theme: CustomerTheme, mode: 'light' | 'dark'): string {
  const glass = theme.glassmorphism[mode];
  
  if (!glass.enabled) return '';

  return `
    .glass {
      backdrop-filter: blur(${glass.blur}) saturate(${glass.backdropSaturation});
      -webkit-backdrop-filter: blur(${glass.blur}) saturate(${glass.backdropSaturation});
      background: rgba(255, 255, 255, ${glass.opacity});
      border: 1px solid rgba(255, 255, 255, ${glass.borderOpacity});
      box-shadow: 0 8px 32px rgba(0, 0, 0, ${glass.shadowIntensity});
    }

    .glass-dark {
      backdrop-filter: blur(${glass.blur}) saturate(${glass.backdropSaturation});
      -webkit-backdrop-filter: blur(${glass.blur}) saturate(${glass.backdropSaturation});
      background: rgba(0, 0, 0, ${glass.opacity});
      border: 1px solid rgba(255, 255, 255, ${glass.borderOpacity});
      box-shadow: 0 8px 32px rgba(0, 0, 0, ${glass.shadowIntensity});
    }

    .glass-card {
      backdrop-filter: blur(${glass.blur});
      -webkit-backdrop-filter: blur(${glass.blur});
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, ${parseFloat(glass.opacity) + 0.1}) 0%,
        rgba(255, 255, 255, ${parseFloat(glass.opacity) - 0.1}) 100%
      );
      border: 1px solid rgba(255, 255, 255, ${glass.borderOpacity});
      box-shadow: 
        0 8px 32px rgba(0, 0, 0, ${glass.shadowIntensity}),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .glass-button {
      backdrop-filter: blur(${glass.blur});
      -webkit-backdrop-filter: blur(${glass.blur});
      background: rgba(255, 255, 255, ${parseFloat(glass.opacity) * 0.8});
      border: 1px solid rgba(255, 255, 255, ${glass.borderOpacity});
      transition: all 0.3s ease;
    }

    .glass-button:hover {
      background: rgba(255, 255, 255, ${parseFloat(glass.opacity) * 0.9});
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, ${parseFloat(glass.shadowIntensity) * 1.5});
    }
  `;
}
