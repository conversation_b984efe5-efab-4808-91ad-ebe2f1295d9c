import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Order, OrderItem } from '../../../types';
import { v4 as uuidv4 } from 'uuid';
import { AppThunk } from '../store';

interface OrdersState {
  orders: Order[];
  activeOrderId: string | null;
}

const initialState: OrdersState = {
  orders: [],
  activeOrderId: null,
};

export const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    createOrder: (state, action: PayloadAction<{ name: string }>) => {
      const newOrder: Order = {
        id: uuidv4(),
        name: action.payload.name,
        items: [],
        state: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      state.orders.push(newOrder);

      // Set as active order if no active order exists
      if (!state.activeOrderId) {
        state.activeOrderId = newOrder.id;
      }
    },

    setActiveOrder: (state, action: PayloadAction<string>) => {
      state.activeOrderId = action.payload;
    },

    updateOrderName: (state, action: PayloadAction<{ orderId: string; name: string }>) => {
      const { orderId, name } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order) {
        order.name = name;
        order.updatedAt = new Date().toISOString();
      }
    },

    addItemToOrder: (state, action: PayloadAction<{ orderId: string; item: OrderItem }>) => {
      const { orderId, item } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && order.state === 'draft') {
        // Check if item already exists
        const existingItemIndex = order.items.findIndex(i =>
          i.productId === item.productId &&
          i.selectedVariantId === item.selectedVariantId
        );

        if (existingItemIndex >= 0) {
          // Update quantity if item exists
          order.items[existingItemIndex].quantity += item.quantity;
        } else {
          // Add new item
          order.items.push(item);
        }

        order.updatedAt = new Date().toISOString();
      }
    },

    removeItemFromOrder: (state, action: PayloadAction<{ orderId: string; itemIndex: number }>) => {
      const { orderId, itemIndex } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && (order.state === 'draft' || order.state === 'submitted') && itemIndex >= 0 && itemIndex < order.items.length) {
        order.items.splice(itemIndex, 1);
        order.updatedAt = new Date().toISOString();
      }
    },

    updateItemQuantity: (state, action: PayloadAction<{ orderId: string; itemIndex: number; quantity: number }>) => {
      const { orderId, itemIndex, quantity } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && (order.state === 'draft' || order.state === 'submitted') && itemIndex >= 0 && itemIndex < order.items.length) {
        order.items[itemIndex].quantity = Math.max(1, quantity);
        order.updatedAt = new Date().toISOString();
      }
    },

    updateItemTargetPrice: (state, action: PayloadAction<{ orderId: string; itemIndex: number; targetPrice: number | undefined }>) => {
      const { orderId, itemIndex, targetPrice } = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && (order.state === 'draft' || order.state === 'submitted') && itemIndex >= 0 && itemIndex < order.items.length) {
        order.items[itemIndex].targetPrice = targetPrice;
        order.updatedAt = new Date().toISOString();
      }
    },

    clearOrder: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && order.state === 'draft') {
        order.items = [];
        order.updatedAt = new Date().toISOString();
      }
    },

    deleteOrder: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      const orderIndex = state.orders.findIndex(o => o.id === orderId);

      if (orderIndex >= 0 && state.orders[orderIndex].state === 'draft') {
        state.orders.splice(orderIndex, 1);

        // If deleted order was active, set another order as active
        if (state.activeOrderId === orderId) {
          state.activeOrderId = state.orders.length > 0 ? state.orders[0].id : null;
        }
      }
    },

    submitOrder: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && order.state === 'draft' && order.items.length > 0) {
        order.state = 'submitted';
        order.updatedAt = new Date().toISOString();
      }
    },

    confirmOrder: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      const order = state.orders.find(o => o.id === orderId);

      if (order && order.state === 'submitted') {
        order.state = 'confirmed';
        order.updatedAt = new Date().toISOString();
      }
    },

    // For backward compatibility with baskets
    createBasket: (state, action: PayloadAction<{ name: string }>) => {
      // Call createOrder with the same payload
      const newOrder: Order = {
        id: uuidv4(),
        name: action.payload.name,
        items: [],
        state: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      state.orders.push(newOrder);

      // Set as active order if no active order exists
      if (!state.activeOrderId) {
        state.activeOrderId = newOrder.id;
      }
    },

    setActiveBasket: (state, action: PayloadAction<string>) => {
      state.activeOrderId = action.payload;
    },

    addItemToBasket: (state, action: PayloadAction<{ basketId: string; item: OrderItem }>) => {
      const { basketId, item } = action.payload;
      const order = state.orders.find(o => o.id === basketId);

      if (order && order.state === 'draft') {
        // Check if item already exists
        const existingItemIndex = order.items.findIndex(i =>
          i.productId === item.productId &&
          i.selectedVariantId === item.selectedVariantId
        );

        if (existingItemIndex >= 0) {
          // Update quantity if item exists
          order.items[existingItemIndex].quantity += item.quantity;
        } else {
          // Add new item
          order.items.push(item);
        }

        order.updatedAt = new Date().toISOString();
      }
    },

    confirmBasket: (state, action: PayloadAction<string>) => {
      const basketId = action.payload;
      const order = state.orders.find(o => o.id === basketId);

      if (order && order.state === 'draft' && order.items.length > 0) {
        order.state = 'submitted';
        order.updatedAt = new Date().toISOString();
      }
    },
  },
});

export const {
  createOrder,
  setActiveOrder,
  updateOrderName,
  addItemToOrder,
  removeItemFromOrder,
  updateItemQuantity,
  updateItemTargetPrice,
  clearOrder,
  deleteOrder,
  submitOrder,
  confirmOrder,
  // Backward compatibility
  createBasket,
  setActiveBasket,
  addItemToBasket,
  confirmBasket,
} = ordersSlice.actions;

// Thunk to ensure a default order exists
export const ensureDefaultOrder = (): AppThunk => (dispatch, getState) => {
  const { orders } = getState().orders;

  // If no orders exist, create a default "My Cart" order
  if (orders.length === 0) {
    dispatch(createOrder({ name: 'My Cart' }));
  }
};

export default ordersSlice.reducer;
