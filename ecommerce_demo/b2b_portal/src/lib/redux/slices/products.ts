import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { FilterState, Product } from '../../../types';
import { fetchProducts } from '../../api/odoo';

interface ProductsState {
  products: Product[];
  filteredProducts: Product[];
  filters: FilterState;
  loading: boolean;
  error: string | null;
}

const initialState: ProductsState = {
  products: [],
  filteredProducts: [],
  filters: {
    categories: [],
    subcategories: [],
    brands: [],
    priceRange: {
      min: 0,
      max: 10000,
    },

    searchQuery: '',
  },
  loading: false,
  error: null,
};

// Async thunk for fetching products
export const getProducts = createAsyncThunk(
  'products/getProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchProducts();
      return response;
    } catch (error) {
      return rejectWithValue('Failed to fetch products');
    }
  }
);

// Helper function to apply filters
const applyFilters = (products: Product[], filters: FilterState): Product[] => {
  return products.filter(product => {
    // Filter by search query
    if (filters.searchQuery && !product.name.toLowerCase().includes(filters.searchQuery.toLowerCase())) {
      return false;
    }

    // Filter by main categories (Laptops, Phones, Electronics)
    if (filters.categories.length > 0 && !product.categories.some(cat => filters.categories.includes(cat))) {
      return false;
    }

    // Filter by subcategories
    if (filters.subcategories.length > 0 && !product.categories.some(cat => filters.subcategories.includes(cat))) {
      return false;
    }

    // Filter by brands
    if (filters.brands.length > 0 && !filters.brands.includes(product.brand || '')) {
      return false;
    }

    // Filter by price range
    if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) {
      return false;
    }



    return true;
  });
};

export const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.filters.searchQuery = action.payload;
      state.filteredProducts = applyFilters(state.products, state.filters);
    },

    setCategoryFilters: (state, action: PayloadAction<string[]>) => {
      state.filters.categories = action.payload;
      state.filteredProducts = applyFilters(state.products, state.filters);
    },

    setSubcategoryFilters: (state, action: PayloadAction<string[]>) => {
      state.filters.subcategories = action.payload;
      state.filteredProducts = applyFilters(state.products, state.filters);
    },

    setBrandFilters: (state, action: PayloadAction<string[]>) => {
      state.filters.brands = action.payload;
      state.filteredProducts = applyFilters(state.products, state.filters);
    },

    setPriceRange: (state, action: PayloadAction<{ min: number; max: number }>) => {
      state.filters.priceRange = action.payload;
      state.filteredProducts = applyFilters(state.products, state.filters);
    },



    clearFilters: (state) => {
      state.filters = {
        categories: [],
        subcategories: [],
        brands: [],
        priceRange: {
          min: 0,
          max: 10000,
        },
  
        searchQuery: '',
      };
      state.filteredProducts = state.products;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
        state.filteredProducts = applyFilters(action.payload, state.filters);
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSearchQuery,
  setCategoryFilters,
  setSubcategoryFilters,
  setBrandFilters,
  setPriceRange,
  setServiceOptionFilters,
  clearFilters
} = productsSlice.actions;

export default productsSlice.reducer;
