import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Basket, BasketItem } from '../../../types';
import { v4 as uuidv4 } from 'uuid';

interface BasketsState {
  baskets: Basket[];
  activeBasketId: string | null;
}

const initialState: BasketsState = {
  baskets: [],
  activeBasketId: null,
};

export const basketsSlice = createSlice({
  name: 'baskets',
  initialState,
  reducers: {
    createBasket: (state, action: PayloadAction<{ name: string }>) => {
      const newBasket: Basket = {
        id: uuidv4(),
        name: action.payload.name,
        items: [],
        state: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      state.baskets.push(newBasket);

      // Set as active basket if no active basket exists
      if (!state.activeBasketId) {
        state.activeBasketId = newBasket.id;
      }
    },

    setActiveBasket: (state, action: PayloadAction<string>) => {
      state.activeBasketId = action.payload;
    },

    addItemToBasket: (state, action: PayloadAction<{
      basketId: string,
      item: BasketItem
    }>) => {
      const { basketId, item } = action.payload;
      const basket = state.baskets.find(b => b.id === basketId);

      if (basket) {
        const existingItemIndex = basket.items.findIndex(
          i => i.productId === item.productId &&
               i.selectedVariantId === item.selectedVariantId
        );

        if (existingItemIndex >= 0) {
          // Update quantity if item already exists
          basket.items[existingItemIndex].quantity += item.quantity;
        } else {
          // Add new item
          basket.items.push(item);
        }

        basket.updatedAt = new Date().toISOString();
      }
    },

    removeItemFromBasket: (state, action: PayloadAction<{
      basketId: string,
      itemIndex: number
    }>) => {
      const { basketId, itemIndex } = action.payload;
      const basket = state.baskets.find(b => b.id === basketId);

      if (basket && itemIndex >= 0 && itemIndex < basket.items.length) {
        basket.items.splice(itemIndex, 1);
        basket.updatedAt = new Date().toISOString();
      }
    },

    updateItemQuantity: (state, action: PayloadAction<{
      basketId: string,
      itemIndex: number,
      quantity: number
    }>) => {
      const { basketId, itemIndex, quantity } = action.payload;
      const basket = state.baskets.find(b => b.id === basketId);

      if (basket && itemIndex >= 0 && itemIndex < basket.items.length) {
        basket.items[itemIndex].quantity = Math.max(1, quantity);
        basket.updatedAt = new Date().toISOString();
      }
    },

    confirmBasket: (state, action: PayloadAction<string>) => {
      const basketId = action.payload;
      const basket = state.baskets.find(b => b.id === basketId);

      if (basket) {
        basket.state = 'confirmed';
        basket.updatedAt = new Date().toISOString();
      }
    },

    clearBasket: (state, action: PayloadAction<string>) => {
      const basketId = action.payload;
      const basket = state.baskets.find(b => b.id === basketId);

      if (basket) {
        basket.items = [];
        basket.updatedAt = new Date().toISOString();
      }
    },

    deleteBasket: (state, action: PayloadAction<string>) => {
      const basketId = action.payload;
      state.baskets = state.baskets.filter(b => b.id !== basketId);

      // If the active basket was deleted, set a new active basket
      if (state.activeBasketId === basketId) {
        state.activeBasketId = state.baskets.length > 0 ? state.baskets[0].id : null;
      }
    },
  },
});

export const {
  createBasket,
  setActiveBasket,
  addItemToBasket,
  removeItemFromBasket,
  updateItemQuantity,
  confirmBasket,
  clearBasket,
  deleteBasket
} = basketsSlice.actions;

export default basketsSlice.reducer;
