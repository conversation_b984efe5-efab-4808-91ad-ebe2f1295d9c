import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import basketsReducer from './slices/baskets';
import ordersReducer from './slices/orders';
import productsReducer from './slices/products';
import wishlistReducer from './slices/wishlist';

// Define the store configuration
const createStore = () => {
  return configureStore({
    reducer: {
      baskets: basketsReducer, // Keep for backward compatibility
      orders: ordersReducer,
      products: productsReducer,
      wishlist: wishlistReducer,
    },
    // Enable Redux DevTools in development
    devTools: process.env.NODE_ENV !== 'production',
  });
};

// Create a store instance for the client side
let clientStore: ReturnType<typeof createStore> | undefined;

// Get or create the store
export const getStore = () => {
  // For SSR, always create a new store
  if (typeof window === 'undefined') {
    return createStore();
  }

  // Create the store once in the client
  if (!clientStore) {
    clientStore = createStore();
  }

  return clientStore;
};

// For convenience, also export a store instance
export const store = getStore();

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Define the AppThunk type for async actions
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
