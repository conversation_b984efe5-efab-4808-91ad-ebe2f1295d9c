import { CustomerTheme } from '../types/theme';

// Default Theme Configuration
// Generic B2B marketplace theme
export const defaultTheme: CustomerTheme = {
  customerId: 'default',
  themeName: 'Default B2B Theme',
  logoUrl: '/logos/default-logo.svg',
  faviconUrl: '/favicons/default-favicon.ico',
  
  colors: {
    light: {
      // Primary colors - neutral blue
      primary: '#3B82F6',           // Blue 500
      primaryHover: '#2563EB',      // Blue 600
      primaryActive: '#1D4ED8',     // Blue 700
      
      // Secondary colors
      secondary: '#6B7280',         // Gray 500
      secondaryHover: '#4B5563',    // Gray 600
      
      // Accent colors
      accent: '#10B981',            // Emerald 500
      accentHover: '#059669',       // Emerald 600
      
      // Background colors
      background: '#FFFFFF',        // White
      backgroundSecondary: '#F9FAFB', // Gray 50
      backgroundTertiary: '#F3F4F6', // Gray 100
      
      // Foreground colors
      foreground: '#111827',        // Gray 900
      foregroundSecondary: '#374151', // Gray 700
      foregroundMuted: '#6B7280',   // Gray 500
      
      // Border colors
      border: '#E5E7EB',            // Gray 200
      borderHover: '#D1D5DB',       // Gray 300
      
      // Status colors
      success: '#10B981',           // Emerald 500
      warning: '#F59E0B',           // Amber 500
      error: '#EF4444',             // Red 500
      info: '#3B82F6',              // Blue 500
    },
    
    dark: {
      // Primary colors adjusted for dark mode
      primary: '#60A5FA',           // Blue 400
      primaryHover: '#93C5FD',      // Blue 300
      primaryActive: '#DBEAFE',     // Blue 100
      
      // Secondary colors
      secondary: '#9CA3AF',         // Gray 400
      secondaryHover: '#D1D5DB',    // Gray 300
      
      // Accent colors
      accent: '#34D399',            // Emerald 400
      accentHover: '#6EE7B7',       // Emerald 300
      
      // Background colors
      background: '#111827',        // Gray 900
      backgroundSecondary: '#1F2937', // Gray 800
      backgroundTertiary: '#374151', // Gray 700
      
      // Foreground colors
      foreground: '#F9FAFB',        // Gray 50
      foregroundSecondary: '#E5E7EB', // Gray 200
      foregroundMuted: '#9CA3AF',   // Gray 400
      
      // Border colors
      border: '#374151',            // Gray 700
      borderHover: '#4B5563',       // Gray 600
      
      // Status colors
      success: '#34D399',           // Emerald 400
      warning: '#FBBF24',           // Amber 400
      error: '#F87171',             // Red 400
      info: '#60A5FA',              // Blue 400
    },
  },
  
  typography: {
    fontPrimary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    fontSecondary: "'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace",
    fontArabic: "'Noto Sans Arabic', 'Tajawal', 'Cairo', sans-serif",
    
    fontSizes: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
    },
    
    fontWeights: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  
  layout: {
    borderRadius: {
      sm: '0.25rem',    // 4px
      md: '0.5rem',     // 8px
      lg: '0.75rem',    // 12px
      xl: '1rem',       // 16px
    },
    
    spacing: {
      xs: '0.5rem',     // 8px
      sm: '1rem',       // 16px
      md: '1.5rem',     // 24px
      lg: '2rem',       // 32px
      xl: '3rem',       // 48px
    },
    
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    },
  },
  
  glassmorphism: {
    light: {
      enabled: false,
      blur: '12px',
      opacity: '0.9',
      borderOpacity: '0.1',
      shadowIntensity: '0.05',
      backdropSaturation: '1.2',
    },
    
    dark: {
      enabled: false,
      blur: '16px',
      opacity: '0.8',
      borderOpacity: '0.1',
      shadowIntensity: '0.1',
      backdropSaturation: '1.1',
    },
  },
  
  customCSS: `
    /* Default theme customizations */
    .default-gradient {
      background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
    }
    
    .default-accent-gradient {
      background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    }
  `,
};
