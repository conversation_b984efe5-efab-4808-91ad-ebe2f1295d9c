import { CustomerTheme } from '../types/theme';

// KAUST University Theme Configuration
// Based on official KAUST branding and website colors
export const kaustTheme: CustomerTheme = {
  customerId: 'kaust',
  themeName: 'KAUST University',
  logoUrl: '/logos/kaust-logo.svg',
  faviconUrl: '/favicons/kaust-favicon.ico',
  
  colors: {
    light: {
      // Primary colors from KAUST branding
      primary: '#0066CC',           // KAUST Blue
      primaryHover: '#0052A3',      // Darker blue on hover
      primaryActive: '#003D7A',     // Even darker for active state
      
      // Secondary colors
      secondary: '#004499',         // Dark blue
      secondaryHover: '#003366',    // Darker secondary
      
      // Accent colors
      accent: '#FFB800',            // KAUST Gold
      accentHover: '#E6A600',       // Darker gold
      
      // Background colors
      background: '#FFFFFF',        // Pure white
      backgroundSecondary: '#F8FAFC', // Very light gray
      backgroundTertiary: '#F1F5F9', // Light gray
      
      // Foreground colors
      foreground: '#1A1A1A',        // Almost black
      foregroundSecondary: '#4A5568', // Dark gray
      foregroundMuted: '#718096',   // Medium gray
      
      // Border colors
      border: '#E2E8F0',            // Light border
      borderHover: '#CBD5E0',       // Darker border on hover
      
      // Status colors
      success: '#10B981',           // Green
      warning: '#F59E0B',           // Orange
      error: '#EF4444',             // Red
      info: '#3B82F6',              // Blue
    },
    
    dark: {
      // Primary colors adjusted for dark mode
      primary: '#3B82F6',           // Lighter blue for dark
      primaryHover: '#60A5FA',      // Even lighter on hover
      primaryActive: '#93C5FD',     // Lightest for active
      
      // Secondary colors
      secondary: '#1E40AF',         // Dark blue adjusted
      secondaryHover: '#3730A3',    // Lighter secondary
      
      // Accent colors
      accent: '#FCD34D',            // Gold adjusted for dark
      accentHover: '#FDE68A',       // Lighter gold
      
      // Background colors
      background: '#0F172A',        // Very dark blue-gray
      backgroundSecondary: '#1E293B', // Dark gray
      backgroundTertiary: '#334155', // Medium dark gray
      
      // Foreground colors
      foreground: '#F8FAFC',        // Almost white
      foregroundSecondary: '#CBD5E0', // Light gray
      foregroundMuted: '#94A3B8',   // Medium light gray
      
      // Border colors
      border: '#334155',            // Dark border
      borderHover: '#475569',       // Lighter border on hover
      
      // Status colors
      success: '#059669',           // Darker green
      warning: '#D97706',           // Darker orange
      error: '#DC2626',             // Darker red
      info: '#2563EB',              // Darker blue
    },
  },
  
  typography: {
    fontPrimary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    fontSecondary: "'Roboto Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace",
    fontArabic: "'Noto Sans Arabic', 'Tajawal', 'Cairo', sans-serif",
    
    fontSizes: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
    },
    
    fontWeights: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  
  layout: {
    borderRadius: {
      sm: '0.25rem',    // 4px
      md: '0.5rem',     // 8px
      lg: '0.75rem',    // 12px
      xl: '1rem',       // 16px
    },
    
    spacing: {
      xs: '0.5rem',     // 8px
      sm: '1rem',       // 16px
      md: '1.5rem',     // 24px
      lg: '2rem',       // 32px
      xl: '3rem',       // 48px
    },
    
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    },
  },
  
  glassmorphism: {
    light: {
      enabled: true,
      blur: '16px',
      opacity: '0.8',
      borderOpacity: '0.2',
      shadowIntensity: '0.1',
      backdropSaturation: '1.8',
    },
    
    dark: {
      enabled: true,
      blur: '20px',
      opacity: '0.7',
      borderOpacity: '0.15',
      shadowIntensity: '0.2',
      backdropSaturation: '1.5',
    },
  },
  
  customCSS: `
    /* KAUST specific customizations */
    .kaust-gradient {
      background: linear-gradient(135deg, #0066CC 0%, #004499 100%);
    }
    
    .kaust-accent-gradient {
      background: linear-gradient(135deg, #FFB800 0%, #E6A600 100%);
    }
    
    /* Custom scrollbar for KAUST theme */
    .kaust-scrollbar::-webkit-scrollbar {
      width: 8px;
    }
    
    .kaust-scrollbar::-webkit-scrollbar-track {
      background: var(--color-background-secondary);
      border-radius: 4px;
    }
    
    .kaust-scrollbar::-webkit-scrollbar-thumb {
      background: var(--color-primary);
      border-radius: 4px;
    }
    
    .kaust-scrollbar::-webkit-scrollbar-thumb:hover {
      background: var(--color-primary-hover);
    }
  `,
};
