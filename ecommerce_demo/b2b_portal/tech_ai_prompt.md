# AI Prompt: Build a Next.js B2B E-commerce Portal from Scratch

## Project Overview

Create a modern B2B e-commerce procurement portal using Next.js that enables business customers (specifically KAUST) to order from vendors (Zenith Arabia) under predefined contractual agreements. The portal should support the complete procurement lifecycle, from browsing products to order negotiation and tracking.

## Business Requirements

### Core Business Objectives

1. Enable KAUST to browse a curated, contract-bound product catalog
2. Implement customer-specific pricing and contract enforcement
3. Digitize purchase order and negotiation workflows
4. Provide real-time order tracking and status updates
5. Support multi-language interface with Arabic and English

### Key Stakeholders

- **Zenith Arabia**: Vendor/supplier providing products, pricing, and fulfillment
- **KAUST**: End customer (university departments) managing procurement
- **Merchandiser**: Manages product catalog and SKUs
- **Account Manager**: Manages customer price lists and contracts
- **KAUST Users**: Purchasing representatives creating and managing orders

### Key Features

1. **Product Catalog Management**
   - Multi-SKU product setup with variants
   - Dynamic attributes and filtering
   - Product descriptions, availability, and accessories

2. **Customer-Specific Pricing**
   - Multiple price lists per product
   - Contract-level price list linking
   - Dynamic discounts and pricing rules

3. **Frame Contract Management**
   - Contract validity, SLAs, and payment terms
   - Links to pricing and PO workflows

4. **Client Portal**
   - Secure access for KAUST users
   - Role-based functionality
   - Multi-PO handling (shopping carts)
   - Contract history and tracking

5. **Purchase Order Lifecycle**
   - Create, edit, copy, or reorder POs
   - PO negotiation workflows
   - Real-time tracking and dashboard

## Technical Requirements

### Technology Stack

- **Frontend**: Next.js 15+ with App Router
- **Language**: TypeScript
- **State Management**: Redux + Context API
- **Styling**: Tailwind CSS
- **Package Manager**: Bun
- **Authentication**: OAuth2/SAML (simulate Nafath integration)
- **API**: RESTful API with mock backend

### Project Structure

Follow this structure for the application:

```
/
├── src/
│   ├── app/                  # Next.js App Router pages
│   │   ├── (portal)/         # Portal-specific routes
│   │   ├── layout.tsx        # Root layout
│   │   └── page.tsx          # Root page
│   ├── components/           # React components
│   │   ├── ui/               # Base UI components
│   │   └── [feature]/        # Feature-specific components
│   ├── contexts/             # React contexts
│   ├── lib/                  # Core utilities
│   │   ├── api/              # API integration
│   │   ├── redux/            # Redux store
│   │   └── i18n/             # Internationalization
│   ├── types/                # TypeScript types
│   └── utils/                # Utility functions
├── public/                   # Static assets
│   ├── locales/              # Translation files
│   └── images/               # Static images
├── locales/                  # i18n resources
└── config/                   # Configuration files
```

### Core Components to Implement

1. **Layout Components**
   - `PortalLayoutContent`: Main layout wrapper
   - `Providers`: Global provider wrapper

2. **Product Components**
   - `ProductCard`: Product display card
   - `ProductDetail`: Detailed product view
   - `FiltersSidebar`: Product filtering

3. **Order Components**
   - `OrderSelector`: Order selection and management
   - `BasketSelector`: Shopping cart management
   - `OrderChatter`: Order communication

4. **UI Components**
   - `LanguageToggle`: Language switching
   - `ThemeToggle`: Theme switching
   - `SearchAutocomplete`: Product search

### State Management

Implement Redux store with the following slices:

1. **Orders Slice**
   - Manage multiple orders/quotes
   - Track active order
   - Handle order state transitions

2. **Products Slice**
   - Product catalog
   - Filtering and search
   - Product details

3. **User Slice**
   - User preferences
   - Authentication state

### Data Models

#### Product Model

```typescript
interface Product {
  id: number;
  name: string;
  description: string;
  categories: string[];
  variants: ProductVariant[];
  price: number;
  discountPercentage?: number;
  discountedPrice?: number;
  service_options: string[];
  image: string;
  images: string[];
  stock: number;
  sku: string;
  attributes: ProductAttribute[];
  brand?: string;
  model?: string;
}

interface ProductVariant {
  id: string;
  name: string;
  color?: string;
  colorName?: string;
  price?: number;
  attributes: ProductAttribute[];
  image?: string;
  stock: number;
}

interface ProductAttribute {
  name: string;
  value: string;
}
```

#### Order Model

```typescript
interface Order {
  id: string;
  name: string;
  items: OrderItem[];
  state: 'draft' | 'submitted' | 'under_review' | 'revised' | 'approved' | 'rejected' | 'expired' | 'ordered';
  createdAt: string;
  updatedAt: string;
}

interface OrderItem {
  productId: number;
  quantity: number;
  selectedVariantId?: string;
  selectedServiceOptions?: string[];
  targetPrice?: number; // For negotiation
}
```

## Order State Machine

Implement the following order state machine:

```mermaid
stateDiagram-v2
    [*] --> draft
    draft --> submitted: Buyer submits
    submitted --> under_review: Auto-transition
    under_review --> revised: Counterparty requests changes
    revised --> under_review: Resubmitted after edit
    under_review --> approved: Terms accepted
    under_review --> rejected: Declined
    approved --> ordered: PO created
    approved --> expired: Not converted in time
    rejected --> [*]
    expired --> [*]
    ordered --> [*]
```

### State Descriptions

| State ID | Description |
|----------|-------------|
| `draft` | Buyer editing quotation/cart |
| `submitted` | Sent to seller for review |
| `under_review` | Counterparty evaluating |
| `revised` | Updated due to negotiation |
| `approved` | Agreement reached |
| `ordered` | Turned into official order |
| `expired` | Validity period lapsed |
| `rejected` | Declined by either side |

## Internationalization

Implement a robust internationalization system with:

1. Language switching between English and Arabic
2. RTL support for Arabic
3. Translation files for all UI elements
4. Context-based language management

## API Integration

Create mock API endpoints for:

1. Product catalog and search
2. Order management
3. User authentication
4. Contract and pricing

## Implementation Steps

1. **Project Setup**
   - Initialize Next.js project with TypeScript
   - Configure Tailwind CSS
   - Set up ESLint and other tools

2. **Core Architecture**
   - Implement project structure
   - Set up Redux store
   - Create context providers

3. **UI Components**
   - Build layout components
   - Implement base UI components
   - Create feature components

4. **Feature Implementation**
   - Product catalog and search
   - Order management
   - Internationalization
   - Theme switching

5. **State Machine**
   - Implement order state transitions
   - Create workflow automation
   - Build UI for state visualization

6. **Mock Backend**
   - Create mock API endpoints
   - Implement data persistence
   - Simulate backend processes

## Deliverables

1. Complete Next.js application with TypeScript
2. Responsive UI with Tailwind CSS
3. Functional product catalog and search
4. Complete order management system
5. Internationalization with Arabic support
6. Mock API integration
7. Documentation of components and architecture

## Additional Considerations

1. **Performance Optimization**
   - Implement code splitting
   - Optimize image loading
   - Use efficient state management

2. **Accessibility**
   - Ensure WCAG compliance
   - Support keyboard navigation
   - Implement proper ARIA attributes

3. **Security**
   - Implement proper authentication
   - Secure API calls
   - Validate user inputs

4. **Testing**
   - Unit tests for components
   - Integration tests for workflows
   - E2E tests for critical paths

## Example User Flows

### Order Creation Flow

1. User browses product catalog
2. User adds products to cart/draft order
3. User reviews order and submits for approval
4. Vendor reviews and approves/rejects/revises
5. User accepts changes and converts to order

### Order Negotiation Flow

1. User creates order with target prices
2. Vendor reviews and revises pricing
3. User receives notification of changes
4. User reviews changes and accepts/rejects
5. If accepted, order moves to approved state

## Conclusion

This B2B e-commerce portal should provide a seamless procurement experience for KAUST users while enabling Zenith Arabia to manage their product catalog, pricing, and order fulfillment efficiently. The implementation should focus on scalability, performance, and user experience while adhering to B2B procurement best practices.